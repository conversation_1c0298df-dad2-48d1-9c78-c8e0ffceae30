import React, { useState, useEffect, useCallback } from "react";
import Table_ from '@/components/pos/Table_default1';
import { fetchData, updateData } from '@/app/utils/pos';
import { toaster } from "@/components/ui/toaster";
import { formatDate } from '@/app/utils/actions';
import { 
  Table, Box, HStack, VStack, Text, Stack, Button, Badge, 
  Select, Avatar, useDisclosure, Modal, ModalOverlay, 
  ModalContent, ModalHeader, ModalFooter, ModalBody, 
  ModalCloseButton, FormControl, FormLabel, Input, Image,
  SimpleGrid, Divider, Icon, Tooltip
} from "@chakra-ui/react";
import { User } from '@/lib/features/users';
import { FiTruck, FiPackage, FiMapPin, FiPhone, FiMail, FiCheck, FiX } from "react-icons/fi";
import { Transaction } from "@/app/utils/definitions";

interface OrderItem {
  clientId: string;
  product: string;
  countInStock: number;
  name: string;
  slug: string;
  category: string;
  price: number;
  quantity: number;
  image: string;
  size?: string;
  color?: string;
}

interface DeliveryAddress {
  fullName: string;
  street: string;
  city: string;
  postalCode: string;
  county: string;
  phone: string;
  country: string;
}

interface Order {
  _id: string;
  items: OrderItem[];
  deliveryAddress: DeliveryAddress;
  expectedDeliveryDate: string;
  deliveryDateIndex: number;
  paymentMethod: string;
  itemsPrice: number;
  deliveryPrice: number;
  taxPrice: number;
  totalPrice: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  createdAt: string;
  updatedAt: string;
  deliveryPerson?: string;
  deliveryNotes?: string;
}

const Orders = ({ status, mode, user, query, checkTransaction }: { 
  status: number; 
  mode: string; 
  user: User; 
  query: string; 
  checkTransaction: any 
}) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [deliveryPersons, setDeliveryPersons] = useState<{id: string, name: string}[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedDeliveryPerson, setSelectedDeliveryPerson] = useState('');
  const [deliveryNotes, setDeliveryNotes] = useState('');
  
  const { isOpen, onOpen, onClose } = useDisclosure();
  const shop_id = user?.currentshop;
  const username = user?.username;

  const loadOrders = useCallback(async () => {
    setIsLoading(true);
    const queryParams = { query,shop_id: shop_id,trans_status: status,username: username};
    try {
        const TransactionResponse = await fetchData<Transaction>('transaction_',currentPage,pageSize,queryParams);
        const { data: TransactionData, metadata: TransactionMetadata } = TransactionResponse;
        setTransaction(TransactionData);
        setTotalItems(TransactionMetadata.total);
    } catch (error) {
        toaster.error({
            title: 'Failed to load data.',
            description:'An unexpected error occurred.'+error,
        });
    } finally {
        setIsLoading(false);
    }
  }, [currentPage, pageSize, query, shop_id, status, username]);

  useEffect(() => {
    loadOrders();
    setDeliveryPersons([
      { id: '1', name: 'John Doe' },
      { id: '2', name: 'Jane Smith' },
      { id: '3', name: 'Mike Johnson' }
    ]);
  }, [loadOrders]);

  const handlePageChange = (newPage: any) => {
    setCurrentPage(newPage.page);
  };

  const handlePageSizeChange = (newPageSize: any) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  const toggleRow = (orderId: string) => {
    setExpandedRows(prev => ({
      ...prev,
      [orderId]: !prev[orderId],
    }));
  };

  const handleAllocateOrder = (order: Order) => {
    setSelectedOrder(order);
    setSelectedDeliveryPerson(order.deliveryPerson || '');
    setDeliveryNotes(order.deliveryNotes || '');
    onOpen();
  };

  const handleAssignDelivery = async () => {
    if (!selectedOrder || !selectedDeliveryPerson) return;

    setLoading(true);
    try {
      // Replace with actual API call
      // await updateData('orders', selectedOrder._id, {
      //   ...selectedOrder,
      //   deliveryPerson: selectedDeliveryPerson,
      //   deliveryNotes,
      //   status: 'processing',
      //   updatedAt: new Date().toISOString()
      // });
      
      // Update local state
      setOrders(prev => 
        prev.map(order => 
          order._id === selectedOrder._id 
            ? { 
                ...order, 
                deliveryPerson: selectedDeliveryPerson,
                deliveryNotes,
                status: 'processing',
                updatedAt: new Date().toISOString()
              } 
            : order
        )
      );
      
      toaster.success({
        title: 'Order Assigned',
        description: 'The order has been assigned for delivery.',
      });
      
      onClose();
    } catch (error) {
      toaster.error({
        title: 'Assignment Failed',
        description: 'Failed to assign order for delivery.',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; label: string }> = {
      pending: { color: 'yellow', label: 'Pending' },
      processing: { color: 'blue', label: 'Processing' },
      shipped: { color: 'teal', label: 'Shipped' },
      delivered: { color: 'green', label: 'Delivered' },
      cancelled: { color: 'red', label: 'Cancelled' },
    };

    const config = statusConfig[status] || { color: 'gray', label: 'Unknown' };
    return (
      <Badge colorScheme={config.color} px={2} py={1} borderRadius="md">
        {config.label}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; label: string }> = {
      pending: { color: 'yellow', label: 'Pending' },
      paid: { color: 'green', label: 'Paid' },
      failed: { color: 'red', label: 'Failed' },
      refunded: { color: 'purple', label: 'Refunded' },
    };

    const config = statusConfig[status] || { color: 'gray', label: 'Unknown' };
    return (
      <Badge colorScheme={config.color} px={2} py={1} borderRadius="md">
        {config.label}
      </Badge>
    );
  };

  const columns = [
    { 
      label: 'Order ID', 
      key: '_id', 
      align: 'left',
      render: (id: string) => `#${id.substring(0, 8).toUpperCase()}`
    },
    { 
      label: 'Customer', 
      key: 'deliveryAddress', 
      align: 'left',
      render: (address: DeliveryAddress) => address?.fullName || 'N/A'
    },
    { 
      label: 'Items', 
      key: 'items', 
      align: 'center',
      render: (items: OrderItem[]) => items?.length || 0
    },
    { 
      label: 'Amount', 
      key: 'totalPrice', 
      align: 'right',
      render: (price: number) => `KSh ${price?.toLocaleString() || '0'}`
    },
    { 
      label: 'Status', 
      key: 'status', 
      align: 'center',
      render: (status: string) => getStatusBadge(status)
    },
    { 
      label: 'Payment', 
      key: 'paymentStatus', 
      align: 'center',
      render: (status: string) => getPaymentStatusBadge(status)
    },
    { 
      label: 'Date', 
      key: 'createdAt', 
      align: 'center',
      render: (date: string) => formatDate(date)
    }
  ];

  const getActions = (item: Order) => [
    {
      label: 'View',
      colorScheme: 'blue',
      onClick: () => toggleRow(item._id),
      icon: <FiPackage />
    },
    {
      label: 'Allocate',
      colorScheme: 'green',
      onClick: () => handleAllocateOrder(item),
      icon: <FiTruck />,
      isDisabled: item.status !== 'pending'
    }
  ];

  const renderRowDetails = (order: Order) => (
    <VStack align="stretch" gap={4} p={4} bg="gray.50" borderRadius="md" mt={2}>
      {/* Order Summary */}
      <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
        <Box>
          <Text fontWeight="bold" mb={2}>Order Summary</Text>
          <VStack align="start" gap={1}>
            <Text><strong>Order ID:</strong> #{order._id.substring(0, 8).toUpperCase()}</Text>
            <Text><strong>Order Date:</strong> {formatDate(order.createdAt)}</Text>
            <Text><strong>Status:</strong> {getStatusBadge(order.status)}</Text>
            <Text><strong>Payment:</strong> {getPaymentStatusBadge(order.paymentStatus)}</Text>
            <Text><strong>Payment Method:</strong> {order.paymentMethod}</Text>
          </VStack>
        </Box>
        
        <Box>
          <Text fontWeight="bold" mb={2}>Delivery Information</Text>
          <VStack align="start" gap={1}>
            <HStack>
              <Icon as={FiMapPin} />
              <Text>
                {order.deliveryAddress.street}, {order.deliveryAddress.city}, {order.deliveryAddress.country}
              </Text>
            </HStack>
            <HStack>
              <Icon as={FiPhone} />
              <Text>{order.deliveryAddress.phone}</Text>
            </HStack>
            <Text><strong>Expected Delivery:</strong> {formatDate(order.expectedDeliveryDate)}</Text>
            {order.deliveryPerson && (
              <Text><strong>Delivery Person:</strong> {order.deliveryPerson}</Text>
            )}
          </VStack>
        </Box>
      </SimpleGrid>

      <Divider />
      <Box>
        <Text fontWeight="bold" mb={2}>Order Items</Text>
        <Table.Root size="sm" stickyHeader>
                <Table.Header>
                    <Table.Row>
                    <Table.ColumnHeader>Item</Table.ColumnHeader>
                    <Table.ColumnHeader >Price</Table.ColumnHeader>
                    <Table.ColumnHeader >Qty</Table.ColumnHeader>
                    <Table.ColumnHeader >Total</Table.ColumnHeader>
                    </Table.Row>
                </Table.Header>
                            <Table.Body>
                {order.receipts.map((receipt) => (
                <Table.Row key={receipt.receipt_id}>
                    <Table.Cell>{receipt.receipt_item}</Table.Cell>
                    <Table.Cell >KSh {receipt.receipt_price?.toLocaleString() || '0'}</Table.Cell>
                    <Table.Cell >{receipt.receipt_quantity}</Table.Cell>
                    <Table.Cell >KSh {receipt.receipt_total?.toLocaleString() || '0'}</Table.Cell>
                </Table.Row>
                ))}
            </Table.Body>
            <Table.Footer>
                <Table.Row>
                <Table.Cell colSpan={3} textAlign="right">Subtotal</Table.Cell>
                <Table.Cell >KSh {item.subtotal?.toLocaleString() || '0'}</Table.Cell>
                </Table.Row>
                <Table.Row>
                <Table.Cell colSpan={3} textAlign="right">Delivery</Table.Cell>
                <Table.Cell >KSh {item.deliveryPrice?.toLocaleString() || '0'}</Table.Cell>
                </Table.Row>
                <Table.Row>
                <Table.Cell colSpan={3} textAlign="right">Tax</Table.Cell>
                <Table.Cell >KSh {item.taxPrice?.toLocaleString() || '0'}</Table.Cell>
                </Table.Row>
                <Table.Row>
                <Table.Cell colSpan={3} textAlign="right" fontWeight="bold">Total</Table.Cell>
                <Table.Cell  fontWeight="bold">KSh {item.totalPrice?.toLocaleString() || '0'}</Table.Cell>
                </Table.Row>
            </Table.Footer>
            </Table.Root>
      </Box>

      {/* Order Actions */}
      <HStack gap={4} justify="flex-end" mt={4}>
        <Button 
          leftIcon={<FiCheck />} 
          colorScheme="green" 
          size="sm"
          isDisabled={order.status === 'delivered'}
          onClick={() => {
            // Handle mark as delivered
            const updatedOrder = { ...order, status: 'delivered', updatedAt: new Date().toISOString() };
            setOrders(prev => 
              prev.map(o => o._id === order._id ? updatedOrder : o)
            );
          }}
        >
          Mark as Delivered
        </Button>
      </HStack>
    </VStack>
  );

  return (
    <>
      <Table_
        columns={columns}
        data={orders.map(order => ({ 
          ...order, 
          isExpanded: expandedRows[order._id] || false 
        }))}
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        handlePageChange={handlePageChange}
        handlePageSizeChange={handlePageSizeChange}
        getActions={getActions}
        isLoading={isLoading}
        width="100%"
        height="100%"
        mode={mode}
        renderRowDetails={renderRowDetails}
      />

      {/* Delivery Assignment Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Assign Delivery</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedOrder && (
              <VStack gap={4} align="stretch">
                <Box>
                  <Text fontWeight="bold">Order #{selectedOrder._id.substring(0, 8).toUpperCase()}</Text>
                  <Text>Customer: {selectedOrder.deliveryAddress.fullName}</Text>
                  <Text>Delivery to: {selectedOrder.deliveryAddress.city}, {selectedOrder.deliveryAddress.country}</Text>
                </Box>

                <FormControl isRequired>
                  <FormLabel>Select Delivery Person</FormLabel>
                  <Select
                    placeholder="Select delivery person"
                    value={selectedDeliveryPerson}
                    onChange={(e) => setSelectedDeliveryPerson(e.target.value)}
                  >
                    {deliveryPersons.map(person => (
                      <option key={person.id} value={person.name}>
                        {person.name}
                      </option>
                    ))}
                  </Select>
                </FormControl>

                <FormControl>
                  <FormLabel>Delivery Notes</FormLabel>
                  <Input
                    as="textarea"
                    rows={3}
                    value={deliveryNotes}
                    onChange={(e) => setDeliveryNotes(e.target.value)}
                    placeholder="Add any special delivery instructions"
                  />
                </FormControl>
              </VStack>
            )}
          </ModalBody>

          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button 
              colorScheme="blue" 
              onClick={handleAssignDelivery}
              isLoading={loading}
              isDisabled={!selectedDeliveryPerson}
            >
              Assign Delivery
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default Orders;

function setTransaction(TransactionData: any) {
    throw new Error("Function not implemented.");
}
