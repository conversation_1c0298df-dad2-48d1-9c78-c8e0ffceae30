"use client"
import React, { useState, useEffect } from 'react';
import {
  Skeleton, Box, VStack, HStack, Text, Button,
  Card, Heading, SimpleGrid, Separator,
  Input, Tabs, Textarea, Center, Table
} from '@chakra-ui/react';
import TabTable from '@/components/pos/pending'
import { formatCurrency } from '@/app/utils/actions';
import { fetchData2, insertData } from '@/app/utils/pos';
import { useAppSelector } from '@/lib/hooks';
import { toaster } from "@/components/ui/toaster";
import { Field } from "@/components/ui/field";
import { NativeSelectRoot, NativeSelectField } from "@/components/ui/native-select";
import { StatRoot, StatLabel, StatValueText, StatHelpText } from "@/components/ui/stat";
import { FiDollarSign, FiTrendingUp, FiTrendingDown, FiCreditCard } from 'react-icons/fi';
const FinancialReports = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [timeFrame, setTimeFrame] = useState('monthly');
  const [financialData, setFinancialData] = useState<any>(null);
  const [liabilities, setLiabilities] = useState<any[]>([]);
  const [expenses, setExpenses] = useState<any[]>([]);
  const [moneySummary, setMoneySummary] = useState<any>(null);
  const [withdrawalAmount, setWithdrawalAmount] = useState('');
const [isLoading ,setisLoading] = useState(false);
  // Form states
  const [showLiabilityForm, setShowLiabilityForm] = useState(false);
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [editingLiability, setEditingLiability] = useState<any>(null);
  const [newLiability, setNewLiability] = useState({
    name: '',
    amount: '',
    dueDate: '',
    description: ''
  });

  // Form states for API structure
  const [newExpense, setNewExpense] = useState({
    trans_total: '',
    trans_description: '',
    expense_category: '',
    expense_details: ''
  });
  const [newIncome, setNewIncome] = useState({
    trans_total: '',
    trans_description: '',
    income_source: '',
    income_details: ''
  });

  // Dialog states for modals
  const [showIncomeDialog, setShowIncomeDialog] = useState(false);
  const [showWithdrawalDialog, setShowWithdrawalDialog] = useState(false);
  const [showLiabilityDialog, setShowLiabilityDialog] = useState(false);
  const[seachquery, setSearchQuery] =useState('');
  // Data states for lists
  const [expensesList, setExpensesList] = useState([]);
  const [liabilitiesList, setLiabilitiesList] = useState([]);
  const [incomesList, setIncomesList] = useState([]);
  const [editingExpense, setEditingExpense] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const username = user?.username;

  // Helper function to parse concatenated string values
  const parseFinancialValue = (value: any): number => {
    if (typeof value == 'number') return value;
    if (typeof value == 'string') {
      // Handle concatenated strings like "15680119.00011200688.00"
      const cleanValue = value.replace(/[^\d.-]/g, '');
      return parseFloat(cleanValue) || 0;
    }
    return 0;
  };

  // Helper function to calculate total revenue properly
  const calculateTotalRevenue = (revenue: any): number => {
    if (!revenue) return 0;
    const normalSales = parseFloat(revenue.normal_sales || 0);
    const orderSales = parseFloat(revenue.order_sales || 0);
    const supplySales = parseFloat(revenue.supply_sales || 0);
    return normalSales + orderSales + supplySales;
  };

  // Helper function to calculate total COGS properly
  const calculateTotalCOGS = (cogs: any): number => {
    if (!cogs) return 0;
    const salesCogs = parseFloat(cogs.sales_cogs || 0);
    const supplyCogs = parseFloat(cogs.supply_cogs || 0);
    return salesCogs + supplyCogs;
  };

  // Helper function to calculate gross profit
  const calculateGrossProfit = (revenue: any, cogs: any): number => {
    return calculateTotalRevenue(revenue) - calculateTotalCOGS(cogs);
  };

  // Fetch financial reports data
  const fetchFinancialData = async () => {
    if (!shop_id) return;

    try {
      setLoading(true);

      // Fetch financial reports
      const financialResponse = await fetchData2('financial-reports', undefined, undefined, {
        shop_id,
        timeFrame
      });

      // Fetch money summary
      const moneyResponse = await fetchData2('money-summary', undefined, undefined, {
        shop_id,
        timeFrame
      });

      // Fetch reports for additional data
      const reportsResponse = await fetchData2('reports', undefined, undefined, {
        shop_id,
        timeFrame: 'daily',
        consolidated: true,
        page: 1,
        pageSize: 10
      });


      setFinancialData(financialResponse);
      setMoneySummary(moneyResponse.data);
      if (reportsResponse?.data && typeof reportsResponse.data == 'object' && 'summaryStats' in reportsResponse.data) {
        const data = reportsResponse.data as any;
        setExpenses(data.summaryStats?.totalExpenses || 0);
      }

    } catch (error) {
      console.error('Error fetching financial data:', error);
      toaster.create({
        title: 'Error',
        description: 'Failed to fetch financial data',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };


  // Handle liability creation
  const handleCreateLiability = async () => {
    if (!newLiability.name || !newLiability.amount || !shop_id) return;

    try {
      setLoading(true);

      if (editingLiability) {
        // Update existing liability
        await insertData('update-liability', {
          id: editingLiability.id,
          shop_id,
          name: newLiability.name,
          amount: parseFloat(newLiability.amount),
          dueDate: newLiability.dueDate,
          description: newLiability.description,
          status: 1,
          username: username
        });

        toaster.create({
          title: 'Success',
          description: 'Liability updated successfully',
          type: 'success'
        });
      } else {
        // Create new liability
        await insertData('liabilities', {
          shop_id,
          name: newLiability.name,
          amount: parseFloat(newLiability.amount),
          dueDate: newLiability.dueDate,
          description: newLiability.description,
          status: 1,
          username: username
        });

        toaster.create({
          title: 'Success',
          description: 'Liability added successfully',
          type: 'success'
        });
      }

      setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
      setEditingLiability(null);
      setShowLiabilityForm(false);
      setShowLiabilityDialog(false);
      fetchFinancialData();
      fetchLiabilitiesList();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: editingLiability ? 'Failed to update liability' : 'Failed to add liability',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle liability clearing/payment
  const handleClearLiability = async (liabilityId: string) => {
    try {
      setLoading(true);
      await insertData('clear-liability', {
        id: liabilityId,
        shop_id,
        status: 'paid'
      });

      toaster.create({
        title: 'Success',
        description: 'Liability cleared successfully',
        type: 'success'
      });

      fetchFinancialData();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: 'Failed to clear liability',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle liability editing
  const handleEditLiability = (liability: any) => {
    setEditingLiability(liability);
    setNewLiability({
      name: liability.name,
      amount: liability.amount.toString(),
      dueDate: liability.dueDate,
      description: liability.description
    });
    setShowLiabilityDialog(true);
  };

  // Fetch expenses list
  const fetchExpensesList = async () => {
    try {
      const response = await fetchData2('expenses', undefined, undefined, { shop_id });
      setExpensesList((response as any)?.data || []);
    } catch (error) {
      console.error('Error fetching expenses:', error);
    }
  };

  // Fetch liabilities list
  const fetchLiabilitiesList = async () => {
    try {
      const response = await fetchData2('liabilities', undefined, undefined, { shop_id });
      setLiabilitiesList((response as any)?.data || []);
    } catch (error) {
      console.error('Error fetching liabilities:', error);
    }
  };



  // Handle tab change and fetch data accordingly
  const handleTabChange = (details: any) => {
    const value = details.value;
    setActiveTab(value);

    if (value === 'expenses' && shop_id) {
      fetchExpensesList();
    } else if (value === 'liabilities' && shop_id) {
      fetchLiabilitiesList();
    }
  };

  // Handle expense editing
  const handleEditExpense = (expense: any) => {
    setEditingExpense(expense);
    setNewExpense({
      trans_total: expense.trans_total?.toString() || '',
      trans_description: expense.trans_description || '',
      expense_category: expense.expense_category || '',
      expense_details: expense.expense_details || ''
    });
    setShowExpenseForm(true);
  };

  // Handle expense deletion
  const handleDeleteExpense = async (expenseId: string) => {
    try {
      setLoading(true);
      await insertData('delete-expense', { id: expenseId, shop_id });

      toaster.create({
        title: 'Success',
        description: 'Expense deleted successfully',
        type: 'success'
      });

      fetchExpensesList();
      fetchFinancialData();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: 'Failed to delete expense',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle liability deletion
  const handleDeleteLiability = async (liabilityId: string) => {
    try {
      setLoading(true);
      await insertData('delete-liability', { id: liabilityId, shop_id });

      toaster.create({
        title: 'Success',
        description: 'Liability deleted successfully',
        type: 'success'
      });

      fetchLiabilitiesList();
      fetchFinancialData();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: 'Failed to delete liability',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle expense creation using new API
  const handleCreateExpense = async () => {
    if (!newExpense.trans_total || !shop_id) return;

    try {
      setisLoading(true);
      await insertData('expenses', {
        ... newExpense,
        shop_id,
        username: user?.username || 'Unknown',
        trans_total: parseFloat(newExpense.trans_total),
        trans_description: newExpense.trans_description || 'Expense Transaction',
        expense_category: newExpense.expense_category || null,
        expense_details: newExpense.expense_details || null
      });

      toaster.create({
        title: 'Success',
        description: 'Expense transaction created successfully',
        type: 'success'
      });

      setNewExpense({ trans_total: '', trans_description: '', expense_category: '', expense_details: '' });
      setShowExpenseForm(false);
      setShowWithdrawalDialog(false);
      fetchFinancialData();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: 'Failed to create expense transaction',
        type: 'error'
      });
    } finally {
      setisLoading(false);
    }
  };

  // Handle income creation using new API
  const handleCreateIncome = async () => {
    if (!newIncome.trans_total || !shop_id) return;

    try {
      setisLoading(true);
      await insertData('create_income', {
        shop_id,
        username: user?.username || 'Unknown',
        trans_total: parseFloat(newIncome.trans_total),
        trans_description: newIncome.trans_description || 'Income Transaction',
        income_source: newIncome.income_source || null,
        income_details: newIncome.income_details || null
      });

      toaster.create({
        title: 'Success',
        description: 'Income transaction created successfully',
        type: 'success'
      });

      setNewIncome({ trans_total: '', trans_description: '', income_source: '', income_details: '' });
      setShowIncomeDialog(false);
      fetchFinancialData();
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: 'Failed to create income transaction',
        type: 'error'
      });
    } finally {
      setisLoading(false);
    }
  };

  useEffect(() => {
    fetchFinancialData();
    if (shop_id) {
      fetchExpensesList();
      fetchLiabilitiesList();
    }
  }, [timeFrame, shop_id]);

  if (loading) {
    return (
      <Box p={6}>
        <VStack gap={4}>
          <Skeleton height="60px" width="100%" />
          <Skeleton height="200px" width="100%" />
          <Skeleton height="300px" width="100%" />
        </VStack>
      </Box>
    );
  }

  return (
    <VStack gap={6} align="stretch" p={6}>
      {/* Header with Time Frame Selector */}
      <HStack justify="space-between" w="full">
        <VStack align="start" gap={1}>
          <Heading size="lg" color="gray.700">Financial Reports</Heading>
          {financialData?.period && (
            <Text fontSize="sm" color="gray.600">
              Period: {new Date(financialData.period.start).toLocaleDateString()} - {new Date(financialData.period.end).toLocaleDateString()}
            </Text>
          )}
        </VStack>
        <NativeSelectRoot w="200px">
          <NativeSelectField value={timeFrame} onChange={(e) => setTimeFrame(e.target.value)}>
            <option value="daily">Today</option>
            <option value="weekly">This Week</option>
            <option value="monthly">This Month</option>
            <option value="yearly">This Year</option>
          </NativeSelectField>
        </NativeSelectRoot>
      </HStack>

      {/* Financial Overview Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 5 }} gap={6}>
        <Card.Root>
          <Card.Body>
            <StatRoot>
              <StatLabel>Total Revenue</StatLabel>
              <StatValueText
                value={financialData?.income_statement ? calculateTotalRevenue(financialData.income_statement.revenue) : 0}
                formatOptions={{ style: "currency", currency: "KES" }}
              />
              <StatHelpText>
                <FiTrendingUp color="green" />
                {timeFrame} performance
              </StatHelpText>
            </StatRoot>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <StatRoot>
              <StatLabel>Cost of Goods Sold</StatLabel>
              <StatValueText
                value={financialData?.income_statement ? calculateTotalCOGS(financialData.income_statement.cost_of_goods_sold) : 0}
                formatOptions={{ style: "currency", currency: "KES" }}
              />
              <StatHelpText>
                <FiTrendingDown color="red" />
                Cost of sales
              </StatHelpText>
            </StatRoot>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <StatRoot>
              <StatLabel>Gross Profit</StatLabel>
              <StatValueText
                value={financialData?.income_statement ? calculateGrossProfit(financialData.income_statement.revenue, financialData.income_statement.cost_of_goods_sold) : 0}
                formatOptions={{ style: "currency", currency: "KES" }}
              />
              <StatHelpText>
                <FiDollarSign color="blue" />
                Revenue - COGS
              </StatHelpText>
            </StatRoot>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <StatRoot>
              <StatLabel>Total Transactions</StatLabel>
              <StatValueText
                value={financialData?.balance_sheet?.activity_metrics?.total_transactions || 0}
                formatOptions={{ style: "decimal" }}
              />
              <StatHelpText>
                <FiCreditCard color="purple" />
                All transactions
              </StatHelpText>
            </StatRoot>
          </Card.Body>
        </Card.Root>

        <Card.Root>
          <Card.Body>
            <StatRoot>
              <StatLabel>Inventory Value</StatLabel>
              <StatValueText
                value={financialData?.balance_sheet?.assets?.inventory?.current_inventory_value || 0}
                formatOptions={{ style: "currency", currency: "KES" }}
              />
              <StatHelpText>
                <FiTrendingUp color="orange" />
                Current stock value
              </StatHelpText>
            </StatRoot>
          </Card.Body>
        </Card.Root>
      </SimpleGrid>

      {/* Tabs for different sections */}
      <Tabs.Root defaultValue="overview" variant="enclosed" onValueChange={handleTabChange}>
        <Tabs.List>
          <Tabs.Trigger value="overview">Overview</Tabs.Trigger>
          <Tabs.Trigger value="liabilities">Liabilities</Tabs.Trigger>
          <Tabs.Trigger value="expenses">Expenses</Tabs.Trigger>
          <Tabs.Trigger value="direct-income">Direct Income</Tabs.Trigger>
          <Tabs.Trigger value="money">Money Management</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="overview">
          <VStack gap={6} align="stretch">
            {/* Income Statement */}
            <Card.Root
              boxShadow="xl"
              borderRadius="2xl"
              overflow="hidden"
              border="1px solid"
              borderColor="gray.100"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
              bg="white"
              _hover={{
                boxShadow: "2xl",
                transform: "translateY(-2px)",
              }}
              transition="all 0.3s ease"
            >
              <Card.Header
                bg="linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1))"
                _dark={{ bg: "linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(16, 185, 129, 0.2))" }}
                p={6}
                position="relative"
                _before={{
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  bg: 'linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #8b5cf6)',
                }}
              >
                <HStack justify="space-between" align="center">
                  <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
                      Income Statement
                    </Heading>
                    <Text color="gray.600" _dark={{ color: "gray.400" }} fontSize="sm" fontWeight="500">
                      {timeFrame.charAt(0).toUpperCase() + timeFrame.slice(1)} Financial Performance
                    </Text>
                  </VStack>
                  <Box
                    p={3}
                    bg="blue.100"
                    _dark={{ bg: "blue.900" }}
                    borderRadius="xl"
                  >
                    <FiTrendingUp size={24} color="#3b82f6" />
                  </Box>
                </HStack>
              </Card.Header>

              <Card.Body p={8}>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap={8}>
                  {/* Revenue Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02))"

                    borderRadius="xl"
                    border="1px solid"
                    borderColor="green.200"
                    _dark={{ borderColor: "green.700", bg: "linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(16, 185, 129, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #10b981, #059669)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="green.600" fontSize="lg">
                          Revenue Breakdown
                        </Text>
                        <Box p={2} bg="green.100" _dark={{ bg: "green.900" }} borderRadius="lg">
                          <FiTrendingUp size={16} color="#059669" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Normal Sales:</Text>
                          <Text fontWeight="semibold" color="green.700" _dark={{ color: "green.300" }}>
                            {formatCurrency(parseFloat(financialData?.income_statement?.revenue?.normal_sales || 0))}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Order Sales:</Text>
                          <Text fontWeight="semibold" color="green.700" _dark={{ color: "green.300" }}>
                            {formatCurrency(parseFloat(financialData?.income_statement?.revenue?.order_sales || 0))}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Supply Sales:</Text>
                          <Text fontWeight="semibold" color="green.700" _dark={{ color: "green.300" }}>
                            {formatCurrency(parseFloat(financialData?.income_statement?.revenue?.supply_sales || 0))}
                          </Text>
                        </HStack>

                        <Separator borderColor="green.200" _dark={{ borderColor: "green.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="green.50"

                          borderRadius="lg"
                          border="1px solid"
                          borderColor="green.200"
                          _dark={{ borderColor: "green.700", bg: "green.900" }}
                        >
                          <HStack justify="space-between" w="full">
                            <Text fontWeight="bold" fontSize="md" color="green.800" _dark={{ color: "green.200" }}>
                              Total Revenue:
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="green.800" _dark={{ color: "green.200" }}>
                              {formatCurrency(financialData?.income_statement ? calculateTotalRevenue(financialData.income_statement.revenue) : 0)}
                            </Text>
                          </HStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* COGS Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="red.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05))", borderColor: "red.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(239, 68, 68, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #ef4444, #dc2626)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="red.600" fontSize="lg">
                          Cost of Goods Sold
                        </Text>
                        <Box p={2} bg="red.100" _dark={{ bg: "red.900" }} borderRadius="lg">
                          <FiTrendingDown size={16} color="#dc2626" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Sales COGS:</Text>
                          <Text fontWeight="semibold" color="red.700" _dark={{ color: "red.300" }}>
                            {formatCurrency(parseFloat(financialData?.income_statement?.cost_of_goods_sold?.sales_cogs || 0))}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Supply COGS:</Text>
                          <Text fontWeight="semibold" color="red.700" _dark={{ color: "red.300" }}>
                            {formatCurrency(parseFloat(financialData?.income_statement?.cost_of_goods_sold?.supply_cogs || 0))}
                          </Text>
                        </HStack>

                        <Separator borderColor="red.200" _dark={{ borderColor: "red.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="red.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="red.200"
                          _dark={{ bg: "red.900", borderColor: "red.700" }}
                        >
                          <HStack justify="space-between" w="full">
                            <Text fontWeight="bold" fontSize="md" color="red.800" _dark={{ color: "red.200" }}>
                              Total COGS:
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="red.800" _dark={{ color: "red.200" }}>
                              {formatCurrency(financialData?.income_statement ? calculateTotalCOGS(financialData.income_statement.cost_of_goods_sold) : 0)}
                            </Text>
                          </HStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* Gross Profit Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(139, 92, 246, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="purple.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05))", borderColor: "purple.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(139, 92, 246, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #8b5cf6, #7c3aed)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="purple.600" fontSize="lg">
                          Gross Profit
                        </Text>
                        <Box p={2} bg="purple.100" _dark={{ bg: "purple.900" }} borderRadius="lg">
                          <FiDollarSign size={16} color="#7c3aed" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Sales Profit:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {formatCurrency(financialData?.income_statement?.gross_profit?.sales_profit || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Supply Profit:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {formatCurrency(financialData?.income_statement?.gross_profit?.supply_profit || 0)}
                          </Text>
                        </HStack>

                        <Separator borderColor="purple.200" _dark={{ borderColor: "purple.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="purple.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="purple.200"
                          _dark={{ bg: "purple.900", borderColor: "purple.700" }}
                        >
                          <HStack justify="space-between" w="full">
                            <Text fontWeight="bold" fontSize="md" color="purple.800" _dark={{ color: "purple.200" }}>
                              Total Gross Profit:
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="purple.800" _dark={{ color: "purple.200" }}>
                              {formatCurrency(financialData?.income_statement ? calculateGrossProfit(financialData.income_statement.revenue, financialData.income_statement.cost_of_goods_sold) : 0)}
                            </Text>
                          </HStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* Net Profit Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="blue.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05))", borderColor: "blue.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(59, 130, 246, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #3b82f6, #2563eb)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="blue.600" fontSize="lg">
                          Net Profit
                        </Text>
                        <Box p={2} bg="blue.100" _dark={{ bg: "blue.900" }} borderRadius="lg">
                          <FiTrendingUp size={16} color="#2563eb" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Total Expenses:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {formatCurrency(financialData?.income_statement?.expenses?.total_expenses || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Expense Count:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {financialData?.income_statement?.expenses?.expense_count || 0}
                          </Text>
                        </HStack>

                        <Separator borderColor="blue.200" _dark={{ borderColor: "blue.700" }} />

                        <Box
                          w="full"
                          p={4}
                          bg="blue.50"
                          borderRadius="lg"
                          border="2px solid"
                          borderColor="blue.300"
                          _dark={{ bg: "blue.900", borderColor: "blue.600" }}
                          boxShadow="0 4px 12px rgba(59, 130, 246, 0.15)"
                        >
                          <VStack gap={2}>
                            <Text fontSize="sm" color="blue.600" _dark={{ color: "blue.400" }} fontWeight="medium">
                              Final Net Profit
                            </Text>
                            <Text fontWeight="bold" fontSize="2xl" color="blue.800" _dark={{ color: "blue.200" }}>
                              {formatCurrency(
                                (financialData?.income_statement ? calculateGrossProfit(financialData.income_statement.revenue, financialData.income_statement.cost_of_goods_sold) : 0) -
                                (financialData?.income_statement?.expenses?.total_expenses || 0)
                              )}
                            </Text>
                          </VStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>
                </SimpleGrid>
              </Card.Body>
            </Card.Root>

            {/* Balance Sheet */}
            <Card.Root
              boxShadow="xl"
              borderRadius="2xl"
              overflow="hidden"
              border="1px solid"
              borderColor="gray.100"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
              bg="white"
              _hover={{
                boxShadow: "2xl",
                transform: "translateY(-2px)",
              }}
              transition="all 0.3s ease"
            >
              <Card.Header
                bg="linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(139, 92, 246, 0.1))"
                _dark={{ bg: "linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(139, 92, 246, 0.2))" }}
                p={6}
                position="relative"
                _before={{
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '3px',
                  bg: 'linear-gradient(90deg, #10b981, #f59e0b, #8b5cf6, #3b82f6)',
                }}
              >
                <HStack justify="space-between" align="center">
                  <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
                      Balance Sheet & Business Analytics
                    </Heading>
                    <Text color="gray.600" _dark={{ color: "gray.400" }} fontSize="sm" fontWeight="500">
                      Assets, Liabilities & Business Performance
                    </Text>
                  </VStack>
                  <Box
                    p={3}
                    bg="orange.100"
                    _dark={{ bg: "orange.900" }}
                    borderRadius="xl"
                  >
                    <FiCreditCard size={24} color="#f59e0b" />
                  </Box>
                </HStack>
              </Card.Header>

              <Card.Body p={8}>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap={8}>
                  {/* Assets Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="green.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))", borderColor: "green.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(16, 185, 129, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #10b981, #059669)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="green.600" fontSize="lg">
                          Assets
                        </Text>
                        <Box p={2} bg="green.100" _dark={{ bg: "green.900" }} borderRadius="lg">
                          <FiTrendingUp size={16} color="#059669" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Inventory Value:</Text>
                          <Text fontWeight="semibold" color="green.700" _dark={{ color: "green.300" }}>
                            {formatCurrency(financialData?.balance_sheet?.assets?.inventory?.current_inventory_value || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Inventory Items:</Text>
                          <Text fontWeight="semibold" color="green.700" _dark={{ color: "green.300" }}>
                            {financialData?.balance_sheet?.assets?.inventory?.inventory_items || 0}
                          </Text>
                        </HStack>

                        <Separator borderColor="green.200" _dark={{ borderColor: "green.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="green.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="green.200"
                          _dark={{ bg: "green.900", borderColor: "green.700" }}
                        >
                          <VStack gap={1}>
                            <Text fontSize="sm" color="green.600" _dark={{ color: "green.400" }} fontWeight="medium">
                              Total Assets
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="green.800" _dark={{ color: "green.200" }}>
                              {formatCurrency(financialData?.balance_sheet?.assets?.inventory?.current_inventory_value || 0)}
                            </Text>
                          </VStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* Liabilities Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="orange.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05))", borderColor: "orange.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(245, 158, 11, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #f59e0b, #d97706)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="orange.600" fontSize="lg">
                          Liabilities
                        </Text>
                        <Box p={2} bg="orange.100" _dark={{ bg: "orange.900" }} borderRadius="lg">
                          <FiCreditCard size={16} color="#d97706" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Total Liabilities:</Text>
                          <Text fontWeight="semibold" color="orange.700" _dark={{ color: "orange.300" }}>
                            {formatCurrency(financialData?.balance_sheet?.liabilities?.total_liabilities || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Outstanding:</Text>
                          <Text fontWeight="semibold" color="orange.700" _dark={{ color: "orange.300" }}>
                            {formatCurrency(financialData?.balance_sheet?.liabilities?.outstanding || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Overdue Amount:</Text>
                          <Text fontWeight="semibold" color="orange.700" _dark={{ color: "orange.300" }}>
                            {formatCurrency(financialData?.balance_sheet?.liabilities?.overdue_amount || 0)}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Liability Count:</Text>
                          <Text fontWeight="semibold" color="orange.700" _dark={{ color: "orange.300" }}>
                            {financialData?.balance_sheet?.liabilities?.liability_count || 0}
                          </Text>
                        </HStack>

                        <Separator borderColor="orange.200" _dark={{ borderColor: "orange.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="orange.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="orange.200"
                          _dark={{ bg: "orange.900", borderColor: "orange.700" }}
                        >
                          <VStack gap={1}>
                            <Text fontSize="sm" color="orange.600" _dark={{ color: "orange.400" }} fontWeight="medium">
                              Net Liabilities
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="orange.800" _dark={{ color: "orange.200" }}>
                              {formatCurrency(financialData?.balance_sheet?.liabilities?.total_liabilities || 0)}
                            </Text>
                          </VStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* Supply Analytics Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(139, 92, 246, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="purple.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(139, 92, 246, 0.05))", borderColor: "purple.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(139, 92, 246, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #8b5cf6, #7c3aed)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="purple.600" fontSize="lg">
                          Supply Analytics
                        </Text>
                        <Box p={2} bg="purple.100" _dark={{ bg: "purple.900" }} borderRadius="lg">
                          <FiDollarSign size={16} color="#7c3aed" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Pending Supplies:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {formatCurrency(parseFloat(financialData?.balance_sheet?.supply_analytics?.pending_supplies?.total_pending || 0))}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Pending Count:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {financialData?.balance_sheet?.supply_analytics?.pending_supplies?.pending_count || 0}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Paid Supplies:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {formatCurrency(parseFloat(financialData?.balance_sheet?.supply_analytics?.paid_supplies?.total_paid || 0))}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Paid Count:</Text>
                          <Text fontWeight="semibold" color="purple.700" _dark={{ color: "purple.300" }}>
                            {financialData?.balance_sheet?.supply_analytics?.paid_supplies?.paid_count || 0}
                          </Text>
                        </HStack>

                        <Separator borderColor="purple.200" _dark={{ borderColor: "purple.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="purple.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="purple.200"
                          _dark={{ bg: "purple.900", borderColor: "purple.700" }}
                        >
                          <VStack gap={1}>
                            <Text fontSize="sm" color="purple.600" _dark={{ color: "purple.400" }} fontWeight="medium">
                              Total Supply Value
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="purple.800" _dark={{ color: "purple.200" }}>
                              {formatCurrency(
                                parseFloat(financialData?.balance_sheet?.supply_analytics?.pending_supplies?.total_pending || 0) +
                                parseFloat(financialData?.balance_sheet?.supply_analytics?.paid_supplies?.total_paid || 0)
                              )}
                            </Text>
                          </VStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>

                  {/* Activity Metrics Section */}
                  <Box
                    p={6}
                    bg="linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02))"
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="blue.200"
                    _dark={{ bg: "linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05))", borderColor: "blue.700" }}
                    position="relative"
                    overflow="hidden"
                    _hover={{
                      transform: "translateY(-4px)",
                      boxShadow: "0 12px 30px rgba(59, 130, 246, 0.15)"
                    }}
                    transition="all 0.3s ease"
                    _before={{
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      bg: 'linear-gradient(90deg, #3b82f6, #2563eb)',
                    }}
                  >
                    <VStack align="start" gap={4}>
                      <HStack justify="space-between" w="full">
                        <Text fontWeight="bold" color="blue.600" fontSize="lg">
                          Activity Metrics
                        </Text>
                        <Box p={2} bg="blue.100" _dark={{ bg: "blue.900" }} borderRadius="lg">
                          <FiTrendingUp size={16} color="#2563eb" />
                        </Box>
                      </HStack>

                      <VStack align="start" gap={3} w="full">
                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Total Transactions:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {financialData?.balance_sheet?.activity_metrics?.total_transactions || 0}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Normal Sales:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {financialData?.balance_sheet?.activity_metrics?.normal_sales_count || 0}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Order Sales:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {financialData?.balance_sheet?.activity_metrics?.order_sales_count || 0}
                          </Text>
                        </HStack>

                        <HStack justify="space-between" w="full">
                          <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Supply Sales:</Text>
                          <Text fontWeight="semibold" color="blue.700" _dark={{ color: "blue.300" }}>
                            {financialData?.balance_sheet?.activity_metrics?.supply_sales_count || 0}
                          </Text>
                        </HStack>

                        <Separator borderColor="blue.200" _dark={{ borderColor: "blue.700" }} />

                        <Box
                          w="full"
                          p={3}
                          bg="blue.50"
                          borderRadius="lg"
                          border="1px solid"
                          borderColor="blue.200"
                          _dark={{ bg: "blue.900", borderColor: "blue.700" }}
                        >
                          <VStack gap={1}>
                            <Text fontSize="sm" color="blue.600" _dark={{ color: "blue.400" }} fontWeight="medium">
                              Business Activity
                            </Text>
                            <Text fontWeight="bold" fontSize="lg" color="blue.800" _dark={{ color: "blue.200" }}>
                              {financialData?.balance_sheet?.activity_metrics?.total_transactions || 0} Transactions
                            </Text>
                          </VStack>
                        </Box>
                      </VStack>
                    </VStack>
                  </Box>
                </SimpleGrid>

                {/* Enhanced Top Buyers Section */}
                {financialData?.balance_sheet?.supply_analytics?.top_buyers && financialData.balance_sheet.supply_analytics.top_buyers.length > 0 && (
                  <Box mt={8}>
                    <HStack justify="space-between" align="center" mb={6}>
                      <VStack align="start" gap={1}>
                        <Text fontWeight="bold" color="teal.600" fontSize="xl">
                          Top Buyers
                        </Text>
                        <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                          Your most valuable customers
                        </Text>
                      </VStack>
                      <Box
                        p={3}
                        bg="teal.100"
                        _dark={{ bg: "teal.900" }}
                        borderRadius="xl"
                      >
                        <FiTrendingUp size={20} color="#0d9488" />
                      </Box>
                    </HStack>

                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={6}>
                      {financialData.balance_sheet.supply_analytics.top_buyers.map((buyer: any, index: number) => (
                        <Box
                          key={index}
                          p={6}
                          bg="linear-gradient(135deg, rgba(20, 184, 166, 0.05), rgba(20, 184, 166, 0.02))"
                          borderRadius="xl"
                          border="1px solid"
                          borderColor="teal.200"
                          _dark={{ bg: "linear-gradient(135deg, rgba(20, 184, 166, 0.1), rgba(20, 184, 166, 0.05))", borderColor: "teal.700" }}
                          position="relative"
                          overflow="hidden"
                          _hover={{
                            transform: "translateY(-4px)",
                            boxShadow: "0 12px 30px rgba(20, 184, 166, 0.15)"
                          }}
                          transition="all 0.3s ease"
                          _before={{
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            height: '3px',
                            bg: 'linear-gradient(90deg, #14b8a6, #0d9488)',
                          }}
                        >
                          <VStack align="start" gap={3}>
                            <HStack justify="space-between" w="full">
                              <Text fontWeight="bold" fontSize="lg" color="teal.800" _dark={{ color: "teal.200" }}>
                                {buyer.customer_name}
                              </Text>
                              <Box
                                px={2}
                                py={1}
                                bg="teal.100"
                                borderRadius="md"
                                fontSize="xs"
                                fontWeight="bold"
                                color="teal.700"
                                _dark={{ bg: "teal.900", color: "teal.300" }}
                              >
                                #{index + 1}
                              </Box>
                            </HStack>

                            <VStack align="start" gap={2} w="full">
                              <HStack justify="space-between" w="full">
                                <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Total Purchased:</Text>
                                <Text fontWeight="semibold" color="teal.700" _dark={{ color: "teal.300" }}>
                                  {formatCurrency(parseFloat(buyer.total_purchased || 0))}
                                </Text>
                              </HStack>

                              <HStack justify="space-between" w="full">
                                <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>Purchase Count:</Text>
                                <Text fontWeight="semibold" color="teal.700" _dark={{ color: "teal.300" }}>
                                  {buyer.purchase_count || 0} orders
                                </Text>
                              </HStack>
                            </VStack>

                            <Box
                              w="full"
                              p={3}
                              bg="teal.50"
                              borderRadius="lg"
                              border="1px solid"
                              borderColor="teal.200"
                              _dark={{ bg: "teal.900", borderColor: "teal.700" }}
                              mt={2}
                            >
                              <Text fontSize="xs" color="teal.600" _dark={{ color: "teal.400" }} textAlign="center" fontWeight="medium">
                                Average: {formatCurrency(parseFloat(buyer.total_purchased || 0) / (buyer.purchase_count || 1))}
                              </Text>
                            </Box>
                          </VStack>
                        </Box>
                      ))}
                    </SimpleGrid>
                  </Box>
                )}
              </Card.Body>
            </Card.Root>
          </VStack>
        </Tabs.Content>

        <Tabs.Content value="liabilities">
          <VStack gap={6} align="stretch">
            {/* Liabilities Header */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <HStack justify="space-between" align="center">
                  <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
                      Liabilities Management
                    </Heading>
                    <Text color="gray.600" _dark={{ color: "gray.400" }} fontSize="sm">
                      Track and manage all business liabilities
                    </Text>
                  </VStack>
                  <Button
                    colorScheme="orange"
                    size="sm"
                    onClick={() => setShowLiabilityDialog(true)}
                  >
                    Add New Liability
                  </Button>
                </HStack>
              </Card.Header>
            </Card.Root>

            {/* Liabilities Summary */}
            <SimpleGrid columns={{ base: 1, md: 3 }} gap={6}>
              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="red.200"
                _dark={{ borderColor: "red.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02))"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Total Outstanding
                      </Text>
                      <Box p={2} bg="red.100" _dark={{ bg: "red.900" }} borderRadius="lg">
                        <FiTrendingDown size={16} color="#dc2626" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                      {formatCurrency(financialData?.balance_sheet?.liabilities?.outstanding || 0)}
                    </Text>
                    <Text fontSize="sm" color="red.600" _dark={{ color: "red.400" }}>
                      Amount due
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>

              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="orange.200"
                _dark={{ borderColor: "orange.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02))"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Total Liabilities
                      </Text>
                      <Box p={2} bg="orange.100" _dark={{ bg: "orange.900" }} borderRadius="lg">
                        <FiCreditCard size={16} color="#d97706" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="orange.600" _dark={{ color: "orange.300" }}>
                      {formatCurrency(financialData?.balance_sheet?.liabilities?.total_liabilities || 0)}
                    </Text>
                    <Text fontSize="sm" color="orange.600" _dark={{ color: "orange.400" }}>
                      Total debt
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>

              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="green.200"
                _dark={{ borderColor: "green.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02))"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Paid This Period
                      </Text>
                      <Box p={2} bg="green.100" _dark={{ bg: "green.900" }} borderRadius="lg">
                        <FiTrendingUp size={16} color="#059669" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600" _dark={{ color: "green.300" }}>
                      {formatCurrency((financialData?.balance_sheet?.liabilities?.total_liabilities || 0) - (financialData?.balance_sheet?.liabilities?.outstanding || 0))}
                    </Text>
                    <Text fontSize="sm" color="green.600" _dark={{ color: "green.400" }}>
                      Payments made
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>
            </SimpleGrid>

            {/* Liabilities Table */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <Heading size="md" color="gray.800" _dark={{ color: "white" }}>
                  Liabilities List
                </Heading>
              </Card.Header>
              <Card.Body p={0}>
                {liabilitiesList.length > 0 ? (
                  <Table.Root size="sm">
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Name</Table.ColumnHeader>
                        <Table.ColumnHeader>Amount</Table.ColumnHeader>
                        <Table.ColumnHeader>Due Date</Table.ColumnHeader>
                        <Table.ColumnHeader>Status</Table.ColumnHeader>
                        <Table.ColumnHeader>Actions</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body  p={10}>
                      {liabilitiesList.map((liability: any, index: number) => (
                        <Table.Row key={index}>
                          <Table.Cell>
                            <VStack align="start" gap={1}>
                              <Text fontWeight="medium">{liability.name}</Text>
                              {liability.description && (
                                <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                  {liability.description}
                                </Text>
                              )}
                            </VStack>
                          </Table.Cell>
                          <Table.Cell>
                            <Text fontWeight="bold" color="orange.600" _dark={{ color: "orange.300" }}>
                              {formatCurrency(liability.amount)}
                            </Text>
                          </Table.Cell>
                          <Table.Cell>
                            <Text fontSize="sm">
                              {liability.dueDate ? new Date(liability.dueDate).toLocaleDateString() : 'No due date'}
                            </Text>
                          </Table.Cell>
                          <Table.Cell>
                            <Box
                              px={2}
                              py={1}
                              bg={liability.status == 'paid' ? 'green.100' : 'red.100'}
                              _dark={{ color: liability.status == 'paid' ? 'green.300' : 'red.300'  }}
                              borderRadius="md"
                              fontSize="xs"
                              fontWeight="bold"
                              color={liability.status == 'paid' ? 'green.700' : 'red.700'}
                      
                              display="inline-block"
                            >
                              {liability.status == 'paid' ? 'Paid' : 'Outstanding'}
                            </Box>
                          </Table.Cell>
                          <Table.Cell>
                            <HStack gap={2}>
                              <Button
                                size="xs"
                                colorScheme="blue"
                                onClick={() => handleEditLiability(liability)}
                              >
                                Edit
                              </Button>
                              {liability.status !== 'paid' && (
                                <Button
                                  size="xs"
                                  colorScheme="green"
                                  onClick={() => handleClearLiability(liability.id)}
                                >
                                  Clear
                                </Button>
                              )}
                              <Button
                                size="xs"
                                colorScheme="red"
                                onClick={() => handleDeleteLiability(liability.id)}
                              >
                                Delete
                              </Button>
                            </HStack>
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table.Root>
                ) : (
                  <Box p={8} textAlign="center">
                    <Text color="gray.500" _dark={{ color: "gray.400" }}>
                      No liabilities found. Add your first liability to get started.
                    </Text>
                  </Box>
                )}
              </Card.Body>
            </Card.Root>
          </VStack>
        </Tabs.Content>

        <Tabs.Content value="expenses">
          <VStack gap={6} align="stretch">
            {/* Expenses Header */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <HStack justify="space-between" align="center">
                  <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
                      Expenses Management
                    </Heading>
                    <Text color="gray.600" _dark={{ color: "gray.400" }} fontSize="sm">
                      Track and manage all business expenses
                    </Text>
                  </VStack>
               
                </HStack>
              </Card.Header>
            </Card.Root>

            {/* Expenses Summary */}
            <SimpleGrid columns={{ base: 1, md: 2 }} gap={6}>
              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="red.200"
                _dark={{ borderColor: "red.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02))"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Total Expenses
                      </Text>
                      <Box p={2} bg="red.100" _dark={{ bg: "red.900" }} borderRadius="lg">
                        <FiTrendingDown size={16} color="#dc2626" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                      {formatCurrency(financialData?.income_statement?.expenses?.total_expenses || 0)}
                    </Text>
                    <Text fontSize="sm" color="red.600" _dark={{ color: "red.400" }}>
                      This {timeFrame}
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>

              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="blue.200"
                _dark={{ borderColor: "blue.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02))"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Expense Ratio
                      </Text>
                      <Box p={2} bg="blue.100" _dark={{ bg: "blue.900" }} borderRadius="lg">
                        <FiDollarSign size={16} color="#2563eb" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="blue.600" _dark={{ color: "blue.300" }}>
                      {financialData?.income_statement?.revenue?.total_revenue
                        ? ((financialData?.income_statement?.expenses?.total_expenses || 0) / financialData?.income_statement?.revenue?.total_revenue * 100).toFixed(1)
                        : 0}%
                    </Text>
                    <Text fontSize="sm" color="blue.600" _dark={{ color: "blue.400" }}>
                      Of total revenue
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>
            </SimpleGrid>

            {/* Expenses Table */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <Heading size="md" color="gray.800" _dark={{ color: "white" }}>
                  Expenses List
                </Heading>
              </Card.Header>
              <Card.Body p={0}>
                {expensesList.length > 0 ? (
                  <Table.Root size="sm">
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Date</Table.ColumnHeader>
                        <Table.ColumnHeader>Description</Table.ColumnHeader>
                        <Table.ColumnHeader>Category</Table.ColumnHeader>
                        <Table.ColumnHeader>Amount</Table.ColumnHeader>
                        <Table.ColumnHeader>Actions</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {expensesList.map((expense: any, index: number) => (
                        <Table.Row key={index}>
                          <Table.Cell>
                            {new Date(expense.created_at).toLocaleDateString()}
                          </Table.Cell>
                          <Table.Cell>
                            <VStack align="start" gap={1}>
                              <Text fontWeight="medium">{expense.trans_description}</Text>
                              {expense.expense_details && (
                                <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                                  {expense.expense_details}
                                </Text>
                              )}
                            </VStack>
                          </Table.Cell>
                          <Table.Cell>
                            <Box
                              px={2}
                              py={1}
                              bg="blue.100"
                              borderRadius="md"
                              fontSize="xs"
                              fontWeight="bold"
                              color="blue.700"
                              _dark={{ bg: "blue.900", color: "blue.300" }}
                              display="inline-block"
                            >
                              {expense.expense_category || 'Other'}
                            </Box>
                          </Table.Cell>
                          <Table.Cell>
                            <Text fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                              {formatCurrency(expense.trans_total)}
                            </Text>
                          </Table.Cell>
                          <Table.Cell>
                            <HStack gap={2}>
                              <Button
                                size="xs"
                                colorScheme="blue"
                                onClick={() => handleEditExpense(expense)}
                              >
                                Edit
                              </Button>
                              <Button
                                size="xs"
                                colorScheme="red"
                                onClick={() => handleDeleteExpense(expense.id)}
                              >
                                Delete
                              </Button>
                            </HStack>
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table.Root>
                ) : (
                  <Box p={8} textAlign="center">
                    <Text color="gray.500" _dark={{ color: "gray.400" }}>
                      No expenses found. Add your first expense to get started.
                    </Text>
                  </Box>
                )}
              </Card.Body>
            </Card.Root>
          </VStack>
        </Tabs.Content>

        <Tabs.Content value="direct-income">
          <VStack gap={6} align="stretch">
            {/* Direct Income Header */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
            </Card.Root>

            {/* Income Transactions Table */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <Heading size="md" color="gray.800" _dark={{ color: "white" }}>
                  Income Transactions
                </Heading>
              </Card.Header>
              <Card.Body p={0}>
                  <TabTable status={[22]} mode="complete" user={user} query={seachquery} checkTransaction={() => {}} />
              </Card.Body>
            </Card.Root>
          </VStack>
        </Tabs.Content>

        <Tabs.Content value="money">

          {isLoading && (
            <Center p={8}>
              <Skeleton height="400px" width="100%" />
            </Center>
          ) }
          <VStack gap={6} align="stretch">
            {/* Header with Action Buttons */}
            <Card.Root
              boxShadow="lg"
              borderRadius="xl"
              border="1px solid"
              borderColor="gray.200"
              _dark={{ borderColor: "gray.700", bg: "gray.800" }}
            >
              <Card.Header p={6}>
                <HStack justify="space-between" align="center">
                  <VStack align="start" gap={1}>
                    <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
                      Money Management
                    </Heading>
                    <Text color="gray.600" _dark={{ color: "gray.400" }} fontSize="sm">
                      Manage income, expenses, withdrawals, and liabilities
                    </Text>
                  </VStack>
                  <HStack gap={3}>
                    <Button
                      colorScheme="green"
                      size="sm"
                      onClick={() => setShowIncomeDialog(true)}
                    >
                      Income Entry
                    </Button>
                    <Button
                      colorScheme="red"
                      size="sm"
                      onClick={() => setShowWithdrawalDialog(true)}
                    >
                      Withdrawal
                    </Button>
                  </HStack>
                </HStack>
              </Card.Header>
            </Card.Root>

            {/* Enhanced Money Summary Cards */}
            <SimpleGrid columns={{ base: 1, md: 3 }} gap={6}>
              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="green.200"
                _dark={{ borderColor: "green.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02))"
                _hover={{ transform: "translateY(-2px)", boxShadow: "xl" }}
                transition="all 0.3s ease"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Money In
                      </Text>
                      <Box p={2} bg="green.100" _dark={{ bg: "green.900" }} borderRadius="lg">
                        <FiTrendingUp size={16} color="#059669" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600" _dark={{ color: "green.300" }}>
                      {formatCurrency(moneySummary?.money_in || 0)}
                    </Text>
                    <Text fontSize="sm" color="green.600" _dark={{ color: "green.400" }}>
                      Total inflow this period
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>

              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="red.200"
                _dark={{ borderColor: "red.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(239, 68, 68, 0.05), rgba(239, 68, 68, 0.02))"
                _hover={{ transform: "translateY(-2px)", boxShadow: "xl" }}
                transition="all 0.3s ease"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Money Out
                      </Text>
                      <Box p={2} bg="red.100" _dark={{ bg: "red.900" }} borderRadius="lg">
                        <FiTrendingDown size={16} color="#dc2626" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="red.600" _dark={{ color: "red.300" }}>
                      {formatCurrency(moneySummary?.money_out || 0)}
                    </Text>
                    <Text fontSize="sm" color="red.600" _dark={{ color: "red.400" }}>
                      Total outflow this period
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>

              <Card.Root
                boxShadow="lg"
                borderRadius="xl"
                border="1px solid"
                borderColor="blue.200"
                _dark={{ borderColor: "blue.700", bg: "gray.800" }}
                bg="linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02))"
                _hover={{ transform: "translateY(-2px)", boxShadow: "xl" }}
                transition="all 0.3s ease"
              >
                <Card.Body p={6}>
                  <VStack align="start" gap={3}>
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium" color="gray.600" _dark={{ color: "gray.400" }}>
                        Net Movement
                      </Text>
                      <Box p={2} bg="blue.100" _dark={{ bg: "blue.900" }} borderRadius="lg">
                        <FiDollarSign size={16} color="#2563eb" />
                      </Box>
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="blue.600" _dark={{ color: "blue.300" }}>
                      {formatCurrency((moneySummary?.money_in || 0) - (moneySummary?.money_out || 0))}
                    </Text>
                    <Text fontSize="sm" color="blue.600" _dark={{ color: "blue.400" }}>
                      Available balance
                    </Text>
                  </VStack>
                </Card.Body>
              </Card.Root>
            </SimpleGrid>

            {/* Income Entry Modal */}
            {showIncomeDialog && (
              <Card.Root
                position="fixed"
                top="50%"
                left="50%"
                transform="translate(-50%, -50%)"
                zIndex={1000}
                boxShadow="2xl"
                borderRadius="xl"
                border="1px solid"
                borderColor="green.200"
                _dark={{ borderColor: "green.700", bg: "gray.800" }}
                bg="white"
                w={{ base: "90%", md: "500px" }}
                maxH="90vh"
                overflow="auto"
              >
                <Card.Header
                  bg="linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))"
                  p={6}
                >
                  <HStack justify="space-between">
                    <VStack align="start" gap={1}>
                      <Heading size="md" color="green.700" _dark={{ color: "green.300" }}>
                        Income Entry
                      </Heading>
                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                        Record business income transaction
                      </Text>
                    </VStack>
                    <Button size="sm" variant="ghost" onClick={() => setShowIncomeDialog(false)}>
                      ×
                    </Button>
                  </HStack>
                </Card.Header>
                <Card.Body p={6}>
                  <VStack gap={4}>
                    <Field label="Amount *">
                      <Input
                        type="number"
                        value={newIncome.trans_total}
                        onChange={(e) => setNewIncome({...newIncome, trans_total: e.target.value})}
                        placeholder="0.00"
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                      />
                    </Field>

                    <Field label="Income Source">
                      <NativeSelectRoot>
                        <NativeSelectField
                          value={newIncome.income_source}
                          onChange={(e) => setNewIncome({...newIncome, income_source: e.target.value})}
                          bg="white"
                          _dark={{ bg: "gray.700" }}
                        >
                          <option value="">Select source</option>
                          <option value="business_investment">Business Investment</option>
                          <option value="loan">Loan</option>
                          <option value="grant">Grant</option>
                          <option value="partnership">Partnership</option>
                          <option value="other_income">Other Income</option>
                        </NativeSelectField>
                      </NativeSelectRoot>
                    </Field>

                    <Field label="Description">
                      <Input
                        value={newIncome.trans_description}
                        onChange={(e) => setNewIncome({...newIncome, trans_description: e.target.value})}
                        placeholder="Brief description of income"
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                      />
                    </Field>

                    <Field label="Additional Details">
                      <Textarea
                        value={newIncome.income_details}
                        onChange={(e) => setNewIncome({...newIncome, income_details: e.target.value})}
                        placeholder="Additional information, terms, conditions, etc."
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                        rows={3}
                      />
                    </Field>

                    <HStack gap={3} w="full" justify="end">
                      <Button variant="outline" onClick={() => setShowIncomeDialog(false)}>
                        Cancel
                      </Button>
                      <Button
                        colorScheme="green"
                        onClick={handleCreateIncome}
                        disabled={loading || !newIncome.trans_total}
                      >
                        {loading ? 'Adding...' : 'Add Income'}
                      </Button>
                    </HStack>
                  </VStack>
                </Card.Body>
              </Card.Root>
            )}

            {/* Withdrawal Modal */}
            {showWithdrawalDialog && (
              <Card.Root
                position="fixed"
                top="50%"
                left="50%"
                transform="translate(-50%, -50%)"
                zIndex={1000}
                boxShadow="2xl"
                borderRadius="xl"
                border="1px solid"
                borderColor="red.200"
                _dark={{ borderColor: "red.700", bg: "gray.800" }}
                bg="white"
                w={{ base: "90%", md: "500px" }}
                maxH="90vh"
                overflow="auto"
              >
                <Card.Header
                  bg="linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(239, 68, 68, 0.05))"
                  p={6}
                >
                  <HStack justify="space-between">
                    <VStack align="start" gap={1}>
                      <Heading size="md" color="red.700" _dark={{ color: "red.300" }}>
                        Withdrawal
                      </Heading>
                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                        Record business withdrawal transaction
                      </Text>
                    </VStack>
                    <Button size="sm" variant="ghost" onClick={() => setShowWithdrawalDialog(false)}>
                      ×
                    </Button>
                  </HStack>
                </Card.Header>
                <Card.Body p={6}>
                  <VStack gap={4}>
                    <Field label="Amount *">
                      <Input
                        type="number"
                        value={newExpense.trans_total}
                        onChange={(e) => setNewExpense({...newExpense, trans_total: e.target.value})}
                        placeholder="0.00"
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                      />
                    </Field>

                    <Field label="Withdrawal Category">
                      <NativeSelectRoot>
                        <NativeSelectField
                          value={newExpense.expense_category}
                          onChange={(e) => setNewExpense({...newExpense, expense_category: e.target.value})}
                          bg="white"
                          _dark={{ bg: "gray.700" }}
                        >
                          <option value="">Select category</option>
                          <option value="withdrawal">Personal Withdrawal</option>
                          <option value="business_expense">Business Expense</option>
                          <option value="emergency">Emergency</option>
                          <option value="other">Other</option>
                        </NativeSelectField>
                      </NativeSelectRoot>
                    </Field>

                    <Field label="Description">
                      <Input
                        value={newExpense.trans_description}
                        onChange={(e) => setNewExpense({...newExpense, trans_description: e.target.value})}
                        placeholder="Brief description of withdrawal"
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                      />
                    </Field>

                    <Field label="Additional Details">
                      <Textarea
                        value={newExpense.expense_details}
                        onChange={(e) => setNewExpense({...newExpense, expense_details: e.target.value})}
                        placeholder="Additional information, purpose, etc."
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                        rows={3}
                      />
                    </Field>

                    {newExpense.trans_total && parseFloat(newExpense.trans_total) > ((moneySummary?.money_in || 0) - (moneySummary?.money_out || 0)) && (
                      <Text color="red.500" fontSize="sm">
                        Insufficient funds. Available: {formatCurrency((moneySummary?.money_in || 0) - (moneySummary?.money_out || 0))}
                      </Text>
                    )}

                    <HStack gap={3} w="full" justify="end">
                      <Button variant="outline" onClick={() => setShowWithdrawalDialog(false)}>
                        Cancel
                      </Button>
                      <Button
                        colorScheme="red"
                        onClick={handleCreateExpense}
                        disabled={loading || !newExpense.trans_total}
                      >
                        {loading ? 'Processing...' : 'Withdraw'}
                      </Button>
                    </HStack>
                  </VStack>
                </Card.Body>
              </Card.Root>
            )}







            {/* Enhanced Liability Form Modal */}
            {showLiabilityForm && (
              <Card.Root
                boxShadow="2xl"
                borderRadius="xl"
                border="1px solid"
                borderColor="orange.200"
                _dark={{ borderColor: "orange.700", bg: "gray.800" }}
              >
                <Card.Header
                  bg="linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05))"
                  p={6}
                >
                  <HStack justify="space-between">
                    <VStack align="start" gap={1}>
                      <Heading size="md" color="orange.700" _dark={{ color: "orange.300" }}>
                        {editingLiability ? 'Update Liability' : 'Add New Liability'}
                      </Heading>
                      <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                        {editingLiability ? 'Update liability information' : 'Record a new business liability'}
                      </Text>
                    </VStack>
                    <Button size="sm" variant="ghost" onClick={() => {
                      setShowLiabilityForm(false);
                      setEditingLiability(null);
                      setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
                    }}>
                      ×
                    </Button>
                  </HStack>
                </Card.Header>
                <Card.Body p={6}>
                  <VStack gap={6}>
                    <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
                      <Field label="Liability Name *">
                        <Input
                          value={newLiability.name}
                          onChange={(e) => setNewLiability({...newLiability, name: e.target.value})}
                          placeholder="e.g., Business Loan"
                          bg="white"
                          _dark={{ bg: "gray.700" }}
                        />
                      </Field>
                      <Field label="Amount *">
                        <Input
                          type="number"
                          value={newLiability.amount}
                          onChange={(e) => setNewLiability({...newLiability, amount: e.target.value})}
                          placeholder="0.00"
                          bg="white"
                          _dark={{ bg: "gray.700" }}
                        />
                      </Field>
                      <Field label="Due Date">
                        <Input
                          type="date"
                          value={newLiability.dueDate}
                          onChange={(e) => setNewLiability({...newLiability, dueDate: e.target.value})}
                          bg="white"
                          _dark={{ bg: "gray.700" }}
                        />
                      </Field>
                    </SimpleGrid>

                    <Field label="Description">
                      <Textarea
                        value={newLiability.description}
                        onChange={(e) => setNewLiability({...newLiability, description: e.target.value})}
                        placeholder="Additional details about this liability"
                        bg="white"
                        _dark={{ bg: "gray.700" }}
                        rows={3}
                      />
                    </Field>

                    <HStack gap={3} w="full" justify="end">
                      <Button variant="outline" onClick={() => {
                        setShowLiabilityForm(false);
                        setEditingLiability(null);
                        setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
                      }}>
                        Cancel
                      </Button>
                      <Button
                        colorScheme="orange"
                        onClick={handleCreateLiability}
                        disabled={loading || !newLiability.name || !newLiability.amount}
                      >
                        {loading ? (editingLiability ? 'Updating...' : 'Adding...') : (editingLiability ? 'Update Liability' : 'Add Liability')}
                      </Button>
                    </HStack>
                  </VStack>
                </Card.Body>
              </Card.Root>
            )}
          </VStack>
        </Tabs.Content>
      </Tabs.Root>

      {/* Liability Modal - Global Overlay */}
      {showLiabilityDialog && (
        <>
          {/* Backdrop */}
          <Box
            position="fixed"
            top={0}
            left={0}
            right={0}
            bottom={0}
            bg="blackAlpha.600"
            zIndex={999}
            onClick={() => {
              setShowLiabilityDialog(false);
              setEditingLiability(null);
              setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
            }}
          />

          {/* Modal */}
          <Card.Root
            position="fixed"
            top="50%"
            left="50%"
            transform="translate(-50%, -50%)"
            zIndex={1000}
            boxShadow="2xl"
            borderRadius="xl"
            border="1px solid"
            borderColor="orange.200"
            _dark={{ borderColor: "orange.700", bg: "gray.800" }}
            bg="white"
            w={{ base: "90%", md: "500px" }}
            maxH="90vh"
            overflow="auto"
          >
          <Card.Header
            bg="linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05))"
            p={6}
          >
            <HStack justify="space-between">
              <VStack align="start" gap={1}>
                <Heading size="md" color="orange.700" _dark={{ color: "orange.300" }}>
                  {editingLiability ? 'Update Liability' : 'Add New Liability'}
                </Heading>
                <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }}>
                  {editingLiability ? 'Update liability information' : 'Record a new business liability'}
                </Text>
              </VStack>
              <Button size="sm" variant="ghost" onClick={() => {
                setShowLiabilityDialog(false);
                setEditingLiability(null);
                setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
              }}>
                ×
              </Button>
            </HStack>
          </Card.Header>
          <Card.Body p={6}>
            <VStack gap={4}>
              <SimpleGrid columns={{ base: 1, md: 2 }} gap={4}>
                <Field label="Liability Name *">
                  <Input
                    value={newLiability.name}
                    onChange={(e) => setNewLiability({...newLiability, name: e.target.value})}
                    placeholder="e.g., Business Loan"
                    bg="white"
                    _dark={{ bg: "gray.700" }}
                  />
                </Field>
                <Field label="Amount *">
                  <Input
                    type="number"
                    value={newLiability.amount}
                    onChange={(e) => setNewLiability({...newLiability, amount: e.target.value})}
                    placeholder="0.00"
                    bg="white"
                    _dark={{ bg: "gray.700" }}
                  />
                </Field>
              </SimpleGrid>

              <Field label="Due Date">
                <Input
                  type="date"
                  value={newLiability.dueDate}
                  onChange={(e) => setNewLiability({...newLiability, dueDate: e.target.value})}
                  bg="white"
                  _dark={{ bg: "gray.700" }}
                />
              </Field>

              <Field label="Description">
                <Textarea
                  value={newLiability.description}
                  onChange={(e) => setNewLiability({...newLiability, description: e.target.value})}
                  placeholder="Additional details about this liability"
                  bg="white"
                  _dark={{ bg: "gray.700" }}
                  rows={3}
                />
              </Field>

              <HStack gap={3} w="full" justify="end">
                <Button variant="outline" onClick={() => {
                  setShowLiabilityDialog(false);
                  setEditingLiability(null);
                  setNewLiability({ name: '', amount: '', dueDate: '', description: '' });
                }}>
                  Cancel
                </Button>
                <Button
                  colorScheme="orange"
                  onClick={handleCreateLiability}
                  disabled={loading || !newLiability.name || !newLiability.amount}
                >
                  {loading ? (editingLiability ? 'Updating...' : 'Adding...') : (editingLiability ? 'Update Liability' : 'Add Liability')}
                </Button>
              </HStack>
            </VStack>
          </Card.Body>
        </Card.Root>
        </>
      )}
    </VStack>
  );
};

export default FinancialReports;
