"use client"
import { Text, Grid, GridItem,Card,FormatNumber,HStack, Button, Input,Flex, Stack} from '@chakra-ui/react';
import { StatHelpText, StatLabel, StatRoot, StatUpTrend, StatValueText, } from "@/components/ui/stat"
import {ActionButtons} from '@/components/pos/sale_btn'
import React from 'react';
import { FiSearch } from 'react-icons/fi';
import { InputGroup } from '../ui/input-group';
const Action = () => {

  return (
    <Grid
    templateRows="repeat(2, 1fr)"
    templateColumns="repeat(5, 1fr)"
    gap={2}
    mr={2}
  >
    <GridItem  colSpan={3} >
      <Card.Root boxShadow="xl" h={{ base: "auto", md: "11rem" }} >
            <Card.Body gap="2">
                <Card.Description>
                <HStack  justifyContent="space-between">   
                <Stack>
                <Card.Title>Today's Sales</Card.Title>
                <StatRoot>
                  <HStack >
                        <StatValueText
                          value={8456.4}
                          formatOptions={{ style: "currency", currency: "USD" }}
                        />
                        <StatUpTrend>12%</StatUpTrend>
                      </HStack>
                      <StatHelpText>since yesterday</StatHelpText>
                    </StatRoot>
                    <HStack><Text>Items Sold :</Text> <Text textStyle="lg"><FormatNumber value={1234.45} /></Text></HStack>
                  </Stack>

                  <Stack>
                  <Card.Title>Today's Target</Card.Title>
                    <StatRoot>
                      <HStack>
                        <StatValueText value={8456.4} formatOptions={{ style: "currency", currency: "USD" }} />
                      </HStack>
                      <StatHelpText>Target</StatHelpText>
                    </StatRoot>
                    <HStack><Text>Items to be Sold :</Text> <Text textStyle="lg"><FormatNumber value={1234.45} /></Text></HStack>
                  </Stack>
               </HStack>
                </Card.Description>
            </Card.Body>
      </Card.Root>
    </GridItem>
    <GridItem  colSpan={2}>
    <Card.Root boxShadow="xl" h={{ base: "auto", md: "11rem" }}>
            <Card.Body gap="2">
                <Card.Title>Inventory</Card.Title>
                <Card.Description>
                <StatRoot>
                <StatLabel>All Items</StatLabel>
                <StatValueText value={935.4}/>
              </StatRoot>
                <Text>Approximate Amount : 1234.45</Text>
                </Card.Description>
            </Card.Body>
      </Card.Root>
    </GridItem>

    <GridItem  colSpan={5}>
    <Card.Root boxShadow="xl">
            <Card.Body gap="2">
                <Card.Title mb="2" alignItems={'center'}>Widthdraw</Card.Title>
                <Card.Description>
                  <HStack justifyContent="space-between">
                    <Stack gap={5} direction="row" flex="1"> <InputGroup  startElement={<FiSearch />}> 
                       <Input  size="md" placeholder="Enter Amount to Withdraw"/>
                       </InputGroup><Button size="md">Complete</Button></Stack>

                      <StatRoot>
                        <StatLabel>Amount WithDrawn</StatLabel>
                        <HStack>
                          <StatValueText
                            value={8456.4}
                            formatOptions={{ style: "currency", currency: "USD" }}
                          />
                        </HStack>
                    </StatRoot>
                  </HStack>
                </Card.Description>
            </Card.Body>
      </Card.Root>
    </GridItem>
    <GridItem  colSpan={5}>
    <Card.Root boxShadow="xl">
            <Card.Body>
                <Card.Title mb="2" alignItems={'center'}>Menus/Shortcuts</Card.Title>
                <Card.Description>
                  <ActionButtons/>
                </Card.Description>
            </Card.Body>
      </Card.Root>
    </GridItem>
  </Grid>
  );
};

export default Action;
