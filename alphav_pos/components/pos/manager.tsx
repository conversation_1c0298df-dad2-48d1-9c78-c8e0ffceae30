"use client"

import  React,{ useState,useEffect,useCallback,useMemo,memo,Suspense } from "react";
import {FiX,FiDollarSign,FiInbox,FiGift,FiSearch,FiRefreshCw,FiPlus,FiFilter,FiUsers,FiTrendingUp ,FiShoppingCart,FiShoppingBag,FiCodesandbox,FiDownload,FiImage} from 'react-icons/fi'
import {DataList,Badge,Tag,Field,IconButton,Table,Switch,Center,Spinner,SimpleGrid, Tabs,Icon,Group,Card,Button,Box,Text,Stack,VStack,HStack,Input,Flex,createListCollection } from "@chakra-ui/react"
import {Empty} from '@/components/pos/empty'
import { PaginationItems, PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, } from "@/components/ui/pagination"
import {SaleButton} from '@/components/pos/sale_btn' 
import TabTable from '@/components/pos/pending'
import Search from '@/components/pos/search'
import { useColorModeValue } from '@/components/ui/color-mode';
import {Product,Category,User,Shop,Discount,Payment_Method,Status} from '@/app/utils/definitions'
import Image from 'next/image'
import {DialogBody,DialogCloseTrigger,DialogContent,DialogFooter,DialogHeader,DialogRoot,DialogTitle,DialogTrigger,} from "@/components/ui/dialog"
import { UserForm,ShopModal} from "@/components/pos/admin_functions";
import { motion } from "motion/react";
import {fetchData2,fetchData,insertData,updateData,deleteData2,deleteData,fetchImage } from '@/app/utils/pos';
import {MenuContent,MenuItem,MenuRoot,MenuTrigger,MenuTriggerItem} from "@/components/ui/menu"

import { toaster } from "@/components/ui/toaster"
import Table_ from '@/components/pos/Table_default1'
import {formatDate} from  '@/app/utils/actions'
import Link from 'next/link'
import {Add_payment} from '@/components/pos/invetory_functions'
import {NativeSelectField,NativeSelectRoot,} from "@/components/ui/native-select"
import Filter from '@/components/pos/filters'
import { useAppSelector } from '@/lib/hooks'

const Stock =  memo(({user}: {user:User;}) => {
  const shop_id = user?.currentshop;
  const [activeTab, setActiveTab] = useState<string>('users');
  const [AddModalOpen, setIsAddModalOpen] = useState(false);
  const [editRole, setEditRole] = useState<Role | null>(null);
  const [form, setForm] = useState({ role_name: '', role_status: '' });
  
  const [query, setQuery] = useState('');
  const currentUserRoles = useAppSelector(state => state.user.currentUser?.roles || []);
  const handleSubmit = async () => {
    try {
      if (!form.role_name.trim() || !form.role_status.trim()) {
        toaster.error({ title: 'Incomplete form.', description: 'Please fill out all required fields.' });
        return;
      }
      if (editRole) {
        await updateData('roles', editRole.role_id, form);
        toaster.success({ title: 'Role updated.',description: 'Role updated successfully.' });
      } else {
        await insertData('roles', form);
        toaster.success({title: 'Role added.',description: 'Role added successfully.' });
      }
      setIsAddModalOpen(false)
      setForm({ role_name: '', role_status: '' });
      setEditRole(null);
    } catch (e) {
      toaster.error({ title: 'Operation failed.',description: 'Operation failed.' });
    }
  };
  return (
    <SimpleGrid gap={14} w="100%" px={10}>
      <Tabs.Root defaultValue="users" variant="line" size="md" onValueChange={(value) =>setActiveTab(value.value)}>
        <Tabs.List ml={10}>
          {currentUserRoles.some(r => ['user','create_user'].includes(r.role_name)) && <Tabs.Trigger value="users"><Icon fontSize="20px" color="blue.500"><FiUsers /></Icon> Users</Tabs.Trigger>}
          {currentUserRoles.some(r => ['shop','create_shop'].includes(r.role_name)) && <Tabs.Trigger value="shops"><Icon fontSize="20px" color="blue.500"><FiCodesandbox /></Icon> Shops</Tabs.Trigger>}
          {currentUserRoles.some(r => ['categories','create_categories'].includes(r.role_name)) && <Tabs.Trigger value="categories"><Icon fontSize="20px" color="blue.500"><FiInbox /></Icon> Categories</Tabs.Trigger>}
          {currentUserRoles.some(r => ['payments','create_payments'].includes(r.role_name)) && <Tabs.Trigger value="payments"><Icon fontSize="20px" color="blue.500"><FiDollarSign /></Icon> Payments</Tabs.Trigger>}
          {currentUserRoles.some(r => ['discounts','create_discounts'].includes(r.role_name)) && <Tabs.Trigger value="discounts"><Icon fontSize="20px" color="blue.500"><FiGift /></Icon> Discounts</Tabs.Trigger>}
          {currentUserRoles.some(r => ['roles','create_roles'].includes(r.role_name)) && <Tabs.Trigger value="roles"><Icon fontSize="20px" color="blue.500"><FiUsers /></Icon> Roles</Tabs.Trigger>}
          <HStack gap={10} mb={2} ml={10}>
                    <Search
                      placeholder={`Search for "${activeTab}"`}
                      tableName={activeTab}
                      onSearch={(value) =>setQuery(value)}
                    />

                  {activeTab == 'users' && (
                        <Filter setQuery={setQuery} shop_id={shop_id}/>
                  )}
                  <Box>
                    <HStack gap={4}>
                      {activeTab == 'users' && currentUserRoles.some(r => ['create_user'].includes(r.role_name)) && (
                        <DialogRoot open={AddModalOpen} onOpenChange={(e) => setIsAddModalOpen(e.open)} closeOnEscape={true} placement="top" size="xl" motionPreset="slide-in-top" lazyMount>
                          <DialogTrigger asChild>
                            <Button colorScheme="blue" size="xs">
                              <FiPlus /> Add User
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogBody>
                              <UserForm mode="insert" user={null} onClose={() => setIsAddModalOpen(false)} />
                            </DialogBody>
                            <DialogCloseTrigger />
                          </DialogContent>
                        </DialogRoot>
                      )}

              {activeTab == 'roles' && currentUserRoles.some(r => ['create_roles'].includes(r.role_name)) && (
                        <DialogRoot open={AddModalOpen} onOpenChange={(o) => setIsAddModalOpen(o.open)}>
                        <DialogTrigger asChild>
                          <HStack justify="flex-end" width="100%">
                            <Button size="xs">
                              <FiPlus />
                              Add Role
                            </Button>
                          </HStack>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogBody>
                          <Text fontSize="md" fontWeight="bold" py={2}>Add Role</Text>
                            <VStack gap={3} align="start">
                              <Input placeholder="Role name" value={form.role_name} onChange={(e) => setForm({ ...form, role_name: e.target.value })} />
                              <Input placeholder="Role status (1 active, 0 inactive)" value={form.role_status} onChange={(e) => setForm({ ...form, role_status: e.target.value })} />
                            </VStack>
                          </DialogBody>
                          <DialogFooter>
                              <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>Close</Button>
                            <Button colorScheme="blue" onClick={handleSubmit}>{editRole ? 'Update' : 'Create'}</Button>
                        
                          </DialogFooter>
                        </DialogContent>
                      </DialogRoot>
                      )}
                      {activeTab == 'shops' && currentUserRoles.some(r => ['create_shop'].includes(r.role_name)) && (
                        <DialogRoot open={AddModalOpen} onOpenChange={(e) => setIsAddModalOpen(e.open)} closeOnEscape={true} placement="top" size="xl" motionPreset="slide-in-top" lazyMount>
                          <DialogTrigger asChild>
                            <Button colorScheme="blue" size="xs">
                              <FiPlus /> Add Shop
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogBody>
                              <ShopModal mode="insert" shop={null} onClose={() => setIsAddModalOpen(false)} />
                            </DialogBody>
                            <DialogCloseTrigger />
                          </DialogContent>
                        </DialogRoot>
                      )}
            
        
                      <Button 
                        colorScheme="blue" 
                        size="xs" 
                        onClick={() => handleDownload(activeTab)}
                      >
                        <FiDownload /> Download {activeTab}
                      </Button>
                    </HStack>
                  </Box>
            </HStack>
        </Tabs.List>
        <Stack  key={activeTab}>
          <Suspense fallback={<Center  h={'70vh'} ><Spinner size="xl" color="red.600"/></Center>}>
              {
              activeTab == 'users'?(<Tabs.Content value="users"> <ManageUsers  setQuery={(value:string)=>setQuery(value)}  mode="Users" shop_id ={shop_id} query={query}/></Tabs.Content>) :
              activeTab == 'shops'?( <Tabs.Content value="shops"> <ManageShops   setQuery={(value:string)=>setQuery(value)}  mode="Shops" query={query}/></Tabs.Content>):
              activeTab == 'categories'?(<Tabs.Content value="categories"> <Box  height="40rem" overflowY="auto"><ManageCat setQuery={(value:string)=>setQuery(value)}  mode="categories" query={query} shop_id ={shop_id}/></Box></Tabs.Content>):
              activeTab == 'payments'?(<Tabs.Content value="payments">   <Box  height="40rem" overflowY="auto"><Add_payment /> </Box></Tabs.Content>):
              activeTab == 'discounts'? (<Tabs.Content value="discounts"><ManageDis setQuery={(value:string)=>setQuery(value)}   mode="discounts" query={query} shop_id ={shop_id}/></Tabs.Content>):
              activeTab == 'roles'? (<Tabs.Content value="roles"><ManageRoles/></Tabs.Content>):
              null
              }   
          </Suspense>
      </Stack>

      </Tabs.Root>
    </SimpleGrid>
  );
});

export default Stock;


const ManageUsers = ({mode,setQuery,shop_id,query}:{mode:string;setQuery:(value:string)=>void;shop_id:string,query :string}) => {
  const [User, setUser] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  // const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentPage2, setCurrentPage2] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalRoles, setTotalRoles] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const currentUserRoles = useAppSelector(state => state.user.currentUser?.roles || []);


  const LoadTabData = useCallback(async () => {
    setIsLoading(true);

    try {
      const isAdmin = currentUserRoles.some(r => r.role_name == 'admin');
      const queryParams = {query,...(isAdmin ?   {}:{shop_id})};
      const UserResponse = await fetchData<User>('get_users',currentPage,pageSize,queryParams);
      const RolesResponse = await fetchData<Role>('roles',currentPage,pageSize,queryParams);
      const { data: UserData, metadata: UserMetadata } = UserResponse;
      const { data: RolesData, metadata: totalRoles } = RolesResponse;
      setUser(UserData);
      setTotalItems(UserMetadata.total);
      setRoles(RolesData);
      setTotalRoles(totalRoles.total);
    } catch (error) {
      toaster.error({
        title: 'Failed to load data.',
        description:'An unexpected error occurred.' + error,
      });
    } finally {
      setIsLoading(false);
      setQuery('')
    }
}, [currentPage, pageSize, query]);

      useEffect(() => {LoadTabData();},[LoadTabData]);

      const handlePageChange = (newPage: any) => {
        setCurrentPage(newPage.page);
      };
      const handlePageChange2 = (newPage: any) => {
        setCurrentPage2(newPage.page);
      };
    
      const handlePageSizeChange = (newPageSize: any) => {
        setPageSize(newPageSize.pagesize);
        setCurrentPage(1);
      };

      const deleteUsers = async (users_id: any) => {
        setIsLoading(true);
        try {
          await deleteData("auth_delete",users_id);
          setUser(prevUsers =>
            prevUsers.filter(users => users.user_id != users_id)
          );
          
        } catch (error) {
          toaster.error({ 
            title: "Failed to delete users", 
            description: error instanceof Error ? error.message : "An unexpected error occurred"
          });
        } finally {
          setIsLoading(false);
        }
    };
  
      const toggleRow = (trans_id: string) => {
        setExpandedRows((prev) => {
          const isCurrentlyExpanded = prev[trans_id];
          if (isCurrentlyExpanded) return {};
          return { [trans_id]: true };
        });
      };
      
      const columns = [
        { label: 'User id', key: 'user_id', align: 'left',render: (user_id) => user_id.slice(0, 8) + '...'},
        { label: 'Username', key: 'username', align: 'center' },
        { label: 'Names', key: 'last_names', align: 'center' },
        { label: 'Identification', key: 'identification', align: 'center' },
        { label: 'Verified', key: 'is_verified', align: 'center',render: (is_verified) => is_verified ?'✅ Yes' : '❌ No' },
        { label: 'Status', key: 'user_status', align: 'center',render: (user_status) => user_status?.status_name || 'Undefined' },
        { label: 'Email', key: 'email', align: 'center' },
        { label: 'Created', key: 'created_at', align: 'center',render: (created_at) => formatDate(created_at)},
        { label: 'Updated', key: 'updated_at', align: 'center',render: (updated_at) => formatDate(updated_at)},
        { label: 'Action', key: 'actions', align: 'center' }
      ];
    
      const getActions = (item) => [
        {label: 'View',onClick: () => toggleRow(item.user_id)},
        {label: 'Delete',colorScheme: 'red',onClick: () => deleteUsers(item.user_id)},
      ];


      const renderRowDetails = (item) =>{
        const totalPages = Math.ceil(totalRoles / pageSize);
        const validCurrentPage = Math.max(1, Math.min(currentPage2, totalPages));
        const activeRoleIds = item.roles?.map((role) => role.role_id) || [];
        const [AddModalOpen, setIsAddModalOpen] = useState(false);
 
        const handleRoleToggle = async (roleId, isActive) => {
            try {
                const currentRoles = item.roles.map(role => role.role_id);
                let updatedRoles;
                if (isActive) {
                    updatedRoles = [...new Set([...currentRoles, roleId])];
                } else {
                    updatedRoles = currentRoles.filter(id => id !== roleId);
                }
                const response = await updateData('auth_update', item.user_id, { role_ids: updatedRoles });
                if (response.success) {
                    setUser((prevUsers) =>prevUsers.map((user) => user.user_id == item.user_id? { ...user, roles: updatedRoles.map(id => ({ role_id: id })) }: user));
                } else {
                    toaster.error({title: 'Failed to update role.',description: response.message || 'An unexpected error occurred.',});
                }
            } catch (error) {
                toaster.error({
                    title: 'Failed to update role.',
                    description: 'An unexpected error occurred.',
                });
            }
        };
        const handleItemAdded = () => {
          // setAdd(prev => !prev);
        };


    
        
        return (
        <HStack gap={4} align="start">
         <HStack align="start" gap={6} flex={1}>
              <Box maxH="12rem" overflowY="auto" w="100%" p={3} borderRadius="xl" boxShadow="lg" >
                  <VStack  align="start" gap={3}>
                        <Text fontWeight="bold">User Details</Text>
                        <HStack><Text fontWeight="semibold">Names:</Text><Text> {item.first_names +'  ' +item.last_names}</Text></HStack>
                        <HStack><Text fontWeight="semibold">Email:</Text><Text> {item.email}</Text></HStack>
                        <HStack><Text fontWeight="semibold">Username:</Text><Text> {item.username}</Text></HStack>
                
                        <Stack>
                            <DialogRoot 
                            open={AddModalOpen} 
                            onOpenChange={(e) => setIsAddModalOpen(e.open)}  
                            closeOnEscape={true}
                            placement='top'size="xl"motionPreset="slide-in-top"lazyMount={true}preventScroll>
                                    <DialogTrigger asChild>
                                      <Button colorPalette="blue" size={'xs'}>
                                        <FiRefreshCw /> Update User
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent>
                                      <DialogBody>
                                        <UserForm mode="update" user={item} onSubmit={handleItemAdded} onClose={() => setIsAddModalOpen(false)} />
                                      </DialogBody>
                                      <DialogCloseTrigger />
                                    </DialogContent>
                                </DialogRoot>
                          </Stack>
                    </VStack>
              </Box>
              <HStack gap={4} w="100%">
              <Box maxH="12rem" overflowY="auto" w="100%" px={3} py={4} borderRadius="xl" boxShadow="lg">
                  <HStack gap={5}>
                    <Stack>
                          <Text fontWeight="semibold">Shops</Text>
                          {item.shops && item.shops.length > 0 ? (
                            <DataList.Root orientation="vertical" size="md" divideY="1px" maxW="md">
                              {item.shops.map((shop,index) => (
                                <Stack key={shop.shop_id}>
                                  <DataList.Item>
                                    <DataList.ItemLabel>{shop.shop_name || "No shop"}</DataList.ItemLabel>
                                    <DataList.ItemValue>{shop.shop_owner_names || "N/A"}</DataList.ItemValue>
                                    <DataList.ItemValue>{shop.shop_id || "N/A"}</DataList.ItemValue>
                                    <DataList.ItemValue color="blue.500">
                                      <Link href={shop.shop_website || "#"}>{shop.shop_website || "N/A"}</Link>
                                    </DataList.ItemValue>
                                  </DataList.Item>
                                </Stack>
                              ))}
                            </DataList.Root>
                          ) : (
                            'No shops yet'
                          )}
                    </Stack>
                  </HStack>
                </Box>


              </HStack>
            </HStack>
         
    
            <VStack align="start" gap={2} flex={1}>
                <Text fontWeight="bold">Roles</Text>
                <Box maxH="15rem" overflowY="auto" w="100%" borderRadius="xl" boxShadow="lg" p={2}>
                  <Table.Root size="sm">
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Name</Table.ColumnHeader>
                        <Table.ColumnHeader>Status</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {roles.map((role) => (
                        <Table.Row key={role.role_id}>
                          <Table.Cell>{role.role_name?.toLowerCase()}</Table.Cell>
                          <Table.Cell>
                            <Switch.Root
                              size="md"
                              colorPalette="cyan"
                              checked={activeRoleIds.includes(role.role_id)}
                              onChange={(e) => handleRoleToggle(role.role_id, e.target.checked)}
                            >
                              <Switch.HiddenInput />
                              <Switch.Control>
                                <Switch.Thumb />
                              </Switch.Control>
                              <Switch.Label />
                            </Switch.Root>
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table.Root>
                  {/* <PaginationRoot
                    onPageChange={handlePageChange2}
                    onPageSizeChange={handlePageSizeChange}
                    page={validCurrentPage}
                    count={totalItems}
                    pageSize={pageSize}
                    defaultPage={1}
                    size="sm"
                  > 
                    <HStack justify="space-between" align="center">
                      <PaginationPageText />
                      <Flex justify="center" align="center" gap={2}>
                        <PaginationPrevTrigger />
                        <PaginationItems />
                        <PaginationNextTrigger />
                      </Flex>
                    </HStack>
                  </PaginationRoot> */}
                </Box>
              </VStack>
        </HStack>
      )}



      
  return (
          <Table_
              columns={columns}
              data={User.map((item) => ({ ...item, isExpanded: expandedRows[item.user_id] || false }))}
              currentPage={currentPage}
              totalItems={totalItems}
              pageSize={pageSize}
              handlePageChange={handlePageChange}
              handlePageSizeChange={handlePageSizeChange}
              getActions={getActions}
              isLoading={isLoading}
              width="100%"
              height="100%"
              mode={mode}
              renderRowDetails={renderRowDetails}
        />
  );
};

const ManageShops = ({ mode,setQuery,query }: { mode: string,setQuery:(value:string)=>void,query:string }) => {
  const [shops, setShops] = useState([]);
  // const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const LoadTabData = useCallback(async () => {
    setIsLoading(true);
    const queryParams = { query, page: currentPage, pageSize };
    try {
      const shopsResponse = await fetchData<User>('shop',currentPage,pageSize,queryParams);
      const { data: shopsData, metadata: shopsMetadata } = shopsResponse;
      setShops(shopsData);
      setTotalItems(shopsMetadata.total);
    } catch (error) {
      toaster.error({
        title: "Failed to load data.",
        description: "An unexpected error occurred." + error,
      });
    } finally {
      setIsLoading(false);
      setQuery('')
    }
  }, [currentPage, pageSize, query]);

  useEffect(() => {
    LoadTabData();
  }, [LoadTabData]);

  const handlePageChange = (newPage: any) => {
    setCurrentPage(newPage.page);
  };

  const handlePageSizeChange = (newPageSize: any) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  // const toggleRow = (shop_id: string) => {
  //   setExpandedRows((prev) => ({
  //     ...prev,
  //     [shop_id]: !prev[shop_id],
  //   }));
  // };
  const toggleRow = (shop_id: string) => {
    setExpandedRows((prev) => {
      const isCurrentlyExpanded = prev[shop_id];
      if (isCurrentlyExpanded) return {};
      return { [shop_id]: true };
    });
  };
  


  const handleStatusUpdate = async (shop_id: string, isActive: boolean) => {
    try {
      setIsLoading(true);
      const newStatus = isActive ? 16 : 17;
      const response = await updateData('shop', shop_id, { shop_status: newStatus });
      if (response) {
        setShops((prevShops) => prevShops.map((shop) =>shop.shop_id == shop_id ? { ...shop, shop_status: newStatus } : shop));
        toaster.success({ title: 'Success', description: `Shop status updated to ${isActive ? 'active' : 'inactive'}.`, });
      }
    } catch (error) {
      toaster.error({ title: 'Failed to update status.', description: 'An unexpected error occurred.' + error,});
    } finally {
      setIsLoading(false);
    }
  };

  const deleteShops = async (shop_id: any) => {
    setIsLoading(true);
    try {
      await deleteData("shop",shop_id);
      setShops(prevShops =>
        prevShops.filter(shops => shops.shop_id != shop_id)
      );
      
    } catch (error) {
      console.log(error)
      toaster.error({ 
        title: "Failed to delete shop", 
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsLoading(false);
    }
};

  const columns = [
    { label: "Shop ID", key: "shop_id", align: "left", render: (shop_id) => shop_id.slice(0, 8) + "..." },
    { label: "Shop Name", key: "shop_name", align: "left" },
    { label: "Owner Names", key: "shop_owner_names", align: "left" },
    { label: "Location", key: "shop_location_name", align: "left" },
    { label: "Status", key: "status", align: "center", render: (status) => status.status_name || "Undefined" },
    { label: "Action", key: "actions", align: "center" },
  ];

  const getActions = (item) => [
    {
      label: "View",
      onClick: () => toggleRow(item.shop_id),
    },
    {
      label: "Delete",

      colorScheme: "red",
      onClick: () => deleteShops(item.shop_id),
    },
  ];

  const renderRowDetails = (item) => {
    const [AddModalOpen, setIsAddModalOpen] = useState(false);
    const handleItemAdded = () => {
      // setAdd(prev => !prev);
    };
 
    return (
      <HStack gap={4} align="start">
         <HStack align="start" gap={6} flex={1}>
         <Box maxH="12rem" overflowY="auto" w="100%" p={3} borderRadius="xl" boxShadow="lg">
            <HStack>
                <VStack align="start">
                <Text fontWeight="bold">Shop details</Text>
                    <HStack>
                      <Text fontWeight="semibold">Name:</Text>
                      <Text>{item.shop_name}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="semibold">Owner:</Text>
                      <Text>{item.shop_owner_names}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="semibold">Location:</Text>
                      <Text>{item.shop_location_name}</Text>
                    </HStack>
                    <HStack>
                      <Text fontWeight="semibold">Address:</Text>
                      <Text>{item.shop_location_address}</Text>
                    </HStack> 
                    <Stack>
                      <DialogRoot 
                      open={AddModalOpen} 
                      onOpenChange={(e) => setIsAddModalOpen(e.open)}  
                      closeOnEscape={true}
                      placement='top'size="xl"motionPreset="slide-in-top"lazyMount={true}preventScroll>
                              <DialogTrigger asChild>
                                <Button colorPalette="blue" size={'xs'}>
                                  <FiRefreshCw /> Update Shop
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogBody>
                                  <ShopModal mode="update" shop={item} onAddShop={handleItemAdded} onClose={() => setIsAddModalOpen(false)} />
                                </DialogBody>
                                <DialogCloseTrigger />
                              </DialogContent>
                          </DialogRoot>
                            </Stack>
                  </VStack>
            </HStack>
            <HStack>   
              
            </HStack>
            </Box>
          </HStack>
          <VStack gap={4} align="start" flex={1}>
              <Text fontWeight="bold">Contact details</Text>
                {item.shop_website &&
                  <HStack>
                    <Box maxH="15rem" overflowY="auto" w="100%" borderRadius="xl" boxShadow="lg" p={2}>
                    <Link href={item.shop_website} color="blue.500">
                        {item?.shop_website || "N/A"}
                      </Link>
                    </Box>
                  </HStack>
                }
                 <Stack>
             

                    <HStack align="start">
                <Text fontWeight="semibold">Email:</Text>
                <VStack align="start">
                  {item.shop_email.map((email, index) => (
                    <Text key={index}>{email}</Text>
                  ))}
                </VStack>
              </HStack>
              <HStack align="start">
                <Text fontWeight="semibold">Phone:</Text>
                <VStack align="start">
                  {item.shop_phone.map((phone, index) => (
                    <Text key={index}>{phone}</Text>
                  ))}
                </VStack>
              </HStack>
                </Stack>
                <Text fontWeight="bold">Shop Status</Text>
                <Switch.Root
                  size="md"
                  colorPalette="cyan"
                  checked={item.shop_status == 16}
                  onChange={(e) => handleStatusUpdate(item.shop_id, e.target.checked)}
                >
                  <Switch.HiddenInput />
                  <Switch.Control>
                    <Switch.Thumb />
                  </Switch.Control>
                  <Switch.Label />
                </Switch.Root>
              </VStack>
      </HStack>
    );
  };

  return (
    <Table_
      columns={columns}
      data={shops?.map((item) => ({ ...item, isExpanded: expandedRows[item.shop_id] || false }))}
      currentPage={currentPage}
      totalItems={totalItems}
      pageSize={pageSize}
      handlePageChange={handlePageChange}
      handlePageSizeChange={handlePageSizeChange}
      getActions={getActions}
      isLoading={isLoading}
      width="100%"
      height="100%"
      mode={mode}
      renderRowDetails={renderRowDetails}
    />
  );
};

const ManageCat = ({ mode, setQuery, query,shop_id }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [Loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [newSubcategory, setNewSubcategory] = useState("");
  const [isAdding, setIsAdding] = useState(false);
  const [inputSubcategories, setInputSubcategories] = useState<Array<{id: string, name: string}>>([]);

  const handleAddSubcategory = (category_id: string) => {
    if (!newSubcategory.trim()) return;
    setInputSubcategories(prev => [...prev, { 
      id: Date.now().toString(), 
      name: newSubcategory.trim() 
    }]);
    setNewSubcategory("");
  };

  const handleSaveSubcategories = async (category_id: string) => {
    if (inputSubcategories.length == 0) return;
    setIsAdding(true);
    setLoading(true);
    try {
      const resp = await updateData("category", category_id, { 
        sub_category: [
          ...categories.find(c => c.category_id == category_id)?.sub_category || [],
          ...inputSubcategories.map(s => s.name)
        ]
      });
      setCategories(prev => prev.map(cat =>
        cat.category_id == category_id
          ? { ...cat, sub_category: resp.sub_category }
          : cat
      ));
      setInputSubcategories([]);
      toaster.success({ title: "Sub-category updated successfully" });
    } catch (error) {
      toaster.error({
        title: "Failed to update subcategories",
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsAdding(false);
      setLoading(false);
    }
  };


  const handleRemoveChip = (id: string) => {
    setInputSubcategories(prev => prev.filter(item => item.id !== id));
  };

  const LoadTabData = useCallback(async () => {
    setIsLoading(true);
    try {
      const categoriesResponse = await fetchData2<Category>("categories/shop", currentPage, pageSize, { query },shop_id);
      setCategories(categoriesResponse.data);
      setTotalItems(categoriesResponse?.metadata?.total);
    } catch (error) {
      console.log(error)
      toaster.error({ title: "Failed to load data.", description: "An unexpected error occurred. " + error });
    } finally {
      setIsLoading(false);
      setQuery("");
    }
  }, [currentPage, pageSize, query]);

  useEffect(() => {
    LoadTabData();
  }, [LoadTabData]);

  const deleteSubcategory = async (category_id: any,index:any) => {
      setLoading(true);
      try {
        await deleteData2("categories/remove-subcategory", {
          category_id,
          subCategoryIndex: index
        });
        setCategories(prevCategories => 
          prevCategories.map(category => 
            category.category_id == category_id
              ? {
                  ...category,
                  sub_category: category.sub_category.filter((_, i) => i !== index)
                }
              : category
          )
        );
      } catch (error) {
        toaster.error({ 
          title: "Failed to delete subcategory", 
          description: error instanceof Error ? error.message : "An unexpected error occurred"
        });
      } finally {
        setLoading(false);
        LoadTabData();
      }
  };

  const deleteCategory = async (category_id: any) => {
    setLoading(true);
    try {
      await deleteData("category",category_id);
      setCategories(prevCategories =>
        prevCategories.filter(category => category.category_id !== category_id)
      );
      
    } catch (error) {
      toaster.error({ 
        title: "Failed to delete subcategory", 
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
};

  const toggleRow = (category_id) => setExpandedRows((prev) => ({ [category_id]: !prev[category_id] }));

  const columns = [
    { label: "ID", key: "category_id", align: "left" },
    { label: "Name", key: "category_name", align: "left" },
      { 
        label: 'Updated', 
        key: 'updated_at', 
        align: 'center',
        render: (updated_at) => formatDate(updated_at)
      },
    { label: "Action", key: "actions", align: "center" },
  ];

  const getActions = (item) => [{ label: "View", onClick: () => toggleRow(item.category_id) }];

  const renderRowDetails = (item) => (
      <HStack gap={4} justify="space-between"  align="start" >

          <Box w="100%" p={3} borderRadius="xl" boxShadow="lg">
            <Text fontWeight="bold">Subcategories</Text>
            {item.sub_category?.length > 0 ? (
              item.sub_category.map((sub, index) => (
                <HStack key={index}  w="full" justify="space-between">
                <Text textStyle="md">{sub}</Text>
                <IconButton
                      aria-label="Delete subcategory"
                      size="xs"
                      variant="ghost"
                      colorScheme="red"
                      onClick={() => deleteSubcategory(item.category_id,index)}
                      _hover={{ bg: "red.50" }}
                      disabled={Loading}
                    >
                      <motion.span
                        animate={{ rotate: Loading ? 360 : 0 }}
                        transition={{
                          duration: 1,
                          repeat: Loading ? Infinity : 0,
                          ease: "linear",
                        }}
                        style={{ display: "inline-flex" }} 
                      >
                        <FiX />
                      </motion.span>
                    </IconButton>
                </HStack>
              ))
            ) : (
              <Text>No subcategories found.</Text>
            )}
          </Box>

          <Box w="100%" p={3} borderRadius="xl" boxShadow="lg">
              <Box display="flex" justifyContent="flex-end" cursor="pointer">
                <VStack>
                <IconButton
                      aria-label="Delete category"
                      size={'xl'}
                      variant="ghost"

                      colorScheme="red"
                      onClick={() => deleteCategory(item.category_id)}
                      _hover={{ bg: "red.50",cursor: "pointer" }}
                      disabled={Loading}
                    >
                      <motion.span
                        animate={{ rotate: Loading ? 360 : 0 }}
                        transition={{
                          duration: 1,
                          repeat: Loading ? Infinity : 0,
                          ease: "linear",
                        }}
                        style={{ display: "inline-flex" }} 
                      >
                        <FiX />
                      </motion.span>
                    </IconButton>
                <Text textStyle="xs">Delete category</Text>
                </VStack>
              </Box>

              <HStack flexWrap="wrap" gap={2} mb={2}>
                    {inputSubcategories.map((sub) => (
                    <Tag.Root key={sub.id} variant="solid" colorPalette="blue">
                        <Tag.Label>{sub.name}</Tag.Label>
                        <Tag.EndElement>
                          <Tag.CloseTrigger  onClick={() => handleRemoveChip(sub.id)} />
                        </Tag.EndElement>
                      </Tag.Root>
                    ))}
                </HStack>
          <Field.Root>
            <Field.Label>Add Sub categories</Field.Label>
          
              <Input
                placeholder="Type and press Enter"
                value={newSubcategory}
                onChange={(e) => setNewSubcategory(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key == 'Enter' && newSubcategory.trim()) {
                    handleAddSubcategory(item.category_id);
                  }
                }}
              />
          
          </Field.Root>
          
          {/* <Button
            mt={3}
            colorPalette="blue"
          
            // loading={isAdding}
            disabled={inputSubcategories.length == 0}
            // disabled={!newSubcategory.trim() && inputSubcategories.length == 0}
          >
            Save All Subcategories
          </Button> */}

          <Button  mt={3} colorPalette="blue" size={'xs'}   onClick={() => handleSaveSubcategories(item.category_id)}   disabled={inputSubcategories.length == 0}><FiRefreshCw /> Update category</Button>
        </Box>
      </HStack>
  );

  return (
    <Table_
      columns={columns}
      data={categories.map((item) => ({ ...item, isExpanded: expandedRows[item.category_id] || false }))}
      currentPage={currentPage}
      totalItems={totalItems}
      pageSize={pageSize}
      handlePageChange={(newPage) => setCurrentPage(newPage.page)}
      handlePageSizeChange={(newPageSize) => setPageSize(newPageSize.pagesize)}
      getActions={getActions}
      isLoading={isLoading}
      mode={mode}
      renderRowDetails={renderRowDetails}
    />
  );
};

const ManageDis= ({ mode, setQuery, query,shop_id }) => {
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [Loading,setLoading] =useState(false)
  const LoadTabData = useCallback(async () => {
    setIsLoading(true);
    try {
      const discountsResponse = await fetchData2<Discount>("discount/shop", currentPage, pageSize, { query },shop_id);
      setDiscounts(discountsResponse.data);
      setTotalItems(discountsResponse.metadata.total);
    } catch (error) {
      toaster.error({ title: "Failed to load data.", description: "An unexpected error occurred. " + error });
    } finally {
      setIsLoading(false);
      setQuery("");
    }
  }, [currentPage, pageSize, query]);

  useEffect(() => {
    LoadTabData();
  }, [LoadTabData]);

  const toggleRow = (discount_id) => setExpandedRows((prev) => ({ [discount_id]: !prev[discount_id] }));

  const columns = [
    { label: "Discount ID", key: "discount_id", align: "left" },
    { label: "Discount Name", key: "discount_name", align: "left" },
    { 
      label: "Discount Type", 
      key: "discount_type", 
      align: "left",
      render: (item) => (item == 0 ? "Percent" : "Float")
    },
    { label: "Discount Amount", key: "discount_amount", align: "right"},
    { label: "Action", key: "actions", align: "center" },
  ];

  const getActions = (item) => [{ label: "View", onClick: () => toggleRow(item.discount_id) }];

  const handleTypeChange = (discountId, newType) => {
    setDiscounts((prev) =>prev.map((d) =>d.discount_id == discountId ? { ...d, discount_type: parseInt(newType) } : d));
  };
  
  const handleAmountChange = (discountId, newAmount) => {
    setDiscounts((prev) =>prev.map((d) => d.discount_id == discountId ? { ...d, discount_amount: newAmount } : d));
  };
  const deleteDis = async (discount_id:any)=>{
    setLoading(true);
      try {
        await deleteData("discount",discount_id);
        setDiscounts(prevCategories =>
          prevCategories.filter(discount => discount.discount_id !== discount_id)
        );
        toaster.success({ title: "Discount deleted successfully" });  
      } catch (error) {
        toaster.error({ 
          title: "Failed to delete discount (update all products with the discount first)", 
          description: error instanceof Error ? error.message : "An unexpected error occurred"
        });
      } finally {
        setLoading(false);
      }
  }
  const handleSaveDiscounts = async (item: any) => {
    if (!item || Object.keys(item).length == 0) return;
    setLoading(true);
    try {
      const resp = await updateData("discount", item.discount_id, item);
      setDiscounts((prev) =>
        prev.map((dis) =>
          dis.discount_id == item.discount_id
            ? { ...dis, ...resp.data }
            : dis
        )
      );
      toaster.success({ title: "Discount updated successfully" });
    } catch (error) {
      toaster.error({
        title: "Failed to update discounts ",
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };
  

  const discount_type = [
    { discount_type_id: '0', discount_type_name: 'Percent' },
    { discount_type_id: '1', discount_type_name: 'Float' }
  ];
  
  const renderRowDetails = (item) => (
    <HStack gap={4} justify="space-between"  align="start" >
         <Box w="100%" p={3} borderRadius="xl" boxShadow="lg">

          <VStack align="start" gap={3}>
              <Text fontWeight="bold">Discount Details</Text>
              <Text><b>Shop ID:</b> {item.shop_id}</Text>
              <Text><b>Type:</b> {item.discount_type == 0 ? "Percent" : "Float"}</Text>
              <Text><b>Amount:</b> {item.discount_amount} {item.discount_type == 0 && "%" }</Text>
          
          </VStack>
          </Box>

    <Box w="100%" p={3} borderRadius="xl" boxShadow="lg">
    <Text fontWeight="bold">Edit Discount Details</Text>
          <Box display="flex" justifyContent="flex-end" cursor="pointer">
                      <VStack>
                      <IconButton
                            aria-label="Delete discount"
                            size={'xl'}
                            variant="ghost"

                            colorScheme="red"
                            onClick={() => deleteDis(item.discount_id)}
                            _hover={{ bg: "red.50",cursor: "pointer" }}
                            disabled={Loading}
                          >
                            <motion.span
                              animate={{ rotate: Loading ? 360 : 0 }}
                              transition={{
                                duration: 1,
                                repeat: Loading ? Infinity : 0,
                                ease: "linear",
                              }}
                              style={{ display: "inline-flex" }} 
                            >
                              <FiX />
                            </motion.span>
                          </IconButton>
                      <Text textStyle="xs">Delete Discount</Text>
                      </VStack>
                    </Box>
        <VStack gap={3} align={'start'}>
              <Stack direction={'row'}>
                <Text>Amount:</Text>
                <Input
                  type="number"
                  value={item.discount_amount}
                  onChange={(e) => handleAmountChange(item.discount_id, e.target.value)}
                />
              </Stack>
              <Stack direction={'row'} width="20rem" align={'start'}>
                <Text>Type:</Text>
                  <NativeSelectRoot size="md">
                      <NativeSelectField
                        name="discount_type"
                        value={item.discount_type}
                        onChange={(e) => handleTypeChange(item.discount_id,e.target.value)}>
                        {discount_type.map((discount) => (
                          <option key={discount.discount_type_id} value={discount.discount_type_id}>
                            {discount.discount_type_name}
                          </option>
                        ))}
                      </NativeSelectField>
                    </NativeSelectRoot>
              </Stack>
              <Button colorPalette="blue" disabled={Loading} size={'xs'} onClick={() => handleSaveDiscounts(item)}><FiRefreshCw  onClick={() => handleSaveDiscounts(item)}/>{Loading ? 'Updating ...':'Update Discount' }</Button>
            </VStack>
            </Box>
    </HStack>
  );

  return (
    <Table_
      columns={columns}
      data={discounts.map((item) => ({ ...item, isExpanded: expandedRows[item.discount_id] || false }))}
      currentPage={currentPage}
      totalItems={totalItems}
      pageSize={pageSize}
      handlePageChange={(newPage) => setCurrentPage(newPage.page)}
      handlePageSizeChange={(newPageSize) => setPageSize(newPageSize.pagesize)}
      getActions={getActions}
      isLoading={isLoading}
      mode={mode}
      renderRowDetails={renderRowDetails}
    />
  );
};

// const ManagePay = ({ mode, setQuery, query }) => {
//   const [payments, setPayments] = useState<Payment_Method[]>([]);
//   const [isLoading, setIsLoading] = useState(false);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [pageSize, setPageSize] = useState(10);
//   const [totalItems, setTotalItems] = useState(0);
//   const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

//   const LoadTabData = useCallback(async () => {
//     setIsLoading(true);
//     try {
//       const paymentsResponse = await fetchData<Payment_Method>("paymeth", currentPage, pageSize, { query });
//       setPayments(paymentsResponse.data);
//       setTotalItems(paymentsResponse.metadata.total);
//     } catch (error) {
//       toaster.error({ title: "Failed to load data.", description: "An unexpected error occurred. " + error });
//     } finally {
//       setIsLoading(false);
//       setQuery("");
//     }
//   }, [currentPage, pageSize, query]);

//   useEffect(() => {
//     LoadTabData();
//   }, [LoadTabData]);

//   const toggleRow = (payment_method_id) => setExpandedRows((prev) => ({ [payment_method_id]: !prev[payment_method_id] }));

//   const columns = [
//     { label: "Method ID", key: "payment_method_id", align: "left" },
//     { label: "Name", key: "payment_method_name", align: "left" },
//     { label: "Description", key: "payment_method_description", align: "left" },
//     { label: "Code/Number", key: "payment_number", align: "right" },
//     { label: "Status", key: "status", align: "center" },
//     { label: "Action", key: "actions", align: "center" },
//   ];

//   const getActions = (item) => [{ label: "View", onClick: () => toggleRow(item.payment_method_id) }];

//   const renderRowDetails = (item) => (
//     <VStack gap={4} align="start">
//       <Box w="100%" p={3} borderRadius="xl" boxShadow="lg">
//         <Text fontWeight="bold">Payment Method Details</Text>
//         <Text><b>Shop ID:</b> {item.shop_id}</Text>
//         <Text><b>Description:</b> {item.payment_method_description}</Text>
//         <Text><b>Number:</b> {item.payment_number}</Text>
//       </Box>
//     </VStack>
//   );

//   return (
//     <Table_
//       columns={columns}
//       data={payments.map((item) => ({ ...item, isExpanded: expandedRows[item.payment_method_id] || false }))}
//       currentPage={currentPage}
//       totalItems={totalItems}
//       pageSize={pageSize}
//       handlePageChange={(newPage) => setCurrentPage(newPage.page)}
//       handlePageSizeChange={(newPageSize) => setPageSize(newPageSize.pagesize)}
//       getActions={getActions}
//       isLoading={isLoading}
//       mode={mode}
//       renderRowDetails={renderRowDetails}
//     />
//   );
// };


interface Role { 
  role_id: string; 
  role_name: string; 
  role_status: string; 
}

const ManageRoles: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  const load = async () => {
    setLoading(true);
    try {
      const res = await fetchData2<Role>('roles', currentPage, pageSize, {});
      setRoles(res.data || []);
      setTotalItems(res.metadata.total);
    } catch (e) {
      toaster.error({ title: "Failed to load data.", description: "An unexpected error occurred." });
    } finally { 
      setLoading(false); 
    }
  };

  useEffect(() => { 
    load(); 
  }, [pageSize, currentPage]);

  const handleDelete = async (id: string) => {
    try {
      await deleteData2('roles', id);
      toaster.success({ title: 'Role deleted.' });
      load();
    } catch (e) {
      toaster.error({ title: "Failed to delete role.", description: "An unexpected error occurred." });
    }
  };

  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      await updateData('roles', id, { role_status: newStatus });
      toaster.success({ title: `Role status updated to ${newStatus}.` });
      load();
    } catch (e) {
      toaster.error({ title: "Failed to update role status.", description: "An unexpected error occurred." });
    }
  };

  const columns = [
    { label: 'Role', key: 'role_name', align: 'left' },
    { 
      label: 'Status', 
      key: 'role_status', 
      align: 'center',
      render: (status: string) => (
        <Badge colorScheme={status == '1' ? 'green' : 'red'}>
          {status == '1' ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    { label: 'Actions', key: 'actions', align: 'center' },
  ];

  const getActions = (item: Role) => [
    { 
      label: item.role_status == '1' ? 'Disable' : 'Enable',
      colorScheme: item.role_status == '1' ? 'orange' : 'green',
      onClick: () => handleStatusChange(item.role_id, item.role_status == '1' ? '0' : '1')
    },
    { 
      label: 'Delete', 
      colorScheme: 'red', 
      onClick: () => handleDelete(item.role_id) 
    }
  ];

  return (
    <Box>
      <Table_
        columns={columns}
        data={roles}  
        currentPage={currentPage}
        totalItems={totalItems}
        pageSize={pageSize}
        handlePageChange={(newPage) => setCurrentPage(newPage.page)}
        handlePageSizeChange={(newPageSize) => setPageSize(newPageSize.pagesize)}
        getActions={getActions}
        isLoading={isLoading}
        width="100%"
        height="auto"
        mode="roles"
        renderRowDetails={() => null}
      />
    </Box>
  );
};