
import  React,{useEffect,memo} from "react";
import { For,Center,Spinner,SimpleGrid, Tabs,Icon,Group,Card,Button,Box,Text,Stack,VStack,HStack,Input,Flex,createListCollection } from "@chakra-ui/react"
import {Empty} from '@/components/pos/empty'
import {DialogBody,DialogCloseTrigger,DialogContent,DialogFooter,DialogHeader,DialogRoot,DialogTitle,DialogTrigger,} from "@/components/ui/dialog"
import Payment from '@/components/pos/payment'
import { LuShoppingBag,LuCircleCheckBig} from "react-icons/lu"
import { EmptyState } from "@/components/ui/empty-state";
import { Transaction} from '@/app/utils/definitions'
import { motion } from "motion/react";

var pending_status = 13;
var cancle_status=14;
var paid_status=15;

interface ViewpayProps {
  shop_id:string;
  transaction: Transaction; 
  isOpen: boolean;
  onClose: () => void; 
  mode:any; 
  onBuy: (status:number,paymentMethod:string,phoneNumber:string,Total_amount:any) => void;
  Total_amount:any
}

const ViewPayment =  memo(({shop_id,transaction,isOpen, onClose, mode, onBuy,Total_amount}: ViewpayProps) => {
  const MotionText= motion.create(Text);
    useEffect(() => {
      if (isOpen) {
        if (mode == 2) {
          const timer = setTimeout(() => {onClose();}, 1000);
          onBuy(pending_status,null,null,null) 
          return () => clearTimeout(timer); 
        }
      }
    }, [mode,isOpen]);

    const handleConfirmCancle = () => { 
      onClose();
      onBuy(cancle_status,null,null,null) 
    };
  
    return (
      <DialogRoot
        open={isOpen}
        onOpenChange={(e) => {if (!e.open) onClose();}} 
        placement={mode == 1 ? "top" : "center"}
        size="md" motionPreset="slide-in-top" 
        preventScroll >
        <DialogContent>
          <DialogBody>
            {
            mode == 1 ? (
              <Payment shop_id={shop_id} transaction={transaction} onClose={onClose} onBuy={onBuy} paid_status={paid_status} Total_amount={Total_amount}/>
            ) :
             mode == 3 ? (
                <VStack gap={4} align="center">
                  <Text fontSize="md" fontWeight="bold"> Confrim</Text>
                  <Text> Are you sure you want to cancle this transaction?</Text>
                  <HStack gap={2}>
                    <Button colorScheme="blue" onClick={handleConfirmCancle}>Confirm</Button>
                    <Button variant="outline" onClick={() => onClose()}>Cancel</Button>
                  </HStack>
                </VStack>
         
            ) : mode == 2 ? (
              <VStack gap={4} align="center">
                <Center>
                <EmptyState
                title={"Marking as pending"} 
                description={
                      <MotionText 
                      textStyle="7xl" 
                      color="green.500" 
                      style={{ display: 'inline-block' }} 
                      animate={{ opacity: [1, 0, 1] }} 
                      transition={{ duration: 1.5, repeat: Infinity }} 
                    >...</MotionText> } ><LuCircleCheckBig /></EmptyState>
                </Center>
              </VStack>
            ) : null}
          </DialogBody>
          <DialogCloseTrigger />
        </DialogContent>
      </DialogRoot>
    );
  });

  export default ViewPayment;