import React from 'react';
import { HStack, Input, Button } from '@chakra-ui/react';
import { FiSearch } from 'react-icons/fi';
import { InputGroup } from "@/components/ui/input-group"

interface SearchProps {
  placeholder?: string;
  tableName?: string;
  onSearch?: (value: string, tableName?: string) => void;
}

const Search: React.FC<SearchProps> = ({ placeholder = "Search...", tableName, onSearch }) => {
  const [searchValue, setSearchValue] = React.useState("");

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchValue, tableName);
    }
  };

  return (
    <HStack p={2}>
      <InputGroup flex="1" startElement={<FiSearch />} >
        <Input placeholder={placeholder} size="xs"  value={searchValue} onChange={(e) => setSearchValue(e.target.value)}/>
      </InputGroup>
      <Button   size="xs" onClick={handleSearch} variant="plain" >Search</Button>
    </HStack>
  );
};

export default Search;
