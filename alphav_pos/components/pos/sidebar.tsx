"use client";
import { F<PERSON>, Text, HStack, Box, VStack, Skeleton, Icon, IconButton, Button, Spinner } from "@chakra-ui/react"
import { FiHome, FiTrendingUp, FiTruck, FiFile, FiSettings, FiLogOut, FiDollarSign, FiChevronLeft, FiChevronRight, FiChevronDown, FiShoppingCart, FiBarChart } from 'react-icons/fi'
import { IconType } from 'react-icons'
import NextLink from 'next/link';
import { useColorMode } from "@/components/ui/color-mode"
import { useState, useEffect, useRef } from 'react'
import { CloseButton } from '@/components/ui/close-button';
import { LuMoon, LuSun } from "react-icons/lu"
import { useRouter } from "next/navigation";
import { logout, updateUser } from "@/lib/features/users";
import { useAppDispatch, useAppSelector } from "@/lib/hooks";
import { getPersistor } from "@/lib/store";
import { NativeSelectRoot, NativeSelectField } from "@/components/ui/native-select"
import { toaster } from '@/components/ui/toaster';
import { motion } from "motion/react";

export const Sidebar = ({
  isCollapsed,
  toggleCollapse,
}: {
  isCollapsed: boolean;
  toggleCollapse: () => void;
}) => {

  const dispatch = useAppDispatch();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const currentUser = useAppSelector((state) => state?.user.currentUser);
  useEffect(() => {
    if (!currentUser) {
      toaster.error({
        title: "Session Error",
        description: "No user session found. Please contact admin to login.",
      });
      setTimeout(() => {
        router.push("/");
      }, 2000);
    }
  }, [currentUser, router]);


  const handleSignOut = async () => {
    try {
      setLoading(true);

      // Show logout message first
      toaster.create({
        title: "Logging Out",
        description: "Please wait...",
        type: "info"
      });

      // Navigate immediately to prevent hook issues
      router.push("/");

      // Clear everything after navigation starts
      setTimeout(() => {
        // Clear storage
        localStorage.clear();
        sessionStorage.clear();

        // Clear Redux state
        dispatch(logout());

        // Try to purge persistor
        try {
          const persistor = getPersistor();
          persistor.purge().catch(() => {
            // Silent fail - already cleared
          });
        } catch (persistError) {
          // Silent fail - already cleared
        }

        // Show success message
        toaster.create({
          title: "Logged Out",
          description: "You have been successfully logged out.",
          type: "success"
        });
      }, 200);

    } catch (error) {
      // Fallback - clear everything and navigate
      router.push("/");
      localStorage.clear();
      sessionStorage.clear();
      dispatch(logout());

      toaster.create({
        title: "Logout Complete",
        description: "You have been signed out.",
        type: "info"
      });

    } finally {
      setTimeout(() => setLoading(false), 300);
    }
  };
  // const handleToggleCollapse = () => {
  //   toggleCollapse();
  // };

  const LinkItems = [
    { name: "Home", icon: FiHome, link: "/pos" },
    { name: "Sales", icon: FiDollarSign, link: "/pos/sale" },
    { name: "Inventory", icon: FiShoppingCart, link: "/pos/inventory" },
    { name: "Supplies", icon: FiTruck, link: "/pos/reports/suppliers" },
    { name: "Reports", icon: FiBarChart, link: "/pos/reports" },
    { name: 'Manager', icon: FiSettings, link: '/pos/manager' },
    { name: 'Sign Out', icon: FiLogOut, link: '/', onClick: handleSignOut },
  ];

  return (
    <div>
      {loading && (
        <Flex
          position="fixed"
          top="0"
          left="0"
          height="100vh"
          width="100vw"
          backgroundColor="rgba(0, 0, 0, 0.5)"
          justifyContent="center"
          alignItems="center"
          zIndex="9999"
        >
          <Spinner size="xl" color="red.600" borderWidth="4px" />
        </Flex>
      )}
      <Box
        bg="white"
        _dark={{ bg: "black" }}
        position="fixed"
        left="0"
        top="0"
        h="100vh"
        w={isCollapsed ? "4.5rem" : "12rem"}
        boxShadow="md"
        transition="all 0.2s ease"
        // zIndex={1}
        display="flex"
        flexDirection="column"
      >
        <Box
          p={4}
          borderBottomWidth="1px"
          borderColor="gray.100"
          _dark={{ borderColor: "gray.700" }}
          display="flex"
          justifyContent={isCollapsed ? "center" : "space-between"}
          alignItems="center"
        >
          <VStack alignItems="center" mx="4" gap={4} display={{ base: isCollapsed ? "none" : "flex" }}>
            <Menu_shops />
          </VStack>

        </Box>

        <Box
          flex="1"
          overflowY="auto"
          p={2}
          display="flex"
          flexDirection="column"
          alignItems={isCollapsed ? "center" : "stretch"}
        >
          <NavItem
            data={LinkItems}
            isCollapsed={isCollapsed}
            toggleCollapse={toggleCollapse}
            loading={loading}
          />
        </Box>

      </Box>

    </div>
  );
};
interface NavItemProps {
  data: Array<{
    name: string;
    icon: IconType;
    link: any;
    onClick?: () => void;
  }>;
  isCollapsed: boolean;
  toggleCollapse: () => void;
  loading: boolean;
}
const NavItem = ({ data, isCollapsed, toggleCollapse, loading }: NavItemProps) => {
  const { toggleColorMode } = useColorMode()
  return (
    <>
      <Flex direction="column">
        <Flex direction="column" flex="1">
          {data.slice(0, data.length - 1).map((link) => (
            <NextLink href={link.link} passHref key={link.name}>
              <Flex align="center" py="4" mx={!isCollapsed ? '2rem' : '0'} borderRadius="lg" role="group" cursor="pointer" style={{ textDecoration: "none" }} _focus={{ boxShadow: "none" }} >
                <HStack >
                  <Box px={!isCollapsed ? '-2rem' : '0'} _hover={{ color: "blue.500", transform: "scale(1.1)" }} transition="all 0.2s ease-in-out" fontSize="50" >{link.icon && <link.icon fontSize="50" />}</Box>
                  {!isCollapsed && link.name}
                </HStack>
              </Flex>
            </NextLink>
          ))}
        </Flex>
        <Flex direction="column">

      <VStack gap={3}
        align="center" 
          position="absolute"
          bottom="20px"
          left="0"
          right="0"
          marginX="auto"
        >
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }} >
              <IconButton
                aria-label="Toggle Color Mode"
                onClick={toggleColorMode}
                variant="ghost"
                size="sm"
                borderRadius="lg"
                bg="gray.100"
                _dark={{ bg: "gray.700" }}
                _hover={{
                  bg: "gray.200",
                  _dark: { bg: "gray.600" }
                }}
              >
                {useColorMode().colorMode == "dark" ? <LuSun /> : <LuMoon />}
              </IconButton>
            </motion.div>

            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                <IconButton
                  aria-label="Toggle Sidebar"
                  onClick={toggleCollapse}
                  variant="ghost"
                  size="sm"
                  borderRadius="lg"
                  bg="blue.100"
                  _dark={{ bg: "blue.800", color: "blue.300" }}
                  _hover={{
                    bg: "blue.200",
                    _dark: { bg: "blue.700" }
                  }}
                  color="blue.600"
                >
                  {isCollapsed ? <FiChevronRight /> : <FiChevronLeft />}
                </IconButton>
              </motion.div>


              
              <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Box
                p={isCollapsed ? 3 : 4}
                borderRadius="xl"
                cursor={loading ? "not-allowed" : "pointer"}
                onClick={loading ? undefined : data[data.length - 1].onClick}
                opacity={loading ? 0.6 : 1}
                transition="all 0.2s ease"
              >
                <HStack gap={isCollapsed ? 0 : 4} justify={isCollapsed ? "center" : "flex-start"}>
                  <Box
                    p={2}
                    borderRadius="lg"
                    bg="red.500"
                    cursor={loading ? "not-allowed" : "pointer"}
                    color={'white'}
                  >
                    {loading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Icon boxSize={5}><FiLogOut/></Icon>
                    )}
                  </Box>
                  {!isCollapsed && (
                    <Text fontWeight="medium" color="red.700" _dark={{ color: "red.300" }} fontSize="sm">
                      {loading ? "Signing Out..." : "Sign Out"}
                    </Text>
                  )}
                </HStack>
              </Box>
            </motion.div>
            
        </VStack>
        </Flex>
      </Flex>
    </>
  );
};



export const Menu_shops = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector((state) => state.user.currentUser);

  // Always call hooks in the same order - use safe defaults
  const shops = currentUser?.shops || [];
  const currentShopId = currentUser?.currentshop || '';
  const [selectedShop, setSelectedShop] = useState(() => {
    if (!currentUser) return null;
    const savedShopId = currentShopId ? currentShopId : localStorage.getItem('currentShopId');
    return shops.find((shop: any) => shop.shop_id == savedShopId) || shops[0] || null;
  });

  const [loading, setLoading] = useState(false);
  const [isLoggedOut, setIsLoggedOut] = useState(false);

  // Detect logout state to prevent hook issues
  useEffect(() => {
    if (!currentUser && !isLoggedOut) {
      setIsLoggedOut(true);
    }
  }, [currentUser, isLoggedOut]);

  useEffect(() => {
    // Only show error if user exists but has no shops
    if (currentUser && shops.length === 0) {
      toaster.error({
        title: "Session Error",
        description: "No shops found. Please contact admin to login For shop Allocation.",
      });
      setTimeout(() => {
        router.push("/");
      }, 2000);
    }
  }, [shops, currentUser, router]);

  useEffect(() => {
    if (currentUser && shops.length > 0 && !selectedShop) {
      const defaultShop = shops[0];
      setSelectedShop(defaultShop);
      dispatch(updateUser({ currentshop: defaultShop.shop_id }));
      localStorage.setItem('currentShopId', defaultShop.shop_id);
    }
  }, [shops, selectedShop, dispatch, currentUser]);

  const handleShopChange = async (value: string) => {
    const shop = shops.find((s) => s.shop_id == value);
    if (shop) {
      setLoading(true);
      setSelectedShop(shop);
      dispatch(updateUser({ currentshop: shop.shop_id }));
      localStorage.setItem('currentShopId', shop.shop_id);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setLoading(false);
      window.location.reload();
    }
  };

  // Render logic - no early returns after hooks
  if (isLoggedOut || !currentUser) {
    return <p>Loading...</p>;
  }

  if (shops.length === 0) {
    return <p>No shops available.</p>;
  }

  return (
    <>
      {loading && (
        <Flex
          position="fixed"
          top="0"
          left="0"
          width="100vw"
          height="100vh"
          backgroundColor="rgba(0, 0, 0, 0.5)"
          justifyContent="center"
          alignItems="center"
          zIndex="9999"
        >
          <Spinner size="xl" color="red.600" borderWidth="4px" />
        </Flex>
      )}
      <NativeSelectRoot icon={<FiChevronDown />}>
        <NativeSelectField
          size="md"
          width="9rem"
          value={selectedShop?.shop_id ?? ""}
          onChange={(e) => handleShopChange(e.target.value)}
          placeholder="Select a Shop"
        >
          {shops.map((shop) => (
            <option key={shop.shop_id} value={shop.shop_id}>
              {shop.shop_name}
            </option>
          ))}
        </NativeSelectField>
      </NativeSelectRoot>
    </>
  );
};

export default Sidebar;