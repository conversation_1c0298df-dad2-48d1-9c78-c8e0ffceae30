"use client"

import  React,{ useState,useEffect,useCallback,useMemo,memo,Suspense } from "react";
import {FiX,FiSearch,FiPackage,FiTrendingUp ,FiShoppingCart,FiShoppingBag,FiLoader,FiDownload,FiImage} from 'react-icons/fi'
import {IconButton,For,Center,Spinner,SimpleGrid, Tabs,Icon,Group,Card,Button,Box,Text,Stack,VStack,HStack,Input,Flex,createListCollection } from "@chakra-ui/react"
import {Empty} from '@/components/pos/empty'
import { PaginationItems, PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, } from "@/components/ui/pagination"
import {SaleButton} from '@/components/pos/sale_btn' 
import TabTable from '@/components/pos/pending'
import Search from '@/components/pos/search'
import { useColorModeValue } from '@/components/ui/color-mode';
import {Product,Category} from '@/app/utils/definitions'
import Image from 'next/image'
import {DialogBody,DialogCloseTrigger,DialogContent,DialogFooter,DialogHeader,DialogRoot,DialogTitle,DialogTrigger,} from "@/components/ui/dialog"
import { ProductView} from "@/components/pos/invetory_functions";
import { motion } from "motion/react";
import {fetchData2,fetchData,insertData,updateData,deleteData,fetchImage } from '@/app/utils/pos';
import {MenuContent,MenuItem,MenuRoot,MenuTrigger,MenuTriggerItem} from "@/components/ui/menu"
import { BiCategory } from "react-icons/bi";
import {User} from '@/lib/features/users'
import { Pointer } from "lucide-react";
var pending_status = [13,0];
var cancle_status=14;
var complete_status=[15,20]
var complete_order=[19,21];
// var pending_order=19;
import Filter from '@/components/pos/filters'



const Stock =  memo(({query,onBuy,user,setQuery,checkTransaction}: { query: string; onBuy: (item: Product, quantity: number) => void;user:User;setQuery:any;checkTransaction:any}) => {
  const [activeTab, setActiveTab] = useState<string>('items');
  const[seachquery, setSearchQuery] =useState('');
  const handleSearch = (searchValue: string) => {setSearchQuery(searchValue);};
  const shop_id =user?.currentshop;

  return (
    <SimpleGrid gap={14} w="100%">
      <Tabs.Root defaultValue='items'  variant="line"  onValueChange={(value) =>setActiveTab(value.value)}>
        <Tabs.List gap={4}>
          <Tabs.Trigger value="items" ><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiPackage /></Icon>{' '}<Text fontSize={{base:"10px",md:"12px",lg:"15px"}}>Items</Text></Tabs.Trigger>
          <Tabs.Trigger value="pending"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiLoader /></Icon>{' '}<Text fontSize={{base:"10px",md:"12px",lg:"15px"}}>Pending/Hold</Text></Tabs.Trigger>
          <Tabs.Trigger value="canceled"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiShoppingBag /></Icon>{' '}<Text fontSize={{base:"10px",md:"12px",lg:"15px"}}>Canceled</Text></Tabs.Trigger>
          <Tabs.Trigger value="completed"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiShoppingBag /></Icon>{' '}<Text fontSize={{base:"10px",md:"12px",lg:"15px"}}>Completed</Text></Tabs.Trigger>
          <Tabs.Trigger value="orders"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiDownload /></Icon>{' '}<Text fontSize={{base:"10px",md:"12px",lg:"15px"}}>Orders</Text></Tabs.Trigger>
          {/* <Tabs.Trigger value="pending_orders"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiLoader /></Icon>{' '}<Text fontSize={{base:"9px",md:"12px",lg:"15px"}}>Undelivered</Text></Tabs.Trigger> */}
             
            {/* <Tabs.Trigger value="actions"><Icon fontSize={{base:"13px",md:"15px",lg:"15px"}} color="blue.500"><FiDownload /></Icon>{' '}<Text fontSize={{base:"9px",md:"12px",lg:"15px"}}>Actions</Text></Tabs.Trigger> */}

          {activeTab != 'actions' && activeTab != 'items' && (<Search placeholder={`Search for "${activeTab}"`} tableName={activeTab} onSearch={(value, tableName) => handleSearch(value)}/>)}
          {activeTab == 'items' && ( <Filter setQuery={setQuery} shop_id={shop_id}/>)}
        </Tabs.List>

          <Stack  key={activeTab} w={{base:'100%',md:'100%'}}>
              <Suspense fallback={<Center  h={'70vh'} ><Spinner size="xl" /></Center>}>
                  {
                  activeTab == 'items'?(<Tabs.Content value="items"><Items query={query} onBuy={onBuy} shop_id={shop_id}/></Tabs.Content>) :
                  activeTab == 'pending'?( <Tabs.Content value="pending"><TabTable  status={pending_status} mode="Hold/Pending" user={user} query={seachquery} checkTransaction={checkTransaction}/></Tabs.Content>):
                  activeTab == 'completed'?(<Tabs.Content value="completed"><TabTable status={complete_status}  mode="complete" user={user} query={seachquery} checkTransaction={checkTransaction}/></Tabs.Content>):
                  activeTab == 'canceled'?(<Tabs.Content value="canceled"><TabTable status={cancle_status}  mode="canceled" user={user} query={seachquery} checkTransaction={checkTransaction}/></Tabs.Content>):
                  activeTab == 'orders'?(<Tabs.Content value="orders"><TabTable status={complete_order}  mode="orders" user={user} query={seachquery} checkTransaction={checkTransaction}/></Tabs.Content>):
                  // activeTab == 'pending_orders'?(<Tabs.Content value="pending_orders"><TabTable status={pending_order}  mode="pending_orders" user={user} query={seachquery} checkTransaction={checkTransaction}/></Tabs.Content>):
                  // activeTab == 'actions'? (<Tabs.Content value="actions">{/* <Action /> */}</Tabs.Content>)
                  null
                  }   

              </Suspense>
            </Stack>
        {/* </MotionStack> */}
      </Tabs.Root>
    </SimpleGrid>
  );
});

export default Stock;




const Items =  memo(({ 
  query, 
  onBuy,
  shop_id
}: { 
  query: string; 
  onBuy: (item: Product, quantity: number) => void;
  shop_id:any
    }) => {

  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [items, setItems] = useState<Product[]>([]);
  const [Loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(8);
  const [totalItems, setTotalItems] = useState(0);
  const [imageUrls, setImageUrls] = useState<{ [itemId: string]: string }>({});

  const loadData = useCallback(async () => {
  
    const queryParams = { query, shop_id };
    try {
      const response = await fetchData<Product>('items', currentPage, pageSize, queryParams)
      setItems(response.data);
      setTotalItems(response.metadata.total);
      const imageFetchPromises = response.data.map(async (item) => {
        if (item.item_pic_url?.length > 0) {
          try {
            const fetchedUrl = item.item_pic_url[0];
            return { itemId: item.item_id, url: fetchedUrl };
          } catch (error) {
            return { itemId: item.item_id, url: null };
          }
        }
        return { itemId: item.item_id, url: null };
      });
      const imageResults = await Promise.all(imageFetchPromises);
      const newImageUrls = imageResults.reduce((acc, { itemId, url }) => {
        if (url) acc[itemId] = url;
        return acc;
      }, {} as { [itemId: string]: string });
      setImageUrls((prev) => ({ ...prev, ...newImageUrls }));
    } catch (error) {
      throw new Error("Invalid response format");
    } finally {
      await setLoading(false);
    }
  }, [currentPage,pageSize,query]);

  // useEffect(() => {loadData()},[loadData]); 
  useEffect(() => {
    setLoading(true);
    // const delayDebounceFn = setTimeout(() => {
      loadData();
    // }, 1000);
    // return () => clearTimeout(delayDebounceFn);

  }, [query, loadData]);

  const handleBuyItem = useCallback((item: Product, quantity: number) => {
      if(onBuy){
      onBuy(item,quantity)
        setItems((prevItems) =>
          prevItems.map((prevItem) =>
            prevItem.item_id === item.item_id
              ? { ...prevItem, item_quantity: prevItem.item_quantity - quantity }
              : prevItem
          ));}},
  []);
  

  const handlePageChange = useCallback((page: any) => {
    setCurrentPage(page.page);
  }, []);

  const handlePageSizeChange = (newPageSize: any) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  const CenterBg = useColorModeValue('gray.100', 'gray.700');
  const CenterColor = useColorModeValue('gray.400', 'gray.500');

  const handleViewItem = (item: Product) => {
    setSelectedProduct(item);
    setIsViewModalOpen(true);
  };

  const handleCloseModel = (item: Product) => {
    setIsViewModalOpen(false);
  };
  return (
    <Stack direction="column" gap={4} mr={2}>
      <Stack borderRadius="lg" boxShadow="xl">
      {isViewModalOpen && selectedProduct && (
            <DialogRoot
              open={isViewModalOpen}
              onOpenChange={(e) => {if (!e.open) handleCloseModel}}
              closeOnEscape={true}
              placement="top"
              size="xl"
              motionPreset="slide-in-top"
              lazyMount={true}
              preventScroll>
            <DialogContent>
              <DialogCloseTrigger>
                <Box as="span" onClick={handleCloseModel} aria-label="Close dialog" cursor="pointer" p={1} _hover={{ bg: "gray.100" }}><FiX /></Box>
              </DialogCloseTrigger>
              <DialogBody>
                <ProductView item={selectedProduct} onClose={handleCloseModel} mode='pos' onBuy={handleBuyItem}/>
              </DialogBody>
            </DialogContent>
          </DialogRoot>
          )}

        {Loading ?(
          <Center  h={'70vh'} >
              <Spinner size="xl" color="red.600"/>
        </Center>
        ):!Loading && items.length == 0 ? (
          <Box textAlign="center" pt={120} h={'70vh'}>
            <Empty icon={<FiShoppingCart />} title="No Items" description="No items in the shop"/>
          </Box>
        ):(
          <SimpleGrid columns={{ base: 2, md: 4 }}
           gap={2} p={3}
           >
            {items.map((item) => (
              <Box
                key={item.item_id}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="md"
                overflow="hidden"
                textAlign="center"
                onClick={() => handleViewItem(item)}
                _hover={{ transform: "translateY(-4px)", transition: "all 0.2s",cursor:"pointer" }}>

                {imageUrls[item.item_id] ? (
                  <Box position="relative" width="100%" height="9rem" overflow="hidden">
                    <Image
                      src={imageUrls[item.item_id]}
                      alt={item.item_name}
                      style={{ objectFit: "cover" }}
                      fill
                      priority={true}
                    />
                  </Box>
                ) : (
                  <Center borderRadius="lg" bg={CenterBg} height="7.5rem">
                    <Icon boxSize={12} color={CenterColor}><FiImage /></Icon>
                  </Center>
                )}
                <Text fontWeight="bold" textStyle="sm" mt={2}>{item?.item_name.toLowerCase()}</Text>
                <Text textStyle="sm" color="gray.500">stock:{item.item_quantity} </Text>
                <Text textStyle="sm" > price: {item.item_selling}</Text>
              </Box>
            ))}
          </SimpleGrid>
        )}
        </Stack>
        <PaginationRoot
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          page={currentPage}
          count={totalItems}
          pageSize={pageSize}
          defaultPage={1}
          size='sm'
        >
          <HStack justify="space-between" align="center">
            <PaginationPageText />
            <Flex justify="center" align="center" gap={2}>
              <PaginationPrevTrigger />
              <PaginationItems />
              <PaginationNextTrigger />
            </Flex>
          </HStack>
        </PaginationRoot>
    </Stack>
  );
});



