import { Heading,Table,Text, Box, HStack,Button,Flex,Stack,Center,Spinner} from '@chakra-ui/react';
import { PaginationItems, PaginationNextTrigger, PaginationPageText, PaginationPrevTrigger, PaginationRoot, } from "@/components/ui/pagination"
// const Table_ = ({ columns, columnSizes,totalPages,data, height, width, currentPage, totalItems, handlePageChange, pageSize,getActions,isLoading}) => {
import { Shop,Product,Transaction,Order,Receipt,Category,Discount,ApiResponse } from '@/app/utils/definitions'
import { LuShoppingCart } from "react-icons/lu"
import { EmptyState } from "@/components/ui/empty-state"

const Table_ = ({
  columns,
  data,
  currentPage,
  totalItems,
  pageSize,
  handlePageChange,
  handlePageSizeChange,
  getActions,
  isLoading,
  width,
  height,
}) => {
  // Ensure currentPage is an integer and within valid range

 
  const totalPages = Math.ceil(totalItems / pageSize);
  const validCurrentPage = Math.max(1, Math.min(currentPage, totalPages));

  return (
    <Stack direction='column' h={height} w={width}>
      <Table.Root stickyHeader size={'sm'}>
        <Table.Header>
          <Table.Row bg="bg.subtle">
            {columns.map((col, index) => (
              <Table.ColumnHeader key={index} textAlign={col.align || 'left'}>
                {col.label}
              </Table.ColumnHeader>
            ))}
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {isLoading ? (
            <Table.Row>
              <Table.Cell colSpan={columns.length} textAlign="center">
                <Center h="200px">
                  <Spinner size="xl" color="red.600"/>
                </Center>
              </Table.Cell>
            </Table.Row>
          ) : data.length == 0 ? (
            <Table.Row>
              <Table.Cell colSpan={columns.length} textAlign="center">
                <Center h="60vh">
                  <EmptyState
                    icon={<LuShoppingCart />}
                    title="Your Inventory is empty"
                    description="Explore add items to your Store"
                  />
                </Center>
              </Table.Cell>
            </Table.Row>
          ) :(
            <>
              {data.map((item, index) => (
                <Table.Row key={index}>
                  {columns.map((col, colIndex) => (
                    <Table.Cell key={colIndex} textAlign={col.align || 'left'}>
                      {col.key === 'actions' ? (
                        <HStack gap={2} justify="center">
                          {getActions(item).map((action, actionIndex) => (
                            <Button
                              key={actionIndex}
                              size="xs"
                              colorScheme={action.colorScheme}
                              onClick={() => action.onClick(item)}
                            >
                              {action.label}
                            </Button>
                          ))}
                        </HStack>
                      ) : (
                        // item[col.key]

                        // col.key === 'item_availability' ? (
                        //   item[col.key] ? <Text color={'green.700'} fontSize={'md'}>In stock</Text>: <Text color={'red.700'} fontSize={'md'} >out of stock</Text>
                        // ) : (
                        col.key.includes('.') ? (col.key.split('.').reduce((acc, part) => acc?.[part], item)) : (item[col.key])
                        // )
                      )}
                    </Table.Cell>
                  ))}
                </Table.Row>
              ))}
            </>
          )}
        </Table.Body>
      </Table.Root>
      <PaginationRoot
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        page={validCurrentPage}
        count={totalItems}
        pageSize={pageSize}
        defaultPage={1}
        size='sm'
      >
        <HStack justify="space-between" align="center">
          <PaginationPageText />
          <Flex justify="center" align="center" gap={2}>
            <PaginationPrevTrigger />
            <PaginationItems />
            <PaginationNextTrigger />
          </Flex>
        </HStack>
      </PaginationRoot>
    </Stack>
  );
};
export default Table_;
