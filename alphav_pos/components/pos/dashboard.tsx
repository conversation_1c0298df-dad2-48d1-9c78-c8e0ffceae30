
"use client";
import { <PERSON>, <PERSON>, <PERSON>ing,Spacer, Stack, HStack, Icon, Grid, Flex,Stat, Image, Badge, Button, VStack,Text } from "@chakra-ui/react";
import {FcBusinesswoman,FcManager,FcBusiness,FcShop,FcBriefcase,FcComboChart , FcSalesPerformance, FcStatistics, FcShipped, FcFactory,FcConferenceCall,FcInspection,FcCancel,FcAddRow, FcClock, FcCalendar, FcMoneyTransfer, FcCustomerSupport, FcGlobe, FcSettings } from "react-icons/fc";
import {StatValueText } from "@/components/ui/stat";
import { Legend, Bar, Line, Scatter, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ComposedChart,LineChart, PieChart, Pie, Cell } from 'recharts';
import Link from 'next/link';
import { motion, AnimatePresence } from 'motion/react';
import React, { useState, useEffect } from 'react';
import { GiCirclingFish } from "react-icons/gi";
import {fetchData} from '@/app/utils/pos';
import { toaster } from "@/components/ui/toaster"
import {useAppSelector } from "@/lib/hooks";
import moment from 'moment';

const MotionCard = motion.create(Card.Root);

export default function Dashboard() {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const [tSales, setSales] = useState(0);
  const [tPro, setProd] = useState(0);
  const [Reports, setReports] = useState(0);
  const [AmountSupply, setSupply] = useState(0);
  const [pieData, setpieData] = useState([]);
  const [customerData, setCustomerData] = useState([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [activeCustomers, setActiveCustomers] = useState(0);
  const currentUserRoles = useAppSelector(state => state.user.currentUser?.roles || []);
  const shop_name = useAppSelector(state => state.user.currentUser?.shops?.find((shop) => shop?.shop_id == shop_id)?.shop_name || '');
  const Current_User=useAppSelector(state => state.user.currentUser?.email || '');
  const currentUser = useAppSelector(state => state.user.currentUser);

  if (!currentUser || !currentUser.roles) {
    return null;
  }

  const handleFetch = async () => {
    if (!shop_id) return;
    try {
        const response = await fetchData('cal_dash', null, null, { shop_id });
        if (response) {
          setpieData(response.pieData || []);
          setSales(response.totalSales || 0);
          setProd(response.totalProducts || 0);
          setReports(response.totalReports || 0);
          setSupply(response.totalSupplies || 0);
        }
    } catch (error) {
        toaster.error({title: "Error on server",description: 'An unexpected error occurred.', });
    }
  };
  useEffect(() => {
    handleFetch();
  }, [shop_id]);


  const dash_cards = [
    { id: 1, link: '/pos/sale', name: 'Sales', icon: <FcSalesPerformance />, type: "Today's sales", total: tSales,disabled: false, requiredRoles: ['admin', 'manager','pos'] as const},
    { id: 2, link: '/pos/inventory', name: 'Inventory', icon: <FcFactory />, type: "Total Items", total: tPro,disabled: false, requiredRoles: ['admin', 'manager','inventory'] as const},
    { id: 3, link: '/pos/reports', name: 'Reports', icon: <FcStatistics />, type: "Reports", total: Reports, disabled: false,  requiredRoles: ['admin', 'manager', 'reports'] as const},
    { id: 4, link: '/pos/reports/suppliers', name: 'Suppliers', icon: <FcShipped />, type: "Sales", total: AmountSupply, disabled: false,  requiredRoles: ['admin', 'manager', 'reports','suppliers'] as const},
  ];

  

  const storeInfoCards = [
    { id: 1,
      frontContent: {
        title:'Current Shop',
        Shop_name:`Shop : ${shop_name}`,
        shop_id:`Shop ID : ${shop_id}`,
        Current_User:`User : ${Current_User}`,
        icon: <FcBusinesswoman/>,
      },
      backContent: {
        title:'Current Shop',
        Shop_name:`Shop : ${shop_name}`,
        shop_id:`Shop ID : ${shop_id}`,
        Current_User:`User : ${Current_User}`,
        icon: <FcBusinesswoman/>,
      },
      color: 'purple.500',
      flipInterval: 9000,
      flipDirection: 'rotateY' },
      // { id: 2,
      //   frontContent: {
      //     title:'Current Shop',
      //     Shop_name:`Shop : ${shop_name}`,
      //     shop_id:`Shop ID : ${shop_id}`,
      //     Current_User:`User : ${Current_User}`,
      //     icon: <FcBusinesswoman/>,
      //   },
      //   backContent: {
      //     title:'Current Shop',
      //     Shop_name:`Shop : ${shop_name}`,
      //     shop_id:`Shop ID : ${shop_id}`,
      //     Current_User:`User : ${Current_User}`,
      //     icon: <FcBusinesswoman/>,
      //   },
      //   color: 'purple.500',
      //   flipInterval: 9000,
      //   flipDirection: 'rotateY' },

    
  ];




    const getUserRoleBadge = () => {
      // Handle both string roles and role objects
      const primaryRoleData = currentUser.roles[0];
      const primaryRole = primaryRoleData?.role_name || 'user';
      const roleColors: Record<string, string> = {
        admin: 'red',
        manager: 'purple',
        supervisor: 'blue',
        pos: 'green',
        inventory: 'orange',
        supplies: 'teal',
        reports: 'cyan'
      };
  
      return (
        <Badge
          colorScheme={roleColors[primaryRole] || 'gray'}
          variant="solid"
          textTransform="capitalize"
        >
          {primaryRole}
        </Badge>
      );
    };

  return (
    <Box bg="gray.50" _dark={{ bg: "gray.900" }} minH="100vh">
      <Box p={{ base: 4, md: 6 }}>
        <HStack justify="space-between" mb={1} px={9}>
          <VStack align="start" gap={1}>
            <Text
              fontSize={{ base: "xl", md: "2xl" }}
              fontWeight="bold"
              color="gray.800"
              _dark={{ color: "white" }}
            >
              Dashboard
            </Text>
          </VStack>
          {getUserRoleBadge()}
        </HStack>
        <Grid
          templateColumns={{
            base: "repeat(1, 1fr)",
            sm: "repeat(2, 1fr)",
            md: "repeat(2, 1fr)",
            lg: "repeat(4, 1fr)"
          }}
          gap={{ base: 4, md: 6 }}
        >
          {dash_cards.map((card_s) => {
            const hasRequiredRole = card_s.requiredRoles.some(requiredRole =>
              currentUserRoles.some(userRole => userRole?.role_name == requiredRole)
            );

            const getCardGradient = (cardName) => {
              const gradients = {
                'Sales': 'linear(135deg, green.400, green.600)',
                'Inventory': 'linear(135deg, blue.400, blue.600)',
                'Reports': 'linear(135deg, purple.400, purple.600)',
                'Suppliers': 'linear(135deg, orange.400, orange.600)',
              };
              return gradients[cardName] || 'linear(135deg, gray.400, gray.600)';
            };

            return (
              <Link
                href={card_s.link}
                key={card_s.id}
                style={{ pointerEvents: !hasRequiredRole ? 'none' : 'auto' }}
              >
                <MotionCard
                  size="sm"
                  w="100%"
                  h={{ base: "140px", md: "160px", lg: "180px" }}
                  bgGradient={hasRequiredRole ? getCardGradient(card_s.name) : "linear(135deg, gray.300, gray.500)"}
                  borderRadius="2xl"
                  overflow="hidden"
                  cursor={!hasRequiredRole ? 'not-allowed' : 'pointer'}
                  position="relative"
                  whileHover={{
                    scale: !hasRequiredRole ? 1 : 1.05,
                    rotateY: !hasRequiredRole ? 0 : 5,
                  }}
                  whileTap={{ scale: !hasRequiredRole ? 1 : 0.95 }}
                  transition="all 0.3s ease"
                  border="1px solid"
                  borderColor="whiteAlpha.200"
                  boxShadow="xl"
                  _hover={{
                    boxShadow: !hasRequiredRole ? 'xl' : '2xl',
                  }}
                >
                  {/* Background Pattern */}
                  <Box
                    position="absolute"
                    top={0}
                    right={0}
                    w="5rem"
                    h="6rem"
                    opacity={0.1}
                    transform="translate(20px, -20px)"
                  >
                    <Icon fontSize="6xl" >
                      {card_s.icon}
                    </Icon>
                  </Box>

                  <Card.Header p={{ base: 4, md: 5 }}>
                    <HStack justify="space-between" align="start">
                      <VStack align="start" gap={1}>
                        <Heading
                          size={{ base: "sm", md: "md" }}
                          textShadow="0 2px 4px rgba(0,0,0,0.3)"
                        >
                          {card_s.name}
                        </Heading>
                        <Text
                          fontSize={{ base: "xs", md: "sm" }}
                          fontWeight="medium"
                        >
                          {card_s.type}
                        </Text>
                      </VStack>
                      <Box
                        p={2}
                        borderRadius="xl"
                        backdropFilter="blur(10px)"
                      >
                        <Icon fontSize={{ base: "xl", md: "2xl" }} >
                          {card_s.icon}
                        </Icon>
                      </Box>
                    </HStack>
                  </Card.Header>

                  <Card.Body p={{ base: 4, md: 5 }} pt={0}>
                    <VStack align="start" gap={2}>
                      <Stat.Root>
                        {card_s.type !== "Reports" &&card_s.type !== "Sales" && card_s.type !== "Total Items" ? (
                          <StatValueText
                            fontSize={{ base: "lg", md: "xl" }}
                            fontWeight="bold"
                            value={card_s.total}
                            formatOptions={{ style: "currency", currency: "KSH" }}
                          />
                        ) : (
                          <StatValueText
                            fontSize={{ base: "lg", md: "xl" }}
                            fontWeight="bold"
                            value={card_s.total}
                          />
                        )}
                      </Stat.Root>

                      {!hasRequiredRole && (
                        <Box
                          bg="red.500"
                          px={2}
                          py={1}
                          borderRadius="md"
                          fontSize="xs"
                          fontWeight="bold"
                        >
                          No Access
                        </Box>
                      )}
                    </VStack>
                  </Card.Body>
                </MotionCard>
              </Link>
            );
          })}
        </Grid>
      </Box>
      <Box px={{ base: 4, md: 6, lg: 7 }}>
        <Grid
          templateColumns={{ base: "1fr", lg: "4fr 4fr 2fr" }}
          gap={{ base: 6, md: 4 }}
        >
          <GraphCard shop_id={shop_id} />
          <CustomerPieChart pieData={pieData} />
          <Grid
            templateColumns={{ base: "1fr" }}
            gap={{ base: 4, md: 6 }}
            alignContent="start"
          >
            {storeInfoCards.map((card) => (
              <FlipCard key={card.id} card={card} disabled={card?.disabled} />
            ))}
          </Grid>
        </Grid>

      </Box>
    </Box>
  );
}

const GraphCard = ({ shop_id }) => {
  const [tSales, setSales] = useState([]);
  const [weeklySales, setWeeklySales] = useState([]);

  const handleFetch = async () => {
    if (!shop_id) return;

    try {
      const response = await fetchData("graph_sale", null, null, { shop_id });
      const response2 = await fetchData("graph_week", null, null, { shop_id });

      if (response.data) {
        setSales(response.data);
      }

      if (response2.data) {
        setWeeklySales(response2.data);
      }
    } catch (error) {
      toaster.error({
        title: "Error on server",
        description: "An unexpected error occurred.",
      });
    }
  };

  useEffect(() => {
    handleFetch();
  }, [shop_id]);

  const formattedWeeklyData = Object.entries(weeklySales).map(([date, sales]) => ({
    name: moment(date).format("ddd"),
    sales,
    date: moment(date).format("MMM DD"),
  }));

  const formattedSaleData = tSales.map((sale, index) => ({
    name: `Sale ${index + 1}`,
    sales: sale,
    time: `${8 + index}:00`,
  }));

  const [isSalesFlipped, setIsSalesFlipped] = useState(false);
  const handleSalesFlip = () => {
    setIsSalesFlipped(!isSalesFlipped);
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Box
          p={3}
          borderRadius="lg"
          boxShadow="lg"
          border="1px solid"
          borderColor="gray.200"
        >
          <Text fontWeight="bold" color="gray.700">{label}</Text>
          <Text color="blue.600">
            Sales: KSH {payload[0].value?.toLocaleString()}
          </Text>
        </Box>
      );
    }
    return null;
  };

  return (
    <MotionCard
      _dark={{ bg: "gray.800",borderColor: "gray.700"  }}
      boxShadow="xl"
      borderRadius="2xl"
      overflow="hidden"
      cursor="pointer"
      style={{ perspective: "1000px" }}
      height={{ base: "400px", md: "500px", lg: "60vh" }}
      onClick={handleSalesFlip}
      border="1px solid"
      borderColor="gray.100"
      _hover={{
        boxShadow: "2xl",
        transform: "translateY(-2px)",
      }}
      transition="all 0.3s ease"
    >
      <motion.div
        style={{
          width: "100%",
          height: "100%",
          position: "relative",
          transformStyle: "preserve-3d",
          transform: isSalesFlipped ? "rotateY(180deg)" : "rotateY(0deg)",
          transition: "transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)",
        }}
      >
        {/* Front Side - Today's Sales */}
        <motion.div
          style={{
            width: "100%",
            height: "100%",
            backfaceVisibility: "hidden",
            position: "absolute",
            padding: "1.5rem",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            borderRadius: "1rem",
          }}
        >
          <VStack align="stretch" h="100%" gap={4}>
            <HStack justify="space-between" align="center">
              <VStack align="start" gap={1}>
                <Heading size="lg" color="white">Today's Sales</Heading>
                <Text color="whiteAlpha.800" fontSize="sm">Real-time performance</Text>
              </VStack>
              <Box
                p={3}
                bg="whiteAlpha.200"
                borderRadius="xl"
                backdropFilter="blur(10px)"
              >
                <FcSalesPerformance size={32} />
              </Box>
            </HStack>

            <Box flex="1" bg="whiteAlpha.100" borderRadius="xl" p={4}>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={formattedSaleData}>
                  <defs>
                    <linearGradient id="salesGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#ffffff" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#ffffff" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'white', fontSize: 12 }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'white', fontSize: 12 }}
                    tickFormatter={(value) => `${value/1000}K`}
                  />
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.2)" />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stroke="#ffffff"
                    strokeWidth={3}
                    fill="url(#salesGradient)"
                    dot={{ fill: '#ffffff', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#ffffff' }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </Box>
          </VStack>
        </motion.div>

        {/* Back Side - Weekly Sales */}
        <motion.div
          style={{
            width: "100%",
            height: "100%",
            backfaceVisibility: "hidden",
            position: "absolute",
            transform: "rotateY(180deg)",
            padding: "1.5rem",
            background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
            borderRadius: "1rem",
          }}
        >
          <VStack align="stretch" h="100%" gap={4}>
            <HStack justify="space-between" align="center">
              <VStack align="start" gap={1}>
                <Heading size="lg" color="white">Weekly Sales</Heading>
                <Text color="whiteAlpha.800" fontSize="sm">7-day overview</Text>
              </VStack>
              <Box
                p={3}
                bg="whiteAlpha.200"
                borderRadius="xl"
                backdropFilter="blur(10px)"
              >
                <FcStatistics size={32} />
              </Box>
            </HStack>

            <Box flex="1" bg="whiteAlpha.100" borderRadius="xl" p={4}>
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart data={formattedWeeklyData}>
                  <defs>
                    <linearGradient id="weeklyGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#ffffff" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#ffffff" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <XAxis
                    dataKey="name"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'white', fontSize: 12 }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fill: 'white', fontSize: 12 }}
                    tickFormatter={(value) => `${value/1000}K`}
                  />
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.2)" />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="sales"
                    fill="url(#weeklyGradient)"
                    radius={[4, 4, 0, 0]}
                  />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#ffffff"
                    strokeWidth={3}
                    dot={{ fill: '#ffffff', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#ffffff' }}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </Box>
          </VStack>
        </motion.div>
      </motion.div>
    </MotionCard>
  );
};





const FlipCard = ({ card, disabled }) => {
  const [isFlipped, setIsFlipped] = useState(false);

  useEffect(() => {
    const flipInterval = setInterval(() => {
      setIsFlipped((prev) => !prev);
    }, card.flipInterval);
    return () => clearInterval(flipInterval);
  }, [card.flipInterval]);

  const getAnimationStyle = () => {
    switch (card.flipDirection) {
      case 'rotateY':
        return {
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
          transformStyle: 'preserve-3d',
        };
      case 'rotateX':
        return {
          transform: isFlipped ? 'rotateX(180deg)' : 'rotateX(0deg)',
          transformStyle: 'preserve-3d',
        };
      case 'translateY':
        return {
          transform: isFlipped ? 'translateY(-100%)' : 'translateY(0%)',
        };
      default:
        return {
          transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
          transformStyle: 'preserve-3d',
        };
    }
  };



  return (
    <MotionCard
      position="relative"
      h={{ base: "180px", md: "200px", lg: "220px" }}
      w="100%"
      boxShadow="xl"
      borderRadius="2xl"
      overflow="hidden"
      cursor="pointer"
      style={{ perspective: '1000px' }}
      whileHover={{
        scale: disabled ? 1 : 1.05,
        rotateY: disabled ? 0 : 5,
      }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      border="1px solid"
      borderColor="gray.100"
      _dark={{ borderColor: "gray.700" }}
      transition="all 0.3s ease"
    >
      {/* Disabled Overlay */}
      {disabled && (
        <Box
          position="absolute"
          top="0"
          left="0"
          right="0"
          bottom="0"
          bg="blackAlpha.600"
          display="flex"
          alignItems="center"
          justifyContent="center"
          zIndex={10}
          borderRadius="2xl"
        >
          <VStack gap={2}>
            <Box
              transform="rotate(-15deg)"
              fontSize={{ base: "lg", md: "xl" }}
              fontWeight="bold"
              color="white"
              textAlign="center"
              bg="red.500"
              px={4}
              py={2}
              borderRadius="lg"
              boxShadow="lg"
            >
              IN DEVELOPMENT
            </Box>
            <Text color="whiteAlpha.800" fontSize="sm" textAlign="center">
              Coming Soon
            </Text>
          </VStack>
        </Box>
      )}

      <motion.div
        style={{
          width: '100%',
          height: '100%',
          position: 'relative',
          transformStyle: 'preserve-3d',
          transition: 'transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
          ...getAnimationStyle(),
        }}
      >
        {/* Front Side */}
        <motion.div
          style={{
            width: '100%',
            height: '100%',
            backfaceVisibility: 'hidden',
            position: 'absolute',
            background: `linear-gradient(135deg, ${card.color.replace('.500', '.400')}, ${card.color.replace('.500', '.600')})`,
            borderRadius: '1rem',
            padding: '1.5rem',
          }}
        >
          <VStack align="center" justify="center" h="100%" gap={3}>
            {card.frontContent.icon && (
              <Box
                p={3}
                bg="whiteAlpha.200"
                borderRadius="xl"
                backdropFilter="blur(10px)"
              >
                <Icon fontSize={{ base: "2xl", md: "3xl" }} >
                  {card.frontContent.icon}
                </Icon>
              </Box>
            )}
            <Heading
              size={{ base: "md", md: "lg" }}
              textAlign="center"
              textShadow="0 2px 4px rgba(0,0,0,0.3)"
            >
              {card.frontContent.title}
            </Heading>
            <VStack gap={1}>
              {Object.entries(card.frontContent)
                .filter(([key]) => key !== 'title' && key !== 'icon')
                .map(([key, value]) => (
                  <Text
                    key={key}
                    fontSize={{ base: "xs", md: "sm" }}
                    color="whiteAlpha.900"
                    textAlign="center"
                    fontWeight="medium"
                  >
                    {String(value)}
                  </Text>
                ))}
            </VStack>
          </VStack>
        </motion.div>

        {/* Back Side */}
        <motion.div
          style={{
            width: '100%',
            height: '100%',
            backfaceVisibility: 'hidden',
            position: 'absolute',
            transform:
              card.flipDirection == 'rotateY'
                ? 'rotateY(180deg)'
                : card.flipDirection == 'rotateX'
                ? 'rotateX(180deg)'
                : 'translateY(100%)',
            background: `linear-gradient(135deg, ${card.color.replace('.500', '.600')}, ${card.color.replace('.500', '.800')})`,
            borderRadius: '1rem',
            padding: '1.5rem',
          }}
        >
          <VStack align="center" justify="center" h="100%" gap={3}>
            {card.backContent.icon && (
              <Box
                p={3}
                bg="whiteAlpha.200"
                borderRadius="xl"
                backdropFilter="blur(10px)"
              >
                <Icon fontSize={{ base: "2xl", md: "3xl" }} >
                  {card.backContent.icon}
                </Icon>
              </Box>
            )}
            <Heading
              size={{ base: "md", md: "lg" }}
              textAlign="center"
              textShadow="0 2px 4px rgba(0,0,0,0.3)"
            >
              {card.backContent.title}
            </Heading>
            <VStack gap={1}>
              {Object.entries(card.backContent)
                .filter(([key]) => key !== 'title' && key !== 'icon')
                .map(([key, value]) => (
                  <Text
                    key={key}
                    fontSize={{ base: "xs", md: "sm" }}
                    textAlign="center"
                    fontWeight="medium"
                  >
                    {String(value)}
                  </Text>
                ))}
            </VStack>
          </VStack>
        </motion.div>
      </motion.div>
    </MotionCard>
  );
};

const CustomerPieChart = ({ pieData }) => {

  const transformedData = React.useMemo(() => {
    if (!pieData) {
      return [
        { name: 'No Data', value: 1, color: '#e2e8f0' }
      ];
    }

    const chartData = [];
    console.log('Updated pieData structure:', pieData);

    // Add SALE transactions
    if (pieData.SALE && pieData.SALE.count > 0) {
      chartData.push({
        name: 'Sales',
        value: pieData.SALE.count,
        total: pieData.SALE.total,
        color: '#22c55e', // vibrant green
        gradientId: 'salesGradient'
      });
    }

    // Add SUPPLY transactions
    if (pieData.SUPPLY && pieData.SUPPLY.count > 0) {
      chartData.push({
        name: 'Supplies',
        value: pieData.SUPPLY.count,
        total: pieData.SUPPLY.total,
        color: '#3b82f6', // vibrant blue
        gradientId: 'suppliesGradient'
      });
    }

    // Add ORDER transactions
    if (pieData.ORDER && pieData.ORDER.count > 0) {
      chartData.push({
        name: 'Orders',
        value: pieData.ORDER.count,
        total: pieData.ORDER.total,
        color: '#f59e0b', // vibrant amber
        gradientId: 'ordersGradient'
      });
    }

    // If no transactions, show placeholder
    if (chartData.length === 0) {
      return [
        { name: 'No Transactions', value: 1, color: '#e2e8f0' }
      ];
    }

    return chartData;
  }, [pieData]);

  // Calculate total transactions from the new data structure
  const totalTransactions = React.useMemo(() => {
    if (!pieData) return 0;
    return (pieData.SALE?.count || 0) + (pieData.SUPPLY?.count || 0) + (pieData.ORDER?.count || 0);
  }, [pieData]);

  const RADIAN = Math.PI / 180;
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null; // Don't show labels for very small slices

    const radius = innerRadius + (outerRadius - innerRadius) * 0.6;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <g>
        {/* Enhanced background circle for better readability */}
        <circle
          cx={x}
          cy={y}
          r="25"
          fill="rgba(255, 255, 255, 0.95)"
          stroke="rgba(0, 0, 0, 0.15)"
          strokeWidth="2"
          filter="drop-shadow(0 3px 6px rgba(0,0,0,0.2))"
        />
        <text
          x={x}
          y={y}
          fill="#1f2937"
          textAnchor="middle"
          dominantBaseline="central"
          fontSize="5"
          fontWeight="bold"
          style={{ textShadow: '0 1px 2px rgba(0,0,0,0.1)' }}
        >
          {`${(percent * 100).toFixed(1)}%`}
        </text>
      </g>
    );
  };

  return (
    <MotionCard
      _dark={{ bg: "gray.800", borderColor: "gray.700" }}
      boxShadow="xl"
      borderRadius="2xl"
      overflow="hidden"
      // height={{ base: "400px", md: "500px", lg: "60vh" }}
      border="1px solid"
      borderColor="gray.100"
      _hover={{
        boxShadow: "2xl",
        transform: "translateY(-2px)",
      }}
      transition="all 0.3s ease"
    >
      <Card.Header p={6}>
        <HStack justify="space-between" align="center">
          <VStack align="start" gap={1}>
            <Heading size="lg" color="gray.800" _dark={{ color: "white" }}>
              Transaction Distribution
            </Heading>
          </VStack>
          <Box
            p={3}
            bg="blue.100"
            _dark={{ bg: "blue.900" }}
            borderRadius="xl"
          >
            <FcComboChart size={32} />
          </Box>
        </HStack>
      </Card.Header>

      <Card.Body p={6} pt={0}>
        <Box
          h="350px"
          position="relative"
          bg="linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(16, 185, 129, 0.05))"
          borderRadius="xl"
          p={4}
          _before={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1), transparent 50%)',
            borderRadius: 'xl',
            pointerEvents: 'none'
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              {/* 3D Shadow Effect */}
              <defs>
                <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
                  <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
                  <feOffset dx="2" dy="4" result="offset"/>
                  <feComponentTransfer>
                    <feFuncA type="linear" slope="0.3"/>
                  </feComponentTransfer>
                  <feMerge>
                    <feMergeNode/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
                <linearGradient id="salesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#22c55e" stopOpacity={1}/>
                  <stop offset="50%" stopColor="#16a34a" stopOpacity={1}/>
                  <stop offset="100%" stopColor="#15803d" stopOpacity={1}/>
                </linearGradient>
                <linearGradient id="suppliesGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#3b82f6" stopOpacity={1}/>
                  <stop offset="50%" stopColor="#2563eb" stopOpacity={1}/>
                  <stop offset="100%" stopColor="#1d4ed8" stopOpacity={1}/>
                </linearGradient>
                <linearGradient id="ordersGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#f59e0b" stopOpacity={1}/>
                  <stop offset="50%" stopColor="#d97706" stopOpacity={1}/>
                  <stop offset="100%" stopColor="#b45309" stopOpacity={1}/>
                </linearGradient>
                <linearGradient id="noDataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#e2e8f0" stopOpacity={1}/>
                  <stop offset="100%" stopColor="#cbd5e1" stopOpacity={1}/>
                </linearGradient>
              </defs>

              {/* Main 3D Pie Chart */}
              <Pie
                data={transformedData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={100}
                innerRadius={35}
                fill="#8884d8"
                dataKey="value"
                paddingAngle={3}
                animationBegin={0}
                animationDuration={2000}
                filter="url(#dropshadow)"
                startAngle={90}
                endAngle={450}
              >
                {transformedData.map((entry, index) => {
                  let gradientId = 'noDataGradient';

                  if (entry.name === 'Sales') gradientId = 'salesGradient';
                  else if (entry.name === 'Supplies') gradientId = 'suppliesGradient';
                  else if (entry.name === 'Orders') gradientId = 'ordersGradient';

                  return (
                    <Cell
                      key={`cell-${index}`}
                      fill={`url(#${gradientId})`}
                      stroke="#ffffff"
                      strokeWidth={3}
                      style={{
                        filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.15))',
                        cursor: 'pointer'
                      }}
                    />
                  );
                })}
              </Pie>

              {/* Enhanced Tooltip */}
              <Tooltip
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.98)',
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
                  backdropFilter: 'blur(10px)',
                  padding: '12px 16px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
                formatter={(value, name, props) => [
                  <span style={{ color: props.payload.color, fontWeight: 'bold' }}>
                    {value} transactions
                  </span>,
                  <span style={{ color: '#374151', fontWeight: '600' }}>{name}</span>,
                  props.payload.total ? (
                    <span style={{ color: '#059669', fontSize: '12px' }}>
                      Total: KSH {props.payload.total.toLocaleString()}
                    </span>
                  ) : null
                ]}
                labelStyle={{ color: '#1f2937', fontWeight: '600' }}
                cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
              />

              {/* Modern Legend */}
              <Legend
                verticalAlign="bottom"
                height={50}
                iconType="rect"
                wrapperStyle={{
                  paddingTop: '25px',
                  fontSize: '13px',
                  fontWeight: '500',
                  color: '#374151'
                }}
                formatter={(value, entry) => (
                  <span style={{
                    color: entry.color,
                    fontWeight: '600',
                    textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}>
                    {value}
                  </span>
                )}
              />
            </PieChart>
          </ResponsiveContainer>
        </Box>

        {/* Enhanced Transaction Stats */}
        <Grid templateColumns="repeat(1, 1fr)" gap={3} mt={6}>
          <Box
            textAlign="center"
            p={4}
            bg="linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05))"
            _dark={{borderColor: "blue.700", bg: "linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.1))" }}
            borderRadius="xl"
            border="1px solid"
            borderColor="blue.200"
            position="relative"
            overflow="hidden"
            _hover={{
              transform: "translateY(-2px)",
              boxShadow: "0 8px 25px rgba(59, 130, 246, 0.15)"
            }}
            transition="all 0.3s ease"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              bg: 'linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), transparent)',
            }}
          >
            <Text fontSize="3xl" fontWeight="bold" color="blue.600" _dark={{ color: "blue.300" }}>
              {totalTransactions}
            </Text>
            <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }} fontWeight="500">
              Total Transactions
            </Text>
          </Box>
          {/* <Box
            textAlign="center"
            p={4}
            bg="linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05))"
            _dark={{ bg: "linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1))" }}
            borderRadius="xl"
            border="1px solid"
            borderColor="green.200"
            _dark={{ borderColor: "green.700" }}
            position="relative"
            overflow="hidden"
            _hover={{
              transform: "translateY(-2px)",
              boxShadow: "0 8px 25px rgba(16, 185, 129, 0.15)"
            }}
            transition="all 0.3s ease"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              bg: 'linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.8), transparent)',
            }}
          >
            <Text fontSize="3xl" fontWeight="bold" color="green.600" _dark={{ color: "green.300" }}>
              {transformedData.filter(item => item.name !== 'No Data' && item.name !== 'No Transactions').length}
            </Text>
            <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }} fontWeight="500">
              Active Types
            </Text>
          </Box>
          <Box
            textAlign="center"
            p={4}
            bg="linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(245, 158, 11, 0.05))"
            _dark={{ bg: "linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1))" }}
            borderRadius="xl"
            border="1px solid"
            borderColor="amber.200"
            _dark={{ borderColor: "amber.700" }}
            position="relative"
            overflow="hidden"
            _hover={{
              transform: "translateY(-2px)",
              boxShadow: "0 8px 25px rgba(245, 158, 11, 0.15)"
            }}
            transition="all 0.3s ease"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '2px',
              bg: 'linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.8), transparent)',
            }}
          >
            <Text fontSize="2xl" fontWeight="bold" color="amber.600" _dark={{ color: "amber.300" }}>
              KSH {((pieData?.SALE?.total || 0) + (pieData?.SUPPLY?.total || 0) + (pieData?.ORDER?.total || 0)).toLocaleString()}
            </Text>
            <Text fontSize="sm" color="gray.600" _dark={{ color: "gray.400" }} fontWeight="500">
              Total Revenue
            </Text>
          </Box> */}
        </Grid>
      </Card.Body>
    </MotionCard>
  );
};
