'use client'

import  React,{ useState,useEffect} from "react";
import {insertBulk,fetchData2,insertData,updateData,deleteData,fetchImage } from '@/app/utils/pos';
import {Field as Fd ,DataList,Switch,List,Separator,Textarea,Center,Spinner ,Icon ,Text, Grid,Box,Tabs,GridItem,Card,FormatNumber,Heading,HStack, Button, Input,Flex, Stack,VStack,Link,SimpleGrid} from '@chakra-ui/react';
import {Category,Discount,ApiResponse,Status } from '@/app/utils/definitions'
// import Image from 'next/image'
import { Tag } from "@/components/ui/tag"
import { z } from "zod";
import { motion } from "motion/react"
import { useColorModeValue } from '@/components/ui/color-mode';
import { FiDownload,FiUpload,FiPlus,FiX,FiImage} from "react-icons/fi";
import { toaster } from "@/components/ui/toaster"
import { Field } from "@/components/ui/field"
import {NativeSelectField,NativeSelectRoot,} from "@/components/ui/native-select"
import {FileUploadDropzone,FileUploadTrigger,FileUploadRoot} from "@/components/ui/file-upload"
import {useAppSelector } from "@/lib/hooks";
import { uploadFiles } from "@/app/utils/uploadThing";
import { Image } from "@chakra-ui/react"
import { Tooltip } from "@/components/ui/tooltip"
import { deleteFilesThings } from "@/app/utils/uploadThingdel";


const productSchema = z.object({
  item_name: z.string().min(1, 'Item name is required'),
  item_model_no: z.string().optional(),
  item_selling: z.number().min(0, 'Selling price must be a positive number'),
  item_quantity: z.number().min(0, 'Quantity must be a positive number').default(0).optional(),
  item_cat_id: z.string().optional(),
  item_disc_id: z.string().optional(),
  item_pic_url: z.array(z.string()).optional(),
  item_buying: z.number().min(0, 'Buying price must be a positive number'),
  item_sub_cat: z.string().optional(),
  item_description: z.string().optional(),
  item_colors: z.string().optional(),
  item_sizes: z.string().optional(),
  item_tags: z.array(z.string()).optional(),
  item_brand: z.string().optional(),
  item_ispublished: z.boolean().optional()
});

export const ItemForm = ({ mode, item, onSubmit, onClose}) => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;


  const [formData, setFormData] = useState({
    item_name: '',
    item_model_no: '',
    item_selling: 0,
    item_quantity: 0,
    item_cat_id: '',
    item_disc_id: '',
    item_pic_url: [],
    item_buying: 0,
    item_sub_cat: '',
    item_description: '',
    item_availability: '',
    item_colors: '',
    item_sizes: '',
    item_tags: [],
    item_brand: '',
    item_ispublished: true,
  });

  const [categories, setCategories] = useState<Category[]>([]);
  const [discounts, setDiscounts] = useState<Discount[]>([]);
  const [status, setStatus] = useState<Status[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [acceptedFiles, setAcceptedFiles] = useState([]);
  const [imageUrls, setImageUrls] = useState([]);



  const handleFetch = async (endpoint, setState, errorTitle) => {
    try {
      const response = await fetchData2(endpoint, null, null,null,shop_id);
      setState(response.data);
    } catch (error) {
      toaster.error({
        title: errorTitle,
        description: error instanceof z.ZodError ? error.errors[0]?.message : 'An unexpected error occurred.',
      });
    }
  };

  useEffect(() => {
    if (mode == 'update' && item) {
      setFormData({
        item_name: item.item_name || '',
        item_model_no: item.item_model_no || '',
        item_selling: item.item_selling || 0,
        item_quantity: item.item_quantity || 0,
        item_cat_id: item.item_cat_id || '',
        item_disc_id: item.item_disc_id || '',
        item_pic_url: item.item_pic_url || [],
        item_buying: item.item_buying || 0,
        item_sub_cat: item.item_sub_cat || '',
        item_description: item.item_description || '',
        item_availability: item.item_availability || '',
        item_ispublished: item.item_ispublished || true,
        item_colors: item.item_colors || '',
        item_sizes: item.item_sizes || '',
        item_tags: item.item_tags || [],
        item_brand: item.item_brand || '',
      });
      setSelectedCategory(item.item_cat_id || '');

      if (item.item_pic_url?.length > 0) {
        // Promise.allSettled(
        //   item.item_pic_url.map(async (url) => {
        //     try {
        //       const fetchedUrl = await fetchImage(url);
        //       return { [url]: fetchedUrl };
        //     } catch (error) {
        //       return { [url]: null };
        //     }
        //   })
        // ).then(async (results) => {
        // const successfulUrls = item.item_pic_url
        //   .filter((result) => result.status == 'fulfilled' && result.value[Object.keys(result.value)[0]] !== null)
        //   .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        //   .map((result) => result.value);
        // console.log(successfulUrls);
        // console.log(item.item_pic_url);

        // const failedUrls = results
        //   .filter((result) => result.status == 'rejected' || result.value[Object.keys(result.value)[0]] == null)
        //   .map((result) => {
        //     if (result.status === 'fulfilled') {
        //       return Object.keys(result.value || {})[0];
        //     }
        //     return null;
        //   })
        //   .filter((key): key is string => key !== null);

          // if (failedUrls.length > 0) {
          //   try {
          //     await updateData('items', item.item_id, {
          //     item_pic_url: item.item_pic_url.filter((url:string) => !failedUrls.includes(url)),
          //     });
          //     setFormData((prev) => ({
          //       ...prev,
          //       item_pic_url: prev.item_pic_url.filter((url:string) => !failedUrls.includes(url)),
          //     }));
          //     toaster.create({
          //       title: "Partial Image Load",
          //       description: `Some images failed to load and were removed ({failedUrls.length}/{item.item_pic_url.length}).`,
          //       type: "warning",
          //       // duration: 900,
          //     });
          //   } catch (error) {
          //      toaster.create({
          //       title: "Database Update Failed",
          //       description: 'Failed to remove invalid image URLs from the database.',
          //       type: "error",
          //     });
          //   }
          // }
          setImageUrls(item.item_pic_url);
        }
      // );
      // }
    }

    const loadData = async () => {
      setIsLoading(true);
      await Promise.allSettled([
        handleFetch('discount/shop', setDiscounts, 'Failed to load discounts.'),
        handleFetch('categories/shop', setCategories, 'Failed to load categories.'),
      ]);
      setIsLoading(false);
    };

    const loadStatus = async () => {
      setIsLoading(true);
      await handleFetch('status', setStatus, 'Failed to load status.');
      setIsLoading(false);
    };
    if (mode == 'update') loadStatus();
    loadData();
  }, [mode,item]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string | string[]) => {
      setFormData((prev) => ({ ...prev, [name]: value }));
    };

  const handleSubmit = async () => {
      try {
        setIsLoading(true);
        const uploadFormData = new FormData();
        acceptedFiles.forEach((file) => uploadFormData.append("item_pic_url", file));
        let uploadedFiles = [];
        let deletefiles = [];
        // if (acceptedFiles.length > 0) {
        //   const response = await insertData("upload_pic", uploadFormData);
        //   if (!response.files || !Array.isArray(response.files)) {throw new Error("Invalid response format");}
        //   uploadedFiles = response?.files;
        // }
        if (acceptedFiles.length > 0) {
          console.log(acceptedFiles);
          uploadedFiles = await uploadFiles(acceptedFiles, "imageUploader");
          console.log(uploadedFiles);
        }
        const updatedItemPicUrl = [...formData.item_pic_url, ...uploadedFiles];
        const validatedData = productSchema.parse({
          ...formData,
          item_selling: parseFloat(formData?.item_selling),
          item_quantity: parseFloat(formData?.item_quantity),
          item_buying: parseFloat(formData?.item_buying),
          item_pic_url: updatedItemPicUrl
        });
    
        const productData = {
          ...validatedData,
          item_availability: mode == 'insert' ? (Number(formData.item_quantity) > 0 ? 1 : 2) : formData.item_availability,
          shop_id,
        };
        if (mode == 'insert') {
          await insertData('items', productData);
          toaster.create({ title: 'Product added successfully!', type: 'success' });
        } else if (mode == 'update') {
          await updateData('items', item.item_id, productData);
          toaster.create({ title: 'Product updated successfully!', type: 'success' });
        }
        onSubmit();
        onClose();
      } catch (error) {
        toaster.create({
          title: mode == 'insert' ? 'Failed to add product.' : 'Failed to update product.',
          description: error instanceof z.ZodError ? error.errors[0]?.message : 'An unexpected error occurred.',
          type: 'error',
        });
      } finally {
        setIsLoading(false);
      }
    };

  const handleFileUpload = async (details: any) => {
    const { files } = details;
    if (!details || !details.files || !Array.isArray(details.files)) return;
    if (!files.length) return;
    const existingFiles = new Set(acceptedFiles.map(file => file.name));
    const newFiles = files.filter(file => !existingFiles.has(file.name));
    if (newFiles.length == 0) {
      toaster.create({
        title: "Duplicate File",
        description: "A file with the same name already exists. Please upload a different file.",
        type: "warning",
      });
      return;
    }
    setAcceptedFiles(prevFiles => [...prevFiles, ...newFiles]);
    details= [];
  };

  const handleFileRemove = (fileToRemove) => {
    try {
      setAcceptedFiles(prevFiles => prevFiles.filter(file => file !== fileToRemove));
      setFormData(prev =>({...prev, item_pic_url: prev.item_pic_url.filter(file => file !== fileToRemove.name),}));
    } catch (error) {
      toaster.create({
        title: "File Rejected",
        description: 'Failed to remove file: ' + error.message,
        type: "error",
      });
    }
  };

  const handleFileReject = ( details ) => {

    if (!details.files || details.files.length == 0) return;
    const rejectedFiles = details.files.map((fileObj) => {
      const fileName = fileObj.file.name;
      const errorMessages = fileObj.errors?.map(err => err).join(", ") || "Unknown error";
      return `File: {fileName}, Reason: {errorMessages}`;
    });
    toaster.create({
      title: "File Rejected - Upload only CSV/XLSX",
      description: rejectedFiles.join("; "),
      type: "error",
    });
  };


  const handleImageRemove = async (imageUrl: string) => {
    try {
      setImageUrls((prevUrls) =>prevUrls.filter((urlObj) => Object.keys(urlObj)[0] != imageUrl));
      setFormData((prev) => ({...prev,item_pic_url: prev.item_pic_url.filter((url) => url != imageUrl),}));
      // await deleteData ('delete_pic',imageUrl);
      await deleteFilesThings([imageUrl]);
      toaster.create({title: "Success",description: 'Image was deleted' ,type: "success",});
    } catch (error) {
      toaster.create({title: "File Rejected",description: 'Failed to remove file: ' + error.message,type: "error",});
    }
  };

  const selectedCategoryData = categories.find((cat) => cat.category_id == selectedCategory);
  const subCategories = selectedCategoryData?.sub_category || [];
  const selectBg = useColorModeValue('white', 'gray.700');
  const selectColor = useColorModeValue('gray.800', 'white');
  const selectBorderColor = useColorModeValue('gray.200', 'gray.600');
  const selectHoverBorderColor = useColorModeValue('gray.300', 'gray.500');
  const selectFocusBorderColor = 'blue.500';
  const selectFocusBoxShadow = '0 0 0 1px blue.500';

  return (
    <Stack px={6} py={4} 
    // borderRadius="lg" 
    // boxShadow="lg"
    >
      {isLoading ? (
        <Center h="200px">
          <Spinner size="xl" color="red.600"/>
        </Center>
      ) : (
        <>
          <HStack gap={6} align="flex-start">
            <Field label="Name" required flex="1">
              <Input
                placeholder="Item/Product Name"
                name="item_name"
                value={formData.item_name}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Model No" flex="1">
              <Input
                placeholder="Model Number/Serial No"
                name="item_model_no"
                value={formData.item_model_no}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Quantity" required flex="1">
              <Input
                placeholder="Quantity/Amount"
                name="item_quantity"
                value={formData.item_quantity}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
          </HStack>

          <HStack gap={6} align="flex-start">
          <Field label="Category" flex="1" mb={4}>
            <NativeSelectRoot size="md">
              <NativeSelectField
                name="item_cat_id"
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  handleSelectChange('item_cat_id', e.target.value);
                }}
                bg={selectBg}
                color={selectColor}
                borderColor={selectBorderColor}
                _hover={{ borderColor: selectHoverBorderColor }}
                _focus={{
                  borderColor: selectFocusBorderColor,
                  boxShadow: selectFocusBoxShadow,
                  bg: selectBg,
                }}
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.category_id} value={category.category_id}>
                    {category.category_name}
                  </option>
                ))}
              </NativeSelectField>
            </NativeSelectRoot>
          </Field>

          {/* Always render the Sub Category field, but conditionally disable it */}
          <Field label="Sub Category" flex="1">
            <NativeSelectRoot size="md">
              <NativeSelectField
                name="item_sub_cat"
                value={formData.item_sub_cat}
                onChange={(e) => handleSelectChange(e.currentTarget.name, e.currentTarget.value)}
                bg={selectBg}
                color={selectColor}
                borderColor={selectBorderColor}
                _hover={{ borderColor: selectHoverBorderColor }}
                _focus={{
                  borderColor: selectFocusBorderColor,
                  boxShadow: selectFocusBoxShadow,
                  bg: selectBg,
                }}
                disabled={!selectedCategory}
              >
                <option value="">Select a sub-category</option>
                {selectedCategory &&
                  subCategories.map((subCat, subIndex) => (
                    <option key={`{selectedCategory}-{subIndex}`} value={subCat}>
                      {subCat}
                    </option>
                  ))}
              </NativeSelectField>
            </NativeSelectRoot>
          </Field>
            <Field label="Select Discount" flex="1">
              <NativeSelectRoot size="md">
                <NativeSelectField
                  name="item_disc_id"
                  value={formData.item_disc_id}
                  onChange={(e) => handleSelectChange(e.currentTarget.name, e.currentTarget.value)}
                  bg={useColorModeValue('white', 'gray.700')}
                  color={useColorModeValue('gray.800', 'white')}
                  borderColor={useColorModeValue('gray.200', 'gray.600')}
                  _hover={{ borderColor: useColorModeValue('gray.300', 'gray.500') }}
                  _focus={{
                    borderColor: 'blue.500',
                    boxShadow: '0 0 0 1px blue.500',
                    bg: useColorModeValue('white', 'gray.700'),
                  }}
                >
                  <option value="">Select a discount</option>
                  {discounts.map((discount) => (
                    <option key={discount.discount_id} value={discount.discount_id}>
                      {discount.discount_name}
                    </option>
                  ))}
                </NativeSelectField>
              </NativeSelectRoot>
            </Field>
          </HStack>

          <Stack direction={{ base: 'column', md: 'row' }} gap={6} align="flex-start">
            <Field label="Selling Price" required flex="1">
              <Input
                placeholder="Price Per Item"
                name="item_selling"
                value={formData.item_selling}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Buying Price" flex="1">
              <Input
                placeholder="Amount"
                name="item_buying"
                value={formData.item_buying}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
            {mode == 'update' ?
              <Field label="Status" flex="1">
                <NativeSelectRoot size="md">
                  <NativeSelectField
                    name="item_availability"
                    value={formData.item_availability}
                    onChange={(e) => handleSelectChange(e.currentTarget.name, String(Number(e.currentTarget.value)))}
                    bg={useColorModeValue('white', 'gray.700')}
                    color={useColorModeValue('gray.800', 'white')}
                    borderColor={useColorModeValue('gray.200', 'gray.600')}
                    _hover={{ borderColor: useColorModeValue('gray.300', 'gray.500') }}
                    _focus={{
                      borderColor: 'blue.500',
                      boxShadow: '0 0 0 1px blue.500',
                      bg: useColorModeValue('white', 'gray.700'),
                    }}
                  >
                    <option value="">Select a status</option>
                    {status.map((status) => (
                      <option key={status.status_id} value={status.status_id}>
                        {status.status_name}
                      </option>
                    ))}
                  </NativeSelectField>
                </NativeSelectRoot>
              </Field>:null}

              <Field label="Published" flex="1">
          <NativeSelectRoot size="md">
            <NativeSelectField
              name="item_ispublished"
              value={formData.item_ispublished}
              onChange={(e) => handleSelectChange(e.currentTarget.name, e.currentTarget.value)}
              bg={useColorModeValue('white', 'gray.700')}
              color={useColorModeValue('gray.800', 'white')}
              borderColor={useColorModeValue('gray.200', 'gray.600')}
              _hover={{ borderColor: useColorModeValue('gray.300', 'gray.500') }}
              _focus={{
                borderColor: 'blue.500',
                boxShadow: '0 0 0 1px blue.500',
                bg: useColorModeValue('white', 'gray.700'),
              }}
            >
              <option value={true}>Yes</option>
              <option value={false}>No</option>
            </NativeSelectField>
          </NativeSelectRoot>
        </Field>
      </Stack>
          <Stack direction={{ base: 'column', md: 'row' }} gap={6} align="flex-start">
            <Field label="Color" flex="1">
              <Input
                placeholder="Color"
                name="item_colors"
                value={formData.item_colors}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Size" flex="1">
              <Input
                placeholder="Size"
                name="item_sizes"
                value={formData.item_sizes}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
            <Field label="Brand" flex="1">
          <Input
            placeholder="Brand"
            name="item_brand"
            value={formData.item_brand}
            onChange={handleChange}
            _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
          />
        </Field>
          </Stack>
          <Stack direction={{ base: 'column', md: 'row' }} gap={6} align="flex-start">
          <Field label="Description" flex="2">
              <Textarea
                placeholder="Describe the product"
                name="item_description"
                value={formData.item_description}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
            <Field label="Tags" flex="2">
              <Input
                placeholder="Tags (comma separated)"
                name="item_tags"
                value={formData.item_tags.join(', ')}
                onChange={(e) => handleSelectChange('item_tags', e.target.value.split(',').map(tag => tag.trim()))}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
        
          </Stack>
          <Stack direction={{ base: 'column', md: 'row' }} gap={6} align="flex-start">
        

            <Field label="Upload Image" flex="2">
              <FileUploadRoot maxFiles={5} maxFileSize={5000000} onFileAccept={handleFileUpload} onFileReject={handleFileReject} allowDrop={true} accept={["image/png","image/jpeg","image/jpg","image/webp","image/gif","image/bmp","image/svg+xml","image/tiff","image/heic","image/heif",]}>
                <HStack>
                    <FileUploadTrigger asChild>
                      <Button variant="outline" size="sm"><FiUpload />Upload file</Button>
                    </FileUploadTrigger>
                    <Box flex="1">
                      {acceptedFiles.length > 0 ? (
                        <List.Root>
                          {acceptedFiles.map((file, index) => (
                            <List.Item key={index} display="flex" alignItems="center" gap={2}>
                              <Box display="flex" alignItems="center">
                                <Text fontSize="md">{file.name}</Text>
                                <Icon
                                  ml={6}
                                  color="tomato"
                                  onClick={() => handleFileRemove(file)}
                                  cursor="pointer"
                                >
                                  <FiX />
                                </Icon>
                              </Box>
                            </List.Item>
                          ))}
                        </List.Root>
                      ) : (
                        <Text>No Images uploaded yet.</Text>
                      )}
                    </Box>
                </HStack>
              </FileUploadRoot>
            </Field>
            {mode == 'update' && (
                  <Field label="Images" flex="2">
                    <HStack>
                      {formData.item_pic_url.map((imageObj, index) => {
                        return(
                        <Box key={index} position="relative" width="3rem" height="3rem" borderRadius="50%" overflow="hidden"   _hover={{'& > .delete-icon': {opacity: 1,},'& > img': {opacity: 0.5,},}}>
                          <Image
                           src={imageObj}
                          //  width={100} 
                           objectFit="fill"
                           loading="eager"
                          //  height={100} 
                          style = {{objectFit:"fill",width:'3rem',height:'3rem'}} 
                          alt={`Item Image {index}`
                          }/>
                          <Icon position="absolute" top="50%" left="50%" transform="translate(-50%, -50%)" color="red" cursor="pointer" opacity={0}  transition="opacity 0.2s" className="delete-icon" onClick={() => handleImageRemove(imageObj)} boxSize={6}><FiX/></Icon>
                          </Box>)})}
                    </HStack>
                  </Field>
                )}
          </Stack>

          <HStack justify="flex-end" mt={2}>
            <Button w="10rem" colorScheme="orange" variant="outline" _hover={{ bg: 'red.100', transform: 'translateY(-2px)' }} transition="all 0.2s" onClick={onClose} > Discard </Button>
            <Button w="13rem" colorScheme="blue" onClick={handleSubmit} _hover={{ transform: 'translateY(-2px)' }} transition="all 0.2s" > {mode == 'insert' ? 'Add' : 'Update'}</Button>
          </HStack>
        </>
      )}
    </Stack>
  );
};


const DiscountSchema = z.object({
  discount_name: z.string().min(1, "Product name is required"),
  discount_type: z.string().min(1, "Product type is required"),
  discount_amount: z.string().refine((val) => !isNaN(Number(val)), "Amount must be a number"),
});

export const Add_discounts = ({ mode,id }) => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const [errors, setErrors] = useState('');
  const [fields, setFields] = useState([
    { discount_name: '', shop_id: shop_id, discount_type: '0', discount_amount: '' },
  ]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchDiscounts = async () => {
    try {
      const data = await fetchData2("discount",null,null,{ shop_id },id);
      if (data && Array.isArray(data.data) && data.data.length > 0) {
        const validatedData = data.data as { discount_name: string; shop_id: string; discount_type: string; discount_amount: string }[];
        setFields(validatedData);
      }
    } catch (error) {
      console.error("Failed to fetch discounts", error);
    }
  };
  useEffect(() => {
    if (mode == "update") {
 
      fetchDiscounts();
    }
  }, [shop_id, mode]);

  const discount_type = [
    { discount_type_id: '0', discount_type_name: 'Percent' },
    { discount_type_id: '1', discount_type_name: 'Float' }
  ];

  const addField = () => {
    setFields([...fields, { shop_id, discount_name: '', discount_amount: '', discount_type: '0' }]);
  };

  const removeField = (index: number) => {
    setFields(fields.filter((_, i) => i !== index));
  };

  const handleFieldChange = (index: number, fieldName: string, value: string) => {
    setFields((prev) => {
      const updatedFields = [...prev];
      updatedFields[index][fieldName] = value;
      return updatedFields;
    });
  };

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      fields.forEach((field) => DiscountSchema.parse(field));
      if( mode == "update"){
        await updateData("discount",id,fields);
      }else{
        await insertData('discount', fields);
      }
      toaster.create({
        title: mode == "update" ? "Discounts updated successfully!" : "Discounts added successfully!",
        type: 'success',
      });
    } catch (error) {
      toaster.create({
        title: 'Failed to process discount.',
        description: error instanceof z.ZodError ? error.errors[0]?.message : 'An unexpected error occurred.',
        type: 'error',
      });
    }
    finally{
      setIsLoading(false)
      setFields([])
    }
  };

  return (
    <VStack gap={6} p={6} align="stretch" borderRadius="xl" boxShadow="lg">
      {fields?.map((field, index) => (
        <HStack key={index} align="center" justify="center">
          <Field label="Name" required>
            <Input
              value={field.discount_name}
              onChange={(e) => handleFieldChange(index, 'discount_name', e.target.value)}
              placeholder="name"
              variant="outline"
            />
          </Field>
          <Field label="Amount">
            <Input
              value={field.discount_amount}
              onChange={(e) => handleFieldChange(index, 'discount_amount', e.target.value)}
              placeholder="amount"
              variant="outline"
            />
          </Field>
          <Field label="Discount Type:">
            <NativeSelectRoot size="md">
              <NativeSelectField
                name="discount_type"
                value={field.discount_type}
                onChange={(e) => handleFieldChange(index, 'discount_type', e.currentTarget.value)}>
                {discount_type.map((discount) => (
                  <option key={discount.discount_type_id} value={discount.discount_type_id}>
                    {discount.discount_type_name}
                  </option>
                ))}
              </NativeSelectField>
            </NativeSelectRoot>
          </Field>
          <Box display="flex" alignItems="center" mt={6}>
            <Icon fontSize="24px" color="tomato" onClick={() => removeField(index)} cursor="pointer">
              <FiX />
            </Icon>
          </Box>
        </HStack>
      ))}

      <HStack justify="center" align="center">
        <Icon onClick={addField} fontSize="24px" color="blue" cursor="pointer">
          <FiPlus />
        </Icon>
      </HStack>

      <HStack justify="flex-end" mt={6}>
        <Button w="10rem" colorScheme="orange" variant="outline">
          Discard
        </Button>
        <Button w="13rem" colorScheme="blue" onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? 'Loading ... ' :mode == "update" ? "Update Discount" : "Add Discount"}
        </Button>
      </HStack>
    </VStack>
  );
};



const CategorySchema = z.object({
  category_name: z.string().min(1, "Category name is required"),
});

export const Add_category = ({ mode,id }) => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;

  const [fields, setFields] = useState<Category[]>([{category_name: "", shop_id: shop_id, sub_category: [] },]);
  const [errors, setErrors] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (mode == "update") {
      const fetchCategories = async () => {
        try {
          const data = await fetchData2("category",null,null,{ shop_id },id);
          if (data && data.length > 0) setFields(data);
        } catch (error) {
          console.error("Failed to fetch categories", error);
        }
      };
      fetchCategories();
    }
  }, [shop_id, mode]);

  const addField = () => {
    setFields([...fields, { category_name: "", sub_category: [], shop_id }]);
  };

  const removeField = (index: number) => {
    setFields(fields.filter((_, i) => i !== index));
  };

  const handleFieldChange = (index: number, fieldName: string, value: string) => {
    setFields((prev) => {
      const updatedFields = [...prev];
      updatedFields[index][fieldName as keyof Category] = value;
      return updatedFields;
    });
  };

  const handleAddSubCategory = (index: number, value: string) => {
    if (!value.trim()) return;

    setFields((prev) => {
      const updatedFields = [...prev];
      if (!updatedFields[index].sub_category.includes(value.trim())) {
        updatedFields[index].sub_category.push(value.trim());
      }
      return updatedFields;
    });
  };

  const handleRemoveSubCategory = (index: number, subCategoryIndex: number) => {
    setFields((prev) => {
      const updatedFields = [...prev];
      updatedFields[index].sub_category.splice(subCategoryIndex, 1);
      return updatedFields;
    });
  };

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      fields.forEach((field) => CategorySchema.parse(field));
      // await Promise.all(fields.map((field) => insertData<Category>("category", field)));
      if( mode == "update"){
        await updateData("category",id,fields);
      }else{
        await insertData("category", fields);
      }
      toaster.create({
        title: mode == "update" ? "Categories updated successfully!" : "Categories added successfully!",
        type: "success",
      });
    } catch (error) {
      toaster.create({
        title: "Failed to process categories.",
        description: error instanceof z.ZodError ? error.errors[0]?.message : "An unexpected error occurred.",
        type: "error",
      });
     
    }finally{
      setIsLoading(false)
      setFields([])
    }
  };

  return (
    <VStack gap={6} align="stretch" p={6} borderRadius="xl" boxShadow="lg">
      {fields?.map((field, index) => (
        <HStack key={index} gap={4} align="center" justifyContent="space-between" borderRadius="lg">
          <Field label="Category Name" required flex="1">
            <Input
              width="100%"
              value={field.category_name}
              onChange={(e) => handleFieldChange(index, "category_name", e.target.value)}
              placeholder="Enter category"
              _focus={{ borderColor: "blue.500", boxShadow: "0 0 0 1px blue.500" }}
            />
          </Field>

          <Field label="Sub category" flex="1">
            <VStack align="start" width="100%">
              <Input
                width="100%"
                placeholder="Enter sub-category and press Enter"
                onKeyDown={(e) => {
                  if (e.key == "Enter") {
                    handleAddSubCategory(index, e.currentTarget.value);
                    e.currentTarget.value = "";
                  }
                }}
                _focus={{ borderColor: "blue.500", boxShadow: "0 0 0 1px blue.500" }}
              />
              <HStack gap={2} wrap="wrap">
                {field.sub_category.map((subCategory, subIndex) => (
                  <Tag key={subIndex} variant="solid">
                    <Text textStyle="xs" _hover={{ cursor: "pointer" }}>
                      {subCategory}{" "}
                      <Icon fontSize="lg" onClick={() => handleRemoveSubCategory(index, subIndex)}>
                        <FiX />
                      </Icon>
                    </Text>
                  </Tag>
                ))}
              </HStack>
            </VStack>
          </Field>

          <Box display="flex" alignItems="center" gap={2} mt={6}>
            <Icon
              fontSize="24px"
              color="red.500"
              onClick={() => removeField(index)}
              cursor="pointer"
              _hover={{ color: "red.600", transform: "scale(1.1)" }}
              transition="all 0.2s"
            >
              <FiX />
            </Icon>
          </Box>
        </HStack>
      ))}

      <HStack justify="center" align="center">
        <Icon
          fontSize="24px"
          color="blue.500"
          onClick={addField}
          cursor="pointer"
          _hover={{ color: "blue.600", transform: "scale(1.1)" }}
          transition="all 0.2s"
        >
          <FiPlus />
        </Icon>
      </HStack>

      <HStack justify="flex-end" mt={6}>
        <Button w="10rem" colorScheme="orange" variant="outline">
          Discard
        </Button>
        <Button w="13rem" colorScheme="blue" onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? 'Loading ... ' : mode == "update" ? "Update Category" : "Add Category"}
        </Button>
      </HStack>
    </VStack>
  );
};





const PaymentSchema = z.object({
  payment_method_id: z.string().optional(),
  payment_method_name: z.string().min(1, "Payment method name is required"),
  payment_method_description: z.string().optional(),
  payment_number: z.string().optional(),
  shop_id: z.string().min(1, "Shop ID is required"),
  status: z.boolean().optional(),
});


export const Add_payment = () => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const [fields, setFields] = useState([]);
  const [selectedPayments, setSelectedPayments] = useState([]);
  const [fetched, setFetched] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchPayments = async () => {
    setIsLoading(true)
    try{
    const data = await fetchData2("paymeth",null, null,null, shop_id);
    const data_ =data.data ? data.data : data
    setFields(data_);
    setSelectedPayments(data_.map((field) => field.payment_method_name));
    setFetched(data_.length > 0);
    }catch(error){
      toaster.create({
        title: "Failed to get Payments.",
        description: error instanceof z.ZodError ? error.errors[0]?.message : "An unexpected error occurred.",
        type: "error",
      });
    }
    finally{
      setIsLoading(false)
    }

  };



  const paymentOptions = [
    { name: "mpesa_express", label: "M-Pesa Express (STK)" },
    { name: "cash", label: "Cash" },
    { name: "mpesa_till_paybill", label: "M-Pesa Till/Paybill" },
  ];

  const togglePayment = (payment) => {
    if (selectedPayments.includes(payment)) {
      setSelectedPayments(selectedPayments.filter((p) => p != payment));
      setFields(fields.filter((field) => field.payment_method_name != payment));
    } else {
      setSelectedPayments([...selectedPayments, payment]);
      setFields([...fields,
            {
              payment_method_name: payment,
              payment_number: "",
              payment_method_description: "",
              shop_id,
              status: true,
            },
        ]);
    }
  };

  const handleFieldChange = (index, fieldName, value) => {
    const updatedFields = [...fields];
    updatedFields[index][fieldName] = value;
    setFields(updatedFields);
  };

  const handleFieldChange_switch = (paymentMethodid, fieldName, value) => {
    setFields((prevFields) => {
      const updatedFields = prevFields.map((field) =>
        field.payment_method_id == paymentMethodid
          ? { ...field, [fieldName]: value }
          : field
      );
      return updatedFields;
    });
  };
  

  const handleSubmit = async () => {
    try {
      const newFields = fields.filter(field => !field.payment_method_id);
      if (newFields.length == 0) {
        toaster.create({ title: "No new payments to insert.", type: "info" });
        return;
      }
      newFields.forEach((field) => {PaymentSchema.parse(field);});
      await insertData("paymeth", newFields);
      toaster.create({title: "Payment inserted successfully!",type: "success"});
    } catch (error) {
      toaster.create({
        title: "Failed to process Payment.",
        description: error instanceof z.ZodError ? error.errors[0]?.message : "An unexpected error occurred.",
        type: "error",
      });
    }finally{
      await fetchPayments();
      // await setSelectedPayments([]);
    }
  };

  const updatePaymentMethod = async (id) => {

    try {
      const updatedField = fields.find(field => field.payment_method_id === id);
      
      if (!updatedField) {
        toaster.create({ title: "Payment method not found.", type: "error" });
        return;
      }
      PaymentSchema.parse(updatedField);

      await updateData("paymeth",id,updatedField);
      toaster.create({title: "Payment updated successfully!",type: "success"});
    } catch (error) {
      toaster.create({
        title: "Failed to process Payment.",
        description: error instanceof z.ZodError ? error.errors[0]?.message : "An unexpected error occurred.",
        type: "error",
      });
    }finally{
      // await setSelectedPayments([]);
    }
  };

  useEffect(() => {
    fetchPayments();
}, [shop_id]);
  

  return (
    <VStack gap={6} align="stretch" p={6} borderRadius="xl" boxShadow="lg"  >
      {isLoading ? (
        <Center h="200px">
          <Spinner size="xl" color="red.600"/>
        </Center>
      ) : (
        <>
      <Box>
        <Text fontWeight="bold" mb={2}>Select Payment Method</Text>

      
          <VStack gap={4} align="stretch">
            {paymentOptions.map((option) => {
                  const field = fields ?.find(f => f.payment_method_name == option.name);
              return(
              <Box key={option.name} border="1px" borderColor="gray.200" borderRadius="lg" p={4}>
                <Switch.Root
                  checked={selectedPayments.includes(option.name)}
                  onCheckedChange={() => togglePayment(option.name)}
                  colorPalette="blue"
                  disabled={field?.status}
                >
                  <Switch.HiddenInput />
                  <Switch.Control>
                    <Switch.Thumb />
                  </Switch.Control>
                  <Switch.Label>{option.label}</Switch.Label>
                </Switch.Root>

              
                  {selectedPayments.includes(option.name) && (
                  <HStack justify="space-between" w="full" gap={4}>
                    <VStack gap={4} mt={4} flex={1}>
                      <Fd.Root>
                        <Fd.Label fontWeight="bold">Number</Fd.Label>
                        <Input
                          placeholder="Enter number"
                          value={fields.find(f => f.payment_method_name == option.name)?.payment_number || ""}
                          onChange={(e) =>
                            handleFieldChange(
                              fields.findIndex(f => f.payment_method_name == option.name),
                              "payment_number",
                              e.target.value
                            )
                          }
                        />
                      </Fd.Root>

                      <Fd.Root>
                        <Fd.Label fontWeight="bold">Description</Fd.Label>
                        <Input
                          placeholder="Enter description"
                          value={fields.find(f => f.payment_method_name == option.name)?.payment_method_description || ""}
                          onChange={(e) =>
                            handleFieldChange(
                              fields.findIndex(f => f.payment_method_name == option.name),
                              "payment_method_description",
                              e.target.value
                            )
                          }
                        />
                      </Fd.Root>
                    </VStack>
                    <Box flex={1} p={4} borderRadius="lg" boxShadow="md">
                    {fields.length > 0 ? (
                          <DataList.Root>
                            {fields
                              .filter((field) => field.payment_method_name.toLowerCase() == option.name.toLowerCase())
                              .map((field, index) => (
                                <DataList.Item key={field.payment_method_name}>
                                  <DataList.ItemLabel>{field.payment_method_name}</DataList.ItemLabel>
                                  <DataList.ItemValue>{field.payment_number || "N/A"}</DataList.ItemValue>
                                  <DataList.ItemValue >{field.payment_method_description || "N/A"}</DataList.ItemValue>
                                  <DataList.ItemValue><Text fontWeight="semibold">Status : </Text>
                                  <Switch.Root 
                                            size="lg" 
                                            disabled={!field.payment_method_id}
                                            ml={2} 
                                            mb={2}
                                            name={field.payment_method_name}
                                            checked={field.status}
                                            onCheckedChange={(checked) =>
                                              handleFieldChange_switch(field.payment_method_id, "status", checked.checked)
                                            }
                                        >
                                        <Switch.HiddenInput />
                                        <Switch.Control>
                                          <Switch.Thumb />
                                        </Switch.Control>
                                        <Switch.Label>
                                          {field.status == 1 ? "Active" : "Inactive"}
                                        </Switch.Label>
                                      </Switch.Root>

                                  </DataList.ItemValue>

                                  <Button onClick={() => updatePaymentMethod(field.payment_method_id)} size="md" disabled={!field.payment_method_id}>Update payment</Button>
                                 </DataList.Item>
                              ))}
                          </DataList.Root>
                        ) : (
                          <Text>Not Active</Text>
                        )}
                    </Box>

                  </HStack>
                  )}


             
              </Box>
          )})}
          </VStack>
      
      </Box>
      <HStack justify="flex-end" mt={6}>
        <Button
          w="13rem"
          colorScheme="blue"
          onClick={handleSubmit}
        >Add Payment Methods</Button>
      </HStack>
      </>)}
    </VStack>
  );
};





export const Add_items =() => {
  const user = useAppSelector((state) => state.user.currentUser);
const shop_id = user?.currentshop;
        const [acceptedFiles, setAcceptedFiles] = useState([]);

        const handleFileChange = (details) => {
          try {
            const existingFiles = new Set(acceptedFiles.map(file => file.name));
            const newFiles = details.files.filter(file => !existingFiles.has(file.name));
            if (newFiles.length > 0) {
              setAcceptedFiles(prevFiles => [...prevFiles, ...newFiles]);
            } else {
              toaster.create({
                title: "Duplicate File",
                description: "A file with the same name already exists. Please upload a different file.",
                type: "warning",
              });
            }
          } catch (error) {
            toaster.create({
              title: "File Rejected",
              description: error.message || "An error occurred during the file upload.",
              type: "error",
            });
          } 

          };

          const handleFileRemove = (fileToRemove) => {
            try{
                setAcceptedFiles((prevFiles) => prevFiles.filter(file => file != fileToRemove));
              }catch(error){
              toaster.create({
              title: "File Rejected",
              description: 'Upload only CSV/XLSX'+error,
              type: "error",
            });
          }
          };

        const handleFileReject = ( details ) => {
          if (!details.files || details.files.length == 0) return;
          const rejectedFiles = details.files.map((fileObj) => {
            const fileName = fileObj.file.name;
            const errorMessages = fileObj.errors?.map(err => err).join(", ") || "Unknown error";
            return `File: {fileName}, Reason: {errorMessages}`;
          });
          toaster.create({
            title: "File Rejected - Upload only CSV/XLSX",
            description: rejectedFiles.join("; "),
            type: "error",
          });
        };

      

        const handleSubmit = async () => {
          try {
            const formData = new FormData();
            acceptedFiles.forEach((file) => {
              formData.append("file", file);
            });
            await insertBulk(`uploadbulk_items/{shop_id}`,formData)
            toaster.create({
              title: "Upload complete",
              description: 'The data upload and item processing is complete',
              type: "success",
            });
          } catch (error) {
            toaster.create({
              title: "Upload failed",
              description: error.message,
              type: "error",
            });
          }
        };
        

  
return(
  <Stack gap={4} p={6} borderRadius="xl" boxShadow="lg">


          <Heading size="lg" color="gray.700">Upload Bulk Items</Heading>
          <HStack gap={2} align="start">
          {/* maxFiles={5} */}
            <FileUploadRoot maxFileSize={500000000} accept={["text/csv", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]} onFileAccept={handleFileChange} onFileReject={handleFileReject} maxW="45%" >
              <FileUploadDropzone label="Drag and drop files here" description="Only CSV and XLSX files up to 500MB are allowed." />
            </FileUploadRoot>

              <VStack>
                <Box flex="1">
                  <Text fontSize="lg">Uploaded Files:</Text>
                  <br/>
                      {acceptedFiles.length > 0 ? (
                        <List.Root>
                          {acceptedFiles.map((file, index) => (
                            <List.Item key={index} display="flex" alignItems="center" gap={2}>
                              <Box display="flex" alignItems="center">
                              <Text fontSize="md">{file.name}</Text><Icon ml={6} 
                              color="tomato"
                              onClick={() => handleFileRemove(file)}
                              cursor="pointer"><FiX /></Icon>
                              </Box>
                            </List.Item>
                          ))}
                        </List.Root>
                      ) : (
                        <Text>No files uploaded yet.</Text>
                      )}
                  </Box>
                    <HStack justify="flex-end" mt={6}>
                            <Button
                              w="10rem"
                              colorScheme="orange"
                              variant="outline"
                              onClick={()=>setAcceptedFiles([])}
                              _hover={{ bg: "orange.50", transform: "translateY(-2px)" }}
                              transition="all 0.2s"
                            >
                              Discard
                            </Button>
                            <Button
                              w="13rem"
                              colorScheme="blue"
                              onClick={handleSubmit}
                              _hover={{ transform: "translateY(-2px)" }}
                              transition="all 0.2s"
                            >
                              Submit 
                            </Button>
                      </HStack>
                    </VStack>
              </HStack>

              <Box  borderRadius="lg" overflow="hidden">
              
                <HStack justify="space-between" align="center" mb={4}>
                  <Text fontSize="sm">Sample input :</Text>
                  <Link href="/template.xlsx" download="template.xlsx">
                    <Button variant="outline" colorScheme="blue">
                      <FiDownload /> Download Template
                    </Button>
                  </Link>
                </HStack>
                
                  <Image src='/pos/sampleupload.png' 
                            alt="Uploaded Image"     
                            style = {{objectFit:"contain",width:'auto',height:'auto'}}
                            height={5000}
                            width={5000}
                            />
                </Box>
    
      </Stack>
)}





export const ProductView = ({ item, onClose, mode, onBuy }) => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;
  const [isLoading, setIsLoading] = useState(false);
  const [Loading, setLoading] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const buttonVariants = {
    initial: { opacity: 0.7, scale: 0.9 },
    hover: { opacity: 1, scale: 1.1 },
    tap: { scale: 0.95 },
  };
  
  const MotionButton = motion.create(Button);

  const handleBuy = async () => {
    setLoading(true)
    try {
      if (onBuy) {
        await onBuy(item, quantity);
        await updateData('items_update', item.item_id, {type_:1, quantity: quantity});
      }
    } 
    catch (error) {
      toaster.create({title: "Error Adding to checklist",description: error,type: "warning",duration: 900,});
    } finally {
      await onClose();
      await setLoading(false);
    }
  };
  


  // useEffect(() => {
  //   if (!item) return;
  //   setIsLoading(true)
  //     if (item.item_pic_url?.length > 0) {
  //       Promise.allSettled(
  //         item.item_pic_url.map((url) => fetchImage(url).catch(() => null)) 
  //       ).then((results) => {
  //         const successfulUrls = results
  //           .filter(({ status, value }) => status == "fulfilled" && value !== null)
  //           .map(({ value }) => value);
    
  //         setImageUrls(successfulUrls);
    
  //         // const failedCount = results.length - successfulUrls.length;
  //         // if (failedCount > 0) {
  //         //   toaster.create({
  //         //     title: "No Image Load",
  //         //   });
  //         // }
    
  //         setIsLoading(false);
  //       });
  //       }else{

  //         setIsLoading(false)
  //       }


  // }, [item]);

    const handleNextImage = () => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % item?.item_pic_url?.length);
    };
  
    const handlePreviousImage = () => {
      setCurrentImageIndex((prevIndex) => (prevIndex - 1 + item?.item_pic_url?.length) % item?.item_pic_url?.length);
    };
  

  return (
    <VStack gap={4} p={{ base: 4, md: 2 }} align="stretch">
      {isLoading ? (
        <Center h="200px">
          <VStack gap={3}>
            <Spinner size="lg" color="blue.500" />
            <Text color="gray.600" fontSize="sm">
              Loading...
            </Text>
          </VStack>
        </Center>
      ) : (
        <>
          {/* Compact Header */}
          {/* <Box
            p={1}
            bg="linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(16, 185, 129, 0.08))"
            borderRadius="xl"
            border="1px solid"
            borderColor="gray.200"
            _dark={{ borderColor: "gray.700" }}
          >
            <HStack justify="space-between" align="center">
              <Heading
                size={{ base: "md", md: "lg" }}
                color="gray.800"
                _dark={{ color: "white" }}
              >
                Product Details
              </Heading>
              <Icon boxSize={5} color="blue.600">
                <FiImage />
              </Icon>
            </HStack>
          </Box> */}

          {/* Responsive Main Content */}
          <Stack
            direction={{ base: "column", lg: "row" }}
            gap={{ base: 4, md: 6 }}
            align="stretch"
          >
            {/* Compact Image Gallery */}
            <Box
              flex={{ base: "none", lg: "1" }}
              w={{ base: "100%", lg: "auto" }}
              maxW={{ base: "100%", lg: "400px" }}
           
              borderRadius="xl"
              boxShadow="md"
              borderColor="gray.200"
              overflow="hidden"
            >
              {item.item_pic_url?.length > 0 ? (
                <Box position="relative" p={3}>
                  <Box
                    position="relative"
                    width="100%"
                    height={{ base: "250px", md: "300px" }}
                    overflow="hidden"
                    borderRadius="lg"
                  >
                    <Image
                      src={item.item_pic_url[currentImageIndex]}
                      alt={item.item_name}
                      objectFit="cover"
                      width="100%"
                      height="100%"
                      loading="eager"
                      borderRadius="lg"
                    />

                    {/* Compact Navigation */}
                    {item.item_pic_url?.length > 1 && (
                      <>
                        <MotionButton
                          position="absolute"
                          left="2"
                          top="50%"
                          transform="translateY(-50%)"
                          onClick={handlePreviousImage}
                          variants={buttonVariants}
                          initial="initial"
                          whileHover="hover"
                          whileTap="tap"
                          bg="rgba(255, 255, 255, 0.9)"
                          color="gray.700"
                          borderRadius="full"
                          p={2}
                          size="sm"
                          boxShadow="md"
                        >
                          &lt;
                        </MotionButton>
                        <MotionButton
                          position="absolute"
                          right="2"
                          top="50%"
                          transform="translateY(-50%)"
                          onClick={handleNextImage}
                          variants={buttonVariants}
                          initial="initial"
                          whileHover="hover"
                          whileTap="tap"
                          bg="rgba(255, 255, 255, 0.9)"
                          color="gray.700"
                          borderRadius="full"
                          p={2}
                          size="sm"
                          boxShadow="md"
                        >
                          &gt;
                        </MotionButton>
                      </>
                    )}

                    {/* Compact Counter */}
                    <Box
                      position="absolute"
                      bottom="2"
                      right="2"
                      bg="rgba(0, 0, 0, 0.7)"
                      color="white"
                      px={2}
                      py={1}
                      borderRadius="md"
                      fontSize="xs"
                      fontWeight="bold"
                    >
                      {currentImageIndex + 1}/{item.item_pic_url?.length}
                    </Box>
                  </Box>
                </Box>
              ) : (
                <Center
                  height={{ base: "250px", md: "300px" }}
                 
                  m={3}
                  borderRadius="lg"
                  border="2px dashed"
                  borderColor="gray.300"
                >
                  <VStack gap={2}>
                    <Icon boxSize={8} color="gray.400">
                      <FiImage />
                    </Icon>
                    <Text color="gray.500" fontSize="sm">
                      No image
                    </Text>
                  </VStack>
                </Center>
              )}
            </Box>
            {/* Compact Product Details */}
            <Box
              flex={{ base: "none", lg: "1" }}
              w="100%"
              borderRadius="xl"
              boxShadow="md"
              borderColor="gray.200"
              overflow="hidden"
            >
              {/* Product Header */}
              <Box p={4} borderBottom="1px solid" borderColor="gray.200" _dark={{ borderColor: "gray.700" }}>
                <VStack align="start" gap={2}>
                  <Heading
                    size={{ base: "md", md: "lg" }}
                    noOfLines={2}
                  >
                    {item.item_name}
                  </Heading>
                  <HStack gap={2} flexWrap="wrap">
                    <Box
                      px={2}
                      py={1}
                      bg="blue.100"
                      _dark={{ bg: "blue.900" }}
                      borderRadius="md"
                      fontSize="xs"
                      fontWeight="bold"
                    >
                      {item?.Category?.category_name || 'Uncategorized'}
                    </Box>
                    {item.item_sub_cat && (
                      <Box
                        px={2}
                        py={1}
                        bg="purple.100"
                        _dark={{ bg: "purple.900" }}
                        borderRadius="md"
                        fontSize="xs"
                        fontWeight="bold"
                      >
                        {item.item_sub_cat}
                      </Box>
                    )}
                  </HStack>
                </VStack>
              </Box>

              <VStack align="stretch" gap={3} p={4}>
                {/* Compact Info Grid */}
                <SimpleGrid columns={{ base: 1, md: 2 }} gap={3} mb={4}>
                  {/* Model & Stock */}
                  <VStack gap={2} align="stretch">
                    <Box p={3}  borderRadius="lg">
                      <Text fontSize="xs"  mb={1}>Model</Text>
                      <Text fontSize="sm" fontWeight="semibold" truncate>
                        {item.item_model_no || 'N/A'}
                      </Text>
                    </Box>
                    <Box
                      p={3}
                      bg={item.item_quantity > 10 ? "green.50" : item.item_quantity > 0 ? "orange.50" : "red.50"}
                      _dark={{
                        bg: item.item_quantity > 10 ? "green.900" : item.item_quantity > 0 ? "orange.900" : "red.900"
                      }}
                      borderRadius="lg"
                    >
                      <Text fontSize="xs" mb={1}>Stock</Text>
                      <HStack gap={2}>
                        <Text
                          fontSize="sm"
                          fontWeight="bold"
                          color={item.item_quantity > 10 ? "green.700" : item.item_quantity > 0 ? "orange.700" : "red.700"}
                          _dark={{
                            color: item.item_quantity > 10 ? "green.300" : item.item_quantity > 0 ? "orange.300" : "red.300"
                          }}
                        >
                          {item.item_quantity}
                        </Text>
                        <Text fontSize="xs" >
                          {item.item_quantity > 10 ? "In Stock" : item.item_quantity > 0 ? "Low" : "Out"}
                        </Text>
                      </HStack>
                    </Box>
                  </VStack>

                  {/* Pricing */}
                  <VStack gap={2} align="stretch">
                    <HStack justify="space-between" p={3} bg="green.50" _dark={{ bg: "green.900" }} borderRadius="lg">
                      <Text fontSize="xs" >Selling</Text>
                      <Text fontSize="sm" fontWeight="bold" color="green.600" _dark={{ color: "green.400" }}>
                        {item.item_selling}
                      </Text>
                    </HStack>
                    <HStack justify="space-between" p={3} bg="blue.50" _dark={{ bg: "blue.900" }} borderRadius="lg">
                      <Text fontSize="xs" >Buying</Text>
                      <Text fontSize="sm" fontWeight="bold" color="blue.600" _dark={{ color: "blue.400" }}>
                        {item.item_buying}
                      </Text>
                    </HStack>
                  </VStack>
                </SimpleGrid>

                {/* Compact Additional Info */}
                {item?.Discount?.discount_amount && (
                  <HStack justify="space-between" p={2} bg="orange.50" _dark={{ bg: "orange.900" }} borderRadius="md">
                    <Text fontSize="xs" color="orange.700" _dark={{ color: "orange.300" }}>
                      Discount:
                    </Text>
                    <Text fontSize="xs" fontWeight="bold" color="orange.700" _dark={{ color: "orange.300" }}>
                      {item.Discount.discount_amount}
                    </Text>
                  </HStack>
                )}

                {item?.item_description && (
                  <Box p={3} borderRadius="lg">
                    <Text fontSize="xs" color="gray.500" mb={1}>Description</Text>
                    <Text fontSize="sm" color="gray.700" _dark={{ color: "gray.300" }} lineHeight="1.4">
                      {item.item_description.length > 100
                        ? `{item.item_description.substring(0, 100)}...`
                        : item.item_description}
                    </Text>
                  </Box>
                )}

                {/* Compact Quantity Input for POS */}
                {mode === 'pos' && (
                  <Box p={3} borderRadius="lg">
                    <Text fontSize="xs" mb={2}>
                      Quantity:
                    </Text>
                    <Input
                      type="number"
                      value={quantity}
                      onChange={(e) => setQuantity(Number(e.target.value))}
                      min={1}
                      max={item.item_quantity}
                      placeholder="Enter quantity"
                      size="sm"
                      borderRadius="md"
                    />
                    {quantity > item.item_quantity && (
                      <Text fontSize="xs" color="red.500" mt={1}>
                        Exceeds stock ({item.item_quantity})
                      </Text>
                    )}
                  </Box>
                )}
              </VStack>
              
            </Box>
          </Stack>
          {/* Compact Action Buttons */}
          {mode === 'pos' && (
            <HStack
              gap={3}
              p={4}
              borderRadius="xl"
              justify="space-between"
            >
              <Button
                size={{ base: "sm", md: "md" }}
                variant="outline"
                onClick={onClose}
                disabled={Loading}
                flex="1"
                maxW="120px"
              >
                Cancel
              </Button>
              <Button
                // size={{ base: "sm", md: "md" }}
                colorScheme={quantity > item.item_quantity ? "red" : "blue"}
                onClick={handleBuy}
                disabled={quantity > item.item_quantity || quantity < 1 || Loading}
                flex="2"
              >
                {Loading ? (
                  <HStack gap={2}>
                    <Spinner size="xs" />
                    <Text fontSize={{ base: "xs", md: "sm" }}>Wait...</Text>
                  </HStack>
                ) : quantity > item.item_quantity ? (
                  "Out of Stock"
                ) : (
                  <Text fontSize={{ base: "xs", md: "sm" }}>
                    Sell {(item.item_selling * quantity).toFixed(2)}
                  </Text>
                )}
              </Button>
            </HStack>
          )}
        </>
      )}
    </VStack>
  );
};
