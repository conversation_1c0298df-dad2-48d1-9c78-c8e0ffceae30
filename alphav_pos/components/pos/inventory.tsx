'use client'
import  React,{ useState,useEffect,useCallback} from "react";
import {IconButton,List,Image,Textarea,Center,Spinner ,createListCollection,Icon ,Text, Grid,Box,Tabs,GridItem,Card,FormatNumber,Heading,HStack, Button, Input,Flex, Stack,VStack,SimpleGrid} from '@chakra-ui/react';
import Table from '@/components/pos/Table_default2'
import Search from '@/components/pos/search'
import { useColorModeValue } from '@/components/ui/color-mode';
import { StatHelpText, StatLabel, StatRoot, StatUpTrend, StatValueText, } from "@/components/ui/stat"
import {DialogActionTrigger,DialogBody,DialogCloseTrigger,DialogContent,DialogFooter,DialogHeader,DialogRoot,DialogTitle,DialogTrigger,} from "@/components/ui/dialog"
import { Shop,Product,Transaction,Order,Receipt,Category,Discount,ApiResponse } from '@/app/utils/definitions'
import { FiDownload,FiUpload,FiFilter,FiPlus,FiX,FiFolderPlus,FiBriefcase,FiArrowDown} from "react-icons/fi";
import { Field } from "@/components/ui/field"
import {NativeSelectField,NativeSelectRoot,} from "@/components/ui/native-select"
import {FileUploadDropzone,FileUploadList,FileUploadRoot,FileUploadTrigger} from "@/components/ui/file-upload"
import { z } from "zod";
import { toaster } from "@/components/ui/toaster"
import { Tag } from "@/components/ui/tag"
import { FormEvent } from 'react';
import {fetchData} from '@/app/utils/pos';
import { ItemForm,Add_items,Add_category,Add_discounts,ProductView,Add_payment} from "@/components/pos/invetory_functions";
import {useAppSelector } from "@/lib/hooks";
import Filter from '@/components/pos/filters'

const Inventory = () => {

  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;

  const [products, setProducts] = useState<Product[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [query, setQuery] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [onAdd, setAdd] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [AddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const [totalProducts, settotalProducts] = useState(0);
  const [topSellingProducts, settopSellingProducts] = useState([]);
  const [lowStockItems, setlowStockItems] = useState([]);
  // const [isEditModalOpen, setIsEditModalOpen] = useState(0);


  const loadData = useCallback(async () => {
    setIsLoading(true);
    const queryParams = { query, shop_id };
    try {
      const productResponse = await fetchData<Product>('items', currentPage, pageSize, queryParams);
      const { data: productData, metadata: productMetadata } = productResponse;
      setProducts(productData);
      setTotalItems(productMetadata.total);
    } catch (error) {
      toaster.error({
        title: 'Failed to load data.',
        description: error instanceof z.ZodError ? error.errors[0]?.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, pageSize, query, onAdd]);

  useEffect(() => {
    loadData();
    handleFetch();
  }, [loadData]);

  const handlePageChange = (newPage: any) => {
    setCurrentPage(newPage.page);
  };

  const handlePageSizeChange = (newPageSize: any) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  const handleSearch = (searchValue: string) => {
    setQuery(searchValue);
    setCurrentPage(1);
  };

  const handleItemAdded = () => {
    setAdd(prev => !prev);
  };

  const stats = [
    {
      title: "Total Products",
      color: "orange.500",
      values: [
        { value: totalProducts, helpText: "current sales" },
      ],
    },
    {
      title: "Top Selling",
      color: "purple.500",
      values: [
        {
          value:topSellingProducts[0]?.item_name,
          helpText: topSellingProducts?.length > 0  ? topSellingProducts[0]?.receipt_total: 0
        },
      ],
      isCurrency: false,
      isText: true

    },
    {
      title: "Low Stock",
      color: "red.500",
      values: [
        { 
          value: lowStockItems.length > 0 ?lowStockItems[0].item_name:0, 
          helpText: lowStockItems.length > 0 ?lowStockItems[0].item_quantity:0
        },
      ],
      isCurrency: false,
      isText: true
    },
  ];

  const handleFetch = async () => {
    if (!shop_id) return;
    try {
        const response = await fetchData('cal_invet', null, null, { shop_id });
        if (response) { 
          settotalProducts(response?.totalProducts); 
          settopSellingProducts(response?.topSellingProducts);
          setlowStockItems(response?.lowStockItems)}
    } catch (error) {
        toaster.error({title: "Error on server",description: 'An unexpected error occurred.', });
    }
  };


  const columns = [
    { label: 'Id', key: 'item_id', align: 'left' },
    { label: 'Name', key: 'item_name', align: 'left' },
    { label: 'Selling price', key: 'item_selling', align: 'left' },
    { label: 'Buying price', key: 'item_buying', align: 'left' },
    { label: 'Quantity', key: 'item_quantity', align: 'left' },
    { label: 'Discount', key: 'Discount.discount_amount', align: 'left' },
    { label: 'Model', key: 'item_model_no', align: 'left' },
    { label: 'Availability', key: 'status.status_name', align: 'left' },
    { label: 'Action', key: 'actions', align: 'center' }
  ];

  const toggleModal = (item: Product) => {
    if (!isEditModalOpen) {
      setSelectedProduct(item);
    } else { 
      setSelectedProduct(null);}
    setIsEditModalOpen((prev) => !prev);
  };
  const handleView = (item: Product) => {
    setSelectedProduct(item);
    setIsViewModalOpen(true);
  };
  const getActions = (item: Product) => [
    { label: 'Update', colorScheme: 'blue', onClick: () =>toggleModal(item)},
    { label: 'View', colorScheme: 'green', onClick: () =>handleView(item)},
  ];

      return (
 
    <Stack direction="column" gap={6} p={{ base: 3, md: 6 }}>
        {/* Stats and Search/Actions Section */}
        <Stack direction={{ base: "column", lg: "row" }} gap={6} w="100%">
          {/* Stats Section - Now full width on mobile */}
          <Box flex={{ base: "1", lg: "2" }} minW="0">
            <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} gap={4}>
              {stats.map((stat, index) => (
                <Card.Root 
                  key={index} 
                  p={4} 
                  borderRadius="lg" 
                  boxShadow="sm"
                  transition="all 0.2s"
                  _hover={{ boxShadow: "md", transform: "translateY(-2px)" }}
                >
                  <Stack gap={3}>
                    <Heading color={stat.color} size="sm" fontWeight="semibold">
                      {stat.title}
                    </Heading>
                    <StatRoot>
                      <HStack gap={6} flexWrap="wrap">
                        {stat.values.map((item, idx) => (
                          <VStack key={idx} align="start" gap={2}>
                             <StatValueText textStyle="md" whiteSpace="nowrap" display="inline">{item.value}</StatValueText>
                            <StatHelpText fontSize="md" whiteSpace="nowrap" display="inline">{item.helpText}</StatHelpText>
                         
                          </VStack>

                        ))}
                      </HStack>
                    </StatRoot>
                  </Stack>
                </Card.Root>
              ))}
            </SimpleGrid>
          </Box>

          {/* Search and Actions Section */}
          <Box flex={{ base: "1", lg: "1" }} minW="0">
            <VStack gap={4} w="100%">
              {/* Search Box - Full width on mobile */}
              <Box w="100%">
                <Search
                  placeholder="Search for items"
                  tableName="inventory"
                  onSearch={handleSearch}
                />
              </Box>

              {/* Action Buttons - Responsive layout */}
              <Box w="100%">
                <Stack direction={{ base: "column", sm: "row" }} gap={4}>
                  <DialogRoot 
                    open={AddModalOpen} 
                    onOpenChange={(e) => setIsAddModalOpen(e.open)}
                    closeOnEscape={true}
                    placement='top'
                    size="xl"
                    motionPreset="slide-in-top"
                    lazyMount={true}
                    preventScroll
                  >
                    <DialogTrigger asChild>
                      <Button 
                        colorScheme="blue" 
                        flexShrink={0}
                        w={{ base: "100%", sm: "auto" }}
                      ><FiPlus />
                        Add Items
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogBody>
                        <Tabs.Root defaultValue="members">
                          <Tabs.List>
                            <Tabs.Trigger value="members"><FiPlus /> Add Item</Tabs.Trigger>
                            <Tabs.Trigger value="projects"><FiFolderPlus /> Add Items(Bulk)</Tabs.Trigger>
                            <Tabs.Trigger value="payment"><FiBriefcase /> Add Payment Methods</Tabs.Trigger>
                            <Tabs.Trigger value="category"><FiBriefcase /> Add Categories</Tabs.Trigger>
                            <Tabs.Trigger value="discounts"><FiArrowDown /> Add Discounts</Tabs.Trigger>
                          </Tabs.List>
                          <Tabs.Content value="members">
                            <ItemForm mode="insert" item={null} onSubmit={handleItemAdded} onClose={() => setIsAddModalOpen(false)} />
                          </Tabs.Content>
                          <Tabs.Content value="projects">
                            <Add_items />
                          </Tabs.Content>
                          <Tabs.Content value="category">
                            <Add_category mode={'insert'} id={null} />
                          </Tabs.Content>
                          <Tabs.Content value="payment">
                            <Add_payment/>
                          </Tabs.Content>
                          <Tabs.Content value="discounts">
                            <Add_discounts mode={'insert'} id={null} />
                          </Tabs.Content>
                        </Tabs.Root>
                      </DialogBody>
                      <DialogCloseTrigger />
                    </DialogContent>
                  </DialogRoot>

                  <Filter setQuery={setQuery} shop_id={shop_id} />
                  
                  <Button 
                    colorScheme="blue" 
                    flexShrink={0}
                    w={{ base: "100%", sm: "auto" }}
                  ><FiDownload />
                    Download
                  </Button>
                </Stack>
              </Box>
            </VStack>
          </Box>
        </Stack>

        {/* Table Section */}
        <Card.Root 
          boxShadow="xl" 
          w="100%" 
          p={{ base: 2, md: 4 }}
          transition="all 0.2s"
          _hover={{ boxShadow: "2xl" }}
        >
          <Table
            columns={columns}
            data={products}
            currentPage={currentPage}
            totalItems={totalItems}
            pageSize={pageSize}
            handlePageChange={handlePageChange}
            handlePageSizeChange={handlePageSizeChange}
            getActions={getActions}
            isLoading={isLoading}
            width="100%"
            height="100%"
          />
        </Card.Root>

        {/* Modals */}
        {isViewModalOpen && (
          <DialogRoot 
            open={isViewModalOpen} 
            onOpenChange={(e) => setIsViewModalOpen(e.open)}
            closeOnEscape={true}
            placement='top'
            size="xl"
            motionPreset="slide-in-top"
            lazyMount={true}
            preventScroll
          >    
            <DialogContent>
              <DialogBody>
                <ProductView item={selectedProduct} onClose={() => setIsViewModalOpen(false)} />
              </DialogBody>
              <DialogCloseTrigger />
            </DialogContent>
          </DialogRoot>
        )}
        
        {isEditModalOpen && selectedProduct && (
          <DialogRoot 
            open={isEditModalOpen} 
            onOpenChange={(e) => setIsEditModalOpen(e.open)}
            closeOnEscape={true}
            placement='top'
            size="xl"
            motionPreset="slide-in-top"
            lazyMount={true}
            preventScroll
          >    
            <DialogContent>
              <DialogBody>
                <ItemForm
                  mode="update"
                  item={selectedProduct}
                  onSubmit={handleItemAdded}
                  onClose={() => setIsEditModalOpen(false)}
                />
              </DialogBody>
              <DialogCloseTrigger />
            </DialogContent>
          </DialogRoot>
        )}
      </Stack>
    );
};

export default Inventory;



