import  React,{ useState,useEffect,useCallback} from "react";

import {insertBulk,fetchData,insertData,updateData,deleteData,fetchImage } from '@/app/utils/pos';
import {Tag,IconButton,Fieldset,CheckboxGroup,useCheckboxGroup,Select,List,Separator,Textarea,Center,Spinner ,createListCollection,Icon ,Text, Grid,Box,Tabs,GridItem,Card,FormatNumber,Heading,HStack, Button, Input,Flex, Stack,VStack, ToastCloseTrigger} from '@chakra-ui/react';
import { Shop,Role,User,Transaction,Order,Receipt,Payment_Method,Payment_Method_Trans,Category,Discount,ApiResponse,Status } from '@/app/utils/definitions'
import Image from 'next/image'
// import { Tag } from "@/components/ui/tag"
import { z } from "zod";
import { motion } from "motion/react"
import { useColorModeValue } from '@/components/ui/color-mode';
import { FiDownload,FiUpload,FiFilter,FiPlus,FiX,FiFolderPlus,FiBriefcase,FiArrowDown,FiImage,FiChevronDown,FiCodesandbox,FiKey } from "react-icons/fi";
import { toaster } from "@/components/ui/toaster"
import { Field } from "@/components/ui/field"
import {NativeSelectField,NativeSelectRoot,} from "@/components/ui/native-select"
import {FileUploadDropzone,FileUploadList,FileUploadRoot,FileUploadTrigger} from "@/components/ui/file-upload"
import { Checkbox } from "@/components/ui/checkbox"
import { ChevronDownIcon } from "@chakra-ui/icons";
import {
  MenuCheckboxItem,
  MenuContent,
  MenuItemGroup,
  MenuItem,
  MenuRoot,
  MenuTrigger,
  MenuItemCommand
} from "@/components/ui/menu"
import {
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectRoot,
  SelectTrigger,
  SelectValueText,
} from "@/components/ui/select"

const userSchema = z.object({
  // username: z.string().min(1, 'Username is required'),
  email: z.string().email('Invalid email address'),
  status: z.number(),
  role_ids: z.array(z.string()).nonempty('At least one role is required'),
  shop_ids: z.array(z.string()).nonempty('At least one shop is required'),
  first_names: z.string().optional(),
  last_names: z.string().optional(),
  identification: z.string().optional(),
});




export const UserForm = ({ mode, user,onClose }) => {
  // const [formData, setFormData] = useState<User[]>([]);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    role_ids: [],
    shop_ids: [],
    first_names: '',
    last_names: '',
    identification: '',
    status: 16
  });
  
  const [roles, setRoles] = useState([]);
  const [shops, setShops] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [rolesOptions, setrolesOptions] = useState([]);
  const [shopsOptions, setshopsOptions] = useState([]);

  useEffect(() => {
    if (mode == 'update' && user) {
      setFormData({
        username: user.username,
        email: user.email || '',
        role_ids: user.roles ? user.roles.map((role) => role.role_id) : [],
        shop_ids: user.shops ? user.shops.map((shop) => shop.shop_id) : [],
        first_names: user.first_names || '',
        last_names: user.last_names || '',
        identification: user.identification || '',
        status: user.status || 17
      });      
    }
    fetchRolesAndShops();
  }, [mode, user]);

  const fetchRolesAndShops = async () => {
    setIsLoading(true);
    try {
      const rolesResponse = await fetchData('admin/roles', null, null, null);
      const shopsResponse = await fetchData('shop', null, null, null);
      setRoles(rolesResponse.data);
      setShops(shopsResponse.data);
      setrolesOptions(rolesResponse?.data.map((role) => ({
        value: role.role_id,
        label: role.role_name,
      })));
  
      setshopsOptions(shopsResponse?.data.map((shop) => ({
        value: shop.shop_id,
        label: shop.shop_name,
      })));
    } catch (error) {
      toaster.create({
        title: 'Failed to fetch data',
        description: error.message,
        type: 'error'
      });
    } finally {
      setIsLoading(false);
    
    }
  };


  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name, value) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const validatedData = userSchema.parse(formData);
      if (mode == 'insert') {
        await insertData('auth_create', validatedData);
        toaster.create({ title: 'User created successfully.',type: 'success'});
      } else if (mode == 'update') {
        await updateData('auth_update', user.user_id, validatedData);
        toaster.create({title: 'User updated successfully.', type: 'success'});
      }
      // onSubmit();
      if(mode == 'update')onClose();
    } catch (error) {
     
      toaster.create({title: 'Error',
        description:error instanceof z.ZodError? error.errors[0].message: error.message,type: 'error'});
    }finally{
      setIsLoading(false);
      setFormData({
        username: '',
        email: '',
        role_ids: [],
        shop_ids: [],
        first_names: '',
        last_names: '',
        identification: '',
        status: 16
      });
    }
  };

  

  if (isLoading) {
    return (
      <Center h="200px">
        <Spinner size="xl" color="red.600"/>
      </Center>
    );
  }

  return (
    <Stack px={2} py={4}>
      {isLoading ? (
        <Center h="200px">
          <Spinner size="xl" color="red.600"/>
        </Center>
      ) : (
        <>
          <Heading>{mode=='insert'?'Add':'Update'}  User</Heading>
          <HStack gap={6} align="flex-start">
              {mode=='update' && <Field label="Username" required flex="1">
                  <Input
                    placeholder="Username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={mode === 'update'}
                    _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
                  />
                </Field>}
            <Field label="Email" required flex="1">
              <Input
                placeholder="Email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Identification" flex="1">
              <Input
                placeholder="Identification"
                name="identification"
                value={formData.identification}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>
          </HStack>

          <HStack gap={6} align="flex-start">
            <Field label="First Names" flex="1">
              <Input
                placeholder="First Names"
                name="first_names"
                value={formData.first_names}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

            <Field label="Last Names" flex="1">
              <Input
                placeholder="Last Names"
                name="last_names"
                value={formData.last_names}
                onChange={handleChange}
                _focus={{ borderColor: 'blue.500', boxShadow: '0 0 0 1px blue.500' }}
              />
            </Field>

        
          </HStack>

                      <HStack gap={6} align="flex-start">
                      <Field label="Roles" flex="1">
                      {rolesOptions && <CustomMultiSelect 
                              options={rolesOptions}  
                              selectedValues={formData.role_ids || []} 
                              onChange={(selectedValues) => handleSelectChange("role_ids", selectedValues)}
                              placeholder={"roles"}  
                          />}
                      </Field>


                    <Field label="Shops" flex="1">
                    {shopsOptions && <CustomMultiSelect 
                              options={shopsOptions}  
                              selectedValues={formData.shop_ids || []} 
                              onChange={(selectedValues) => handleSelectChange("shop_ids", selectedValues)}
                              
                              placeholder={"shops"}  
                            />}
                  </Field>
          </HStack>
          <HStack justify="flex-end" mt={2}>
            <Button w="10rem" colorScheme="orange" variant="outline" _hover={{ bg: 'red.100', transform: 'translateY(-2px)' }} transition="all 0.2s" onClick={onClose}>
              Discard
            </Button>
            <Button w="13rem" disabled={isLoading} colorScheme="blue" onClick={handleSubmit} _hover={{ transform: 'translateY(-2px)' }} transition="all 0.2s">
      
              {isLoading ? <Spinner size="sm" color="red.600"/> : mode == 'insert' ? 'Add' : 'Update'}
            </Button>
          </HStack>
        </>
      )}
    </Stack>
  );
}; 

const CustomMultiSelect = ({ options, selectedValues, onChange, placeholder }) => {
  const frameworks = createListCollection({ items: options });

  const handleValueChange = (details) => {
    // details.value is an array of selected values for multiple select
    const newValues = details.value || [];
    onChange(newValues);
  };

  return (
    <SelectRoot
      size="md"
      multiple
      collection={frameworks}
      onValueChange={handleValueChange}
      value={selectedValues || []}
    >
      <SelectTrigger clearable>
        <SelectValueText placeholder={`Select ${placeholder}`} />
      </SelectTrigger>
      <SelectContent zIndex="popover">
        {frameworks.items.map((option: any) => (
          <SelectItem item={option} key={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </SelectRoot>
  );
};





const shopSchema = z.object({
  shop_name: z.string().min(1, "Shop name is required"),
  shop_owner_names: z.string().min(1, "Owner names are required"),
  shop_website: z.string().optional(),
  shop_email: z.array(z.string()).min(1, "At least one email is required"),
  shop_phone: z.array(z.string()).min(1, "At least one phone number is required"),
  shop_location_name: z.string().min(1, "Location name is required"),
  shop_location_address: z.string().min(1, "Location address is required"),
  shop_status: z.number(),
});

export const ShopModal = ({ onClose, mode, shop }) => {
  const [shopData, setShopData] = useState({
    shop_name: "",
    shop_owner_names: "",
    shop_website: "",
    shop_email: [],
    shop_phone: [],
    shop_location_name: "",
    shop_location_address: "",
    shop_status: 16,
    shop_logo_url: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [acceptedFiles, setAcceptedFiles] = useState([]);


  useEffect(() => {
    if (mode == "update" && shop) {
      setShopData({
        shop_name: shop.shop_name || "",
        shop_owner_names: shop.shop_owner_names || "",
        shop_website:"",
        shop_email: shop.shop_email || [],
        shop_phone: shop.shop_phone || [],
        shop_location_name: shop.shop_location_name || "",
        shop_location_address: shop.shop_location_address || "",
        shop_status: shop.shop_status || 16,
        shop_logo_url: shop.shop_logo_url || [],  
      });
    } else {
      setShopData({
        shop_name: "",
        shop_owner_names: "",
        shop_website:"",
        shop_email: [],
        shop_phone: [],
        shop_location_name: "",
        shop_location_address: "",
        shop_status: 16,
        shop_logo_url: [],
      });
    }
  }, [mode, shop]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setShopData((prev) => ({ ...prev, [name]: value }));
  };

  const handleArrayChange = (name, value) => {
    setShopData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddTag = (name, inputValue) => {
    if (inputValue.trim() !== "") {
      handleArrayChange(name, [...shopData[name], inputValue.trim()]);
    }
  };

  const handleRemoveTag = (name, index) => {
    const newTags = shopData[name].filter((_, i) => i !== index);
    handleArrayChange(name, newTags);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const uploadFormData = new FormData();
      acceptedFiles.forEach((file) => uploadFormData.append("item_pic_url", file));
      let apiResponse;
      let uploadedFiles = [];
      if (acceptedFiles.length > 0) {
            const response = await insertData("upload_pic", uploadFormData);
            if (!response.files || !Array.isArray(response.files)) {throw new Error("Invalid response format");}
            uploadedFiles = response?.files;
          }
      const validatedData_ = shopSchema.parse(shopData);
      if(uploadedFiles.length > 0){
        var validatedData ={
          ...validatedData_,
          shop_logo_url: uploadedFiles,
        }
      }
      if (mode == "insert") {
        apiResponse = await insertData("shop", validatedData);
      } else if (mode == "update" && shop) {
        apiResponse = await updateData("shop", shop.shop_id, validatedData);
      }
      if (apiResponse?.success) {
        if(mode == 'update')onClose();
      }
    } catch (error) {
      toaster.create({
        title: 'Error',
        description: error instanceof z.ZodError ? error.errors[0].message : error.message,
        type: 'error',
      });
    } finally {
      setIsLoading(false);
      setShopData({
        shop_name: "",
        shop_owner_names: "",
        shop_website: "",
        shop_email: [],
        shop_phone: [],
        shop_location_name: "",
        shop_location_address: "",
        shop_status: 16,
        shop_logo_url:[],
      });
      setAcceptedFiles([]);
    }
  };
  const handleFileUpload = async (files) => {

    let file = files.files
    if (!file.length) return;
    const uniqueFiles = new Set();
    const filteredFiles = file.filter(file => {
      const isUnique = !uniqueFiles.has(file.name);
      uniqueFiles.add(file.name);
      return isUnique;
    });
    const existingFiles = new Set(acceptedFiles.map(file => file.name));
    const newFiles = filteredFiles.filter(file => !existingFiles.has(file.name));
    if (newFiles.length == 0) {
      toaster.create({
        title: "Duplicate File",
        description: "Files with the same name already exist. Please upload different files.",
        type: "warning",
      });
      return;
    }
    setAcceptedFiles(prevFiles => [...prevFiles, ...newFiles]);

    setShopData(prev => ({
      ...prev,
      shop_logo_url: [...(prev.shop_logo_url || []), ...newFiles],
    }));

};



  const handleFileRemove = (fileToRemove) => {
    try {
      setAcceptedFiles(prevFiles => prevFiles.filter(file => file != fileToRemove));
      // setSupplyFormData(prev =>({...prev, attachments: prev.attachments.filter(file => file != fileToRemove.name),}));
      setShopData(prev =>({...prev, shop_logo_url: prev.shop_logo_url.filter(file => file != fileToRemove.name),}));
    } catch (error) {
      toaster.create({
        title: "File Rejected",
        description: 'Failed to remove file: ' + error.message,
        type: "error",
      });
    }
  };


    const handleFileReject = ( details ) => {
  
      if (!details.files || details.files.length == 0) return;
      const rejectedFiles = details.files.map((fileObj) => {
        const fileName = fileObj.file.name;
        const errorMessages = fileObj.errors?.map(err => err).join(", ") || "Unknown error";
        return `File: ${fileName}, Reason: ${errorMessages}`;
      });
      toaster.create({
        title: "File Rejected - Upload only Image",
        description: rejectedFiles.join("; "),
        type: "error",
      });
    };

  return (
    <Stack gap={4}>
      {isLoading ? (
        <Center h="200px">
          <Spinner size="xl" color="red.600"/>
        </Center>
      ) : (
        <>
          <Heading>{mode=='insert'?'Add':'Update'} Shop</Heading>
          <HStack gap={6} align="flex-start">
            <Field label="Shop Name">
              <Input
                name="shop_name"
                value={shopData.shop_name}
                onChange={handleChange}
              />
            </Field>
            <Field label="Owner Names">
              <Input
                name="shop_owner_names"
                value={shopData.shop_owner_names}
                onChange={handleChange}
              />
            </Field>
            <Field label="Website">
              <Input
                name="shop_website"
                value={shopData.shop_website}
                onChange={handleChange}
              />
            </Field>
          </HStack>
          <HStack gap={6} align="flex-start">
            <Field label="Email">
              <Box>
                <HStack gap={2} mb={2} flexWrap="wrap">
                  {shopData.shop_email.map((email, index) => (
                    <Tag.Root key={index}>
                      <Tag.Label>{email}</Tag.Label>
                      <Tag.EndElement>
                        <Tag.CloseTrigger onClick={() => handleRemoveTag('shop_email', index)} />
                      </Tag.EndElement>
                    </Tag.Root>
                  ))}
                </HStack>
                <Input
                  placeholder="Add email and press Enter"
                  onKeyDown={(e) => {
                    if (e.key == 'Enter') {
                      handleAddTag('shop_email', e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
              </Box>
            </Field>
            <Field label="Phone">
              <Box>
                <HStack gap={2} mb={2} flexWrap="wrap">
                  {shopData.shop_phone.map((phone, index) => (
                    <Tag.Root key={index}>
                      <Tag.Label>{phone}</Tag.Label>
                      <Tag.EndElement>
                        <Tag.CloseTrigger onClick={() => handleRemoveTag('shop_phone', index)} />
                      </Tag.EndElement>
                    </Tag.Root>
                  ))}
                </HStack>
                <Input
                  placeholder="Add phone number and press Enter"
                  onKeyDown={(e) => {
                    if (e.key == 'Enter') {
                      handleAddTag('shop_phone', e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
              </Box>
            </Field>
            <Field label="Location Name">
              <Input
                name="shop_location_name"
                 placeholder="Add location name"
                value={shopData.shop_location_name}
                onChange={handleChange}
              />
            </Field>
            <Field label="Location Address">
              <Input
                name="shop_location_address"
                 placeholder="Add location address"
                value={shopData.shop_location_address}
                onChange={handleChange}
              />
            </Field>
          </HStack>
          <HStack gap={6} align="flex-start">
            <Field label="Logo">
                 <FileUploadRoot  maxW="xl"  maxFiles={5} maxFileSize={10000000} 
                             onFileAccept={handleFileUpload} 
                             onFileReject={handleFileReject} 
                             allowDrop={true} 
                               accept={{
                                 'image/*': ['.png', '.jpg', '.jpeg'],
                              }}
                             >
                         <HStack>
                             <FileUploadTrigger asChild>
                               <Button variant="outline" size="sm"><FiUpload />Upload Logo</Button>
                             </FileUploadTrigger>
                             <Box flex="1">
                               {acceptedFiles.length > 0 ? (
                                 <List.Root>
                                   {acceptedFiles.map((file, index) => (
                                     <List.Item key={index} display="flex" alignItems="center" gap={2}>
                                       <Box display="flex" alignItems="center">
                                         <Text fontSize="md">{file.name}</Text>
                                         <Icon
                                           ml={6}
                                           color="tomato"
                                           onClick={() => handleFileRemove(file)}
                                           cursor="pointer"
                                         >
                                           <FiX />
                                         </Icon>
                                       </Box>
                                     </List.Item>
                                   ))}
                                 </List.Root>
                               ) : (
                                 <Text>No Logos uploaded yet.</Text>
                               )}
                             </Box>
                           </HStack>
                         </FileUploadRoot>
            </Field>
    
          </HStack>
          <HStack justify="flex-end" mt={2}>
            <Button w="10rem" colorScheme="orange" variant="outline" _hover={{ bg: 'red.100', transform: 'translateY(-2px)' }} transition="all 0.2s" onClick={onClose}>
              Discard
            </Button>
            <Button w="13rem" disabled={isLoading} colorScheme="blue" onClick={handleSubmit} _hover={{ transform: 'translateY(-2px)' }} transition="all 0.2s">
              {isLoading ? <Spinner size="sm" color="red.600"/> : mode == 'insert' ? 'Add' : 'Update'}
            </Button>
          </HStack>
        </>
      )}
    </Stack>
  );
};

