"use client"
import  React,{ useState,useEffect,useCallback} from "react";
import {Button,Stack} from "@chakra-ui/react"
import {MenuContent,MenuItem,MenuRoot,MenuTrigger,MenuTriggerItem} from "@/components/ui/menu"
import { BiCategory } from "react-icons/bi";
import {Category} from '@/app/utils/definitions'
import {fetchData2,fetchData } from '@/app/utils/pos';


const Filter = ({
    setQuery,
    shop_id
  }: {
    setQuery: (query: string) => void;
    shop_id?: any;
  }) => {

    const [categories, setCategories] = useState<Category[]>([]);
    const [status, setStatus] = useState([]);
    const fetchCategories = useCallback(async () => {
    try {
        const categoriesResponse = await fetchData2<Category>("categories/shop", null, null, null,shop_id);
        const statusResponse = await fetchData('status', null, null, {shop_id});
        setCategories(categoriesResponse.data);
        setStatus(statusResponse.data);
    } catch (error) {
        console.error("Error Fetching Categories:", error);
    }
    }, []);

    const handleSelect = (name: string) => {
        var cat = name;
        setQuery(cat);
      };
    
    useEffect(() => { fetchCategories()}, []);

    
   
return (
        <Stack>
                        <MenuRoot>
                          <MenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <BiCategory /> Filters
                            </Button>
                          </MenuTrigger>
                          <MenuContent>
                            {/* Status Menu */}
                            <MenuRoot positioning={{ placement: "right-start", gutter: 2 }}>
                              <MenuTriggerItem 
                                value="status"
                                _hover={{ transform: "translatex(4px)", transition: "all 0.2s", cursor: "pointer" }}
                              >
                                Status
                              </MenuTriggerItem>
                              <MenuContent>
                                {status.map((stat) => (
                                  <MenuItem key={stat.status_id} value={stat.status_id}>
                                    {stat.status_name}
                                  </MenuItem>
                                ))}
                              </MenuContent>
                            </MenuRoot>
                      
                            {/* Categories Menu with Subcategories */}
                            <MenuRoot positioning={{ placement: "right-start", gutter: 2 }}>
                              <MenuTriggerItem 
                                value="categories"
                                _hover={{ transform: "translatex(4px)", transition: "all 0.2s", cursor: "pointer" }}
                              >
                                Categories
                              </MenuTriggerItem>
                              <MenuContent>
                                   {categories.map((category) => (
                                                     <MenuRoot key={category.category_id} positioning={{ placement: "right-start", gutter: 2 }} closeOnSelect={false}  onSelect={(e) => handleSelect(e.value)}>
                                                      
                                                       <MenuTriggerItem value={category.category_id}  _hover={{ transform: "translatex(4px)", transition: "all 0.2s",cursor:"pointer" }} >{category.category_name}</MenuTriggerItem>
                                                         <MenuContent>
                                                         {category.sub_category && category.sub_category.length > 0 ? (
                                                              category.sub_category.map((sub, index) => (
                                                                <MenuItem 
                                                                  key={`${category.category_id}-${index}`} 
                                                                  value={sub} 
                                                                  style={{ cursor: 'pointer' }}
                                                                >
                                                                  {sub}
                                                                </MenuItem>
                                                              ))
                                                            ) : (
                                                              <MenuItem value="no-subcategories" disabled style={{ color: 'gray', fontStyle: 'italic' }}>
                                                                No subcategories
                                                              </MenuItem>
                                                            )}
                                                         </MenuContent>
                                                     </MenuRoot>
                                                 ))}
                              </MenuContent>
                            </MenuRoot>
                          </MenuContent>
                        </MenuRoot>
                      </Stack>
)
}

export default Filter