

'use client'

import {<PERSON>lient<PERSON>nly, Box, Flex,Skeleton, Text, Button, HStack, Image, Link,IconButton,useDisclosure} from '@chakra-ui/react'
import { Moon, Sun,Menu } from "lucide-react"
import { useColorModeValue, useColorMode } from "@/components/ui/color-mode"
import { usePathname, useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuTrigger,
} from "@/components/ui/menu"
interface Props {
  children: React.ReactNode;
  id: string;
  link?: string;
}

const Links = [{ label: "Home", id: "0", link: "/" },{ label: "About us", id: "1", link: "/about/" },{ label: "Customers", id: "2", link: "/customers/" },{ label: "Packages", id: "30", link: "/packages/" },{ label: "Offers", id: "31", link: "/offers/" },{ label: "Location", id: "32", link: "/location/" },];

const NavLink = ({ children, id, link }: Props) => {
  const path = usePathname();
  const handleClick = (event) => {
    event.preventDefault();
    console.log(id)
    const section = document.getElementById(id);
    if (section) {
      section.scrollIntoView({ behavior: "smooth", block: "start" });
      window.history.pushState({}, "", `${link}`);
  
    }
  };

  const isActive = path == link;

  return (
    <Link
      color={isActive ? "pink.400" : ""}
      variant={isActive ? "underline" : undefined}
      rounded={'md'}
      href={link}
      onClick={handleClick}
      _focus={{ outline: "none" }}
    >
      {children}
    </Link>
  );
}

export default function Nav() {
  const { colorMode, toggleColorMode } = useColorMode();
  const observerRef = useRef<IntersectionObserver | null>(null);

  return (
    <>
      <Box px={4}
        position="sticky"
        top={0}
        zIndex={999}
        width="100%"
        boxShadow={'lg'}
        height="60px"
        bg={useColorModeValue("white", "gray.800")}
      >
        <Flex alignItems={'center'} justifyContent={'space-between'}>
          <Box>
            <HStack mt={2}>
              <Image src='/log.png' alt="logo" w="25px" h="50px" />
              <Text>Alpha Baby Shop</Text>
            </HStack>
          </Box>
          <Flex display={{ base: 'none', md: 'flex' }} alignItems="center">
            <HStack gap={10}>
              {Links.map((link) => (
                <NavLink key={link.id} id={link.id} link={link.link}>
                  {link.label}
                </NavLink>
              ))}
            </HStack>
          </Flex>

          <Flex alignItems={'center'}>
              <Button onClick={toggleColorMode} size="md" borderRadius="full" variant={{base:"ghost"}}>{colorMode === 'light' ? <Moon  size={60}/> : <Sun  size={80}/>} </Button>
          </Flex>
          
          <Flex display={{ base: 'flex', md: 'none' }}>
              <MenuRoot>
              <MenuTrigger
                      as={Button}
                      cursor={'pointer'}
                      variant={{base:"ghost"}}
                      minW={0}><Menu />
                    </MenuTrigger>
                    <MenuContent alignItems={'center'}>
                      {Links.map((link) => (
                          <MenuItem
                            value=""
                            key={link.id}
                            onClick={() => {
                              const section = document.getElementById(link.id);
                              if (section) {
                                section.scrollIntoView({ behavior: 'smooth', block: 'start' });
                              }
                            }}
                          >
                            {link.label}
                          </MenuItem>
                        ))}
                    </MenuContent>
              </MenuRoot>
          </Flex>
        </Flex>
      </Box>
    </>
  );
}

