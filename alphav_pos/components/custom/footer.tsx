'use client'

import {
  Box,
  <PERSON>ton,
  Container,
  Flex,
  Stack,
  VStack,
  Text,
  VisuallyHidden,
  Heading,
  Image,
  Link,
  HStack,
} from '@chakra-ui/react'
import { FaInstagram, FaTwitter, FaFacebook,FaPhone,FaTumblr   } from 'react-icons/fa'
import { ReactNode } from 'react'
import { useColorModeValue } from "@/components/ui/color-mode"
import {phonex,phonex2,caveat} from '@/utils/data'
  const Footer = () => {
    return (
<Box  direction="column" alignContent='center'>
          <VStack align="center" gap={3}>
              <Heading  textAlign={'center'}  size="6xl" className={caveat.className} >Contact</Heading>
                    <Stack align="start">
                        <HStack gap={4}
                        cursor="pointer"
                        _hover={{
                        transform: 'scale(1.05)',
                        transition: 'all 0.2s ease-in-out',

                      }}
                        >
                          {/* <FaPhone/><Text  textStyle="3xl" fontWeight="bold">Phone:</Text> */}
                          <Link href="tel:+254729461169" color="blue.500" fontWeight="medium"  _focus={{ outline: "none" }}>
                          <VStack><Text textStyle="3xl">{phonex}</Text>
                          <Text textStyle="3xl">{phonex2}</Text></VStack>
                          </Link></HStack>
                        {/* <HStack gap={4}
                          cursor="pointer"
                          fontWeight="bold"
                         _hover={{
                          transform: 'scale(1.05)',
                          transition: 'all 0.2s ease-in-out',
                        }}>X<Text>X:</Text> 
                        <Link  color="blue.500" fontWeight="medium"  _focus={{ outline: "none" }}>@Alphas</Link></HStack>
                        <HStack gap={4}
                        cursor="pointer"
                         _hover={{
                          transform: 'scale(1.05)',
                          transition: 'all 0.2s ease-in-out',
                        }}><FaFacebook/><Text fontWeight="bold">Facebook:</Text>
                        <Link  color="blue.500" fontWeight="medium"  _focus={{ outline: "none" }}>facebook.com/alphas</Link></HStack>
                        <HStack gap={4}  
                          cursor="pointer"
                        _hover={{
                        transform: 'scale(1.05)',
                        transition: 'all 0.2s ease-in-out',
                      }}><FaInstagram/><Text fontWeight="bold">Instagram:</Text>
                      <Link  color="blue.500" fontWeight="medium"  _focus={{ outline: "none" }}>@instagram_handle</Link></HStack>
                        <HStack gap={4} 
                          cursor="pointer"
                        _hover={{
                        transform: 'scale(1.05)',
                        transition: 'all 0.2s ease-in-out',
                      }}><FaTumblr/><Text fontWeight="bold">TikTok:</Text>
                      <Link color="blue.500" fontWeight="medium"  _focus={{ outline: "none" }}>@tiktok_username</Link></HStack>
                     */}
                    </Stack>
                  </VStack>
          <Stack align="center" mt={30}>
              <Box as="a" href="/" fontSize="xl" fontWeight="bold">
              <HStack><Image src='/log.png' alt="logo" w="30px" h="60px"/><Heading>Alpha's Baby shop</Heading></HStack>
              </Box>
          <Box borderTopWidth={1} borderStyle="solid" borderColor={useColorModeValue('gray.200', 'gray.700')} >
            <Container as={Stack} py={4} direction={{ base: 'column', md: 'row' }} gap={4} justify={{ base: 'center', md: 'space-between' }} align={{ base: 'center', md: 'center' }}>
              <Text  >© {new Date().getFullYear()} Timely Fashions. All rights reserved</Text>
            </Container>
          </Box>
        </Stack>

       
      </Box>
    );
  };
  
  export default Footer;