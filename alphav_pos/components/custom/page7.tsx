'use client'

import { ReactElement,ReactNode  } from 'react'
import <PERSON>lide<PERSON> from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import { Box, SimpleGrid, Heading,HStack,VStack, Text, Image,Flex,Card,useBreakpointValue,Button } from '@chakra-ui/react'
import {MotionCard,MotionImage,MotionBox,MotionSimpleGrid,Ximages,MotionHStack,MotionFlex,variants,images,images2,caveat,fromRight} from '@/utils/data'
interface FeatureProps {icon: String,children?: ReactNode}
import Carousel from 'react-elastic-carousel'
// const Feature = ({icon, children }: FeatureProps ) => {
//   const cardWidth = useBreakpointValue({ base: "100%", md: "100%", lg: "100%" });
//   return (
//         <Box 
//             position="relative" 
//             width="450px" 
//             height="600px" 
//             rounded="md" 
//             overflow="hidden" 
//             >
//                   <MotionImage
//                         variants={images2}
//                         src={`/${icon}`}
//                         alt="offers"
//                         fill
//                         objectFit="contain"
//                       />

//                   </Box>
//   )
// }

// export default  function Page3() {
  
//   return (
//         <VStack gap={5}>
//           <Heading  textAlign={'center'}  size="6xl" className={caveat.className} >Events & Offers</Heading>
      
//                   <MotionSimpleGrid 
//                     columns={{ base: 1, md: 3 }} 
//                     gap={{ base: 0, md: 5 }}
//                     variants={variants}
//                     initial="hidden"
//                     animate="show"
//                     overflow="hidden"
//                     pl={{ base:0, md: 10 }}
//                     pr={{ base:0, md: 20 }}
//                   >
//                   {Ximages.map((image, index) => (
//                         <Feature key={index} icon={image} />
//                     ))}
//                   </MotionSimpleGrid>

//           </VStack>
//   )
// }


const Feature = ({icon, children }: FeatureProps ) => {
  const cardWidth = useBreakpointValue({ base: "100%", md: "100%", lg: "100%" });
  return (
        <Box position="relative" width="450px" height="450px" rounded="md" overflow="hidden" >
          <MotionImage variants={images2} src={`/${icon}`} alt="offers" fill  style={{ objectFit: 'contain' }}  sizes="(max-width: 450px) 100vw, 450px" />
        </Box>
  )
}

export default  function Page3() {
  const isLargeScreen = useBreakpointValue({ base: false, md: true });
  return (
        <VStack  pt={{base:'0',md:'70px'}}>
          <Heading  textAlign={'center'}  size="6xl" className={caveat.className} >Events & Offers</Heading>
      
          <Box>
          {isLargeScreen ?
                  <Carousel focusOnSelect={true} itemsToShow={3} itemsToScroll={3} 
                  easing="cubic-bezier(1,.15,.55,1.54)"
                  tiltEasing="cubic-bezier(0.110, 1, 1.000, 0.210)"
                  transitionMs={700}
                  itemPadding={[10, 10]}
                  style={{ width: '100vw',paddingLeft:'30px',paddingRight:'30px' }}>
                             {Ximages.map((image, index) => (
                        <Feature key={index} icon={image} />
                    ))}
                  </Carousel>:<Carousel focusOnSelect={true} itemsToShow={1} itemsToScroll={1}  enableAutoPlay 
                                    easing="cubic-bezier(1,.15,.55,1.54)"
                                    showArrows={false}
                                    pagination={false}
                                    tiltEasing="cubic-bezier(0.110, 1, 1.000, 0.210)"
                                    transitionMs={700}
                                    className="carousel-container"
                                    style={{ width: '100vw' }}>
                                                {Ximages.map((image, index) => (
                                                    <Feature key={index} icon={image} />
                                                ))}
                                    </Carousel>}
              </Box>
         </VStack>
  )
}

