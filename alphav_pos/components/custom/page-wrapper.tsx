'use client';
import { motion, AnimatePresence } from 'motion/react';
import { usePathname } from 'next/navigation';
const MotionDiv = motion.create('div')
export default function PageWrapper({ children }) {
    const pathname = usePathname(); 
  return (
    <AnimatePresence>
      <MotionDiv
        // initial={{ opacity: 1, y: 15 }}
        // animate={{ opacity: 1, y: 0 }}
        // exit={{ opacity: 1, y: 15 }}
        // transition={{ delay: 0.25 }}
        key={pathname}
        initial={{ opacity: 1, y: 20 }} 
        animate={{ opacity: 1, y: 0 }} 
        exit={{ opacity: 0, y: 20 }} 
        transition={{ duration: 0.25, ease: 'easeIn' }}
      >
        {children}
      </MotionDiv>
    </AnimatePresence>
  );
}
