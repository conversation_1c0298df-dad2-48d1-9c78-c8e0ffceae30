'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { hasRoutePermission, isUserAuthorized, getDefaultRoute } from '@/app/utils/rbac';
import { Box, Spinner, Center, Text, VStack, Button } from '@chakra-ui/react';
import { LuShieldAlert } from 'react-icons/lu';

interface RouteGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export default function RouteGuard({ children, fallback }: RouteGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);
  
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  useEffect(() => {
    const checkAuthorization = () => {
      // Skip auth check for public routes
      const publicRoutes = ['/'];
      if (publicRoutes.includes(pathname)) {
        setIsAuthorized(true);
        setIsLoading(false);
        return;
      }

      // Check if user is authenticated
      if (!isUserAuthorized(currentUser)) {
        // Redirect to login if not authenticated
        router.push('/');
        setIsLoading(false);
        return;
      }

      // Check route permissions
      const hasPermission = hasRoutePermission(currentUser!.roles, pathname);

      if (!hasPermission) {
        // Redirect to user's default route if no permission
        const defaultRoute = getDefaultRoute(currentUser!.roles);
        router.push(defaultRoute);
        setIsLoading(false);
        return;
      }

      setIsAuthorized(true);
      setIsLoading(false);
    };

    checkAuthorization();
  }, [currentUser, pathname, router]);

  // Loading state
  if (isLoading) {
    return (
      <Center h="100vh">
        <VStack gap={4}>
          <Spinner size="xl" color="red.600" />
          <Text>Checking permissions...</Text>
        </VStack>
      </Center>
    );
  }

  // Unauthorized state
  if (!isAuthorized) {
    return fallback || (
      <Center h="100vh">
        <VStack gap={4} textAlign="center">
          <Box fontSize="4xl" color="red.500">
            <LuShieldAlert />
          </Box>
          <Text fontSize="xl" fontWeight="bold">Access Denied</Text>
          <Text color="gray.600">
            You don't have permission to access this page.
          </Text>
          <Button 
            colorScheme="blue" 
            onClick={() => {
              const defaultRoute = currentUser ? getDefaultRoute(currentUser.roles) : '/';
              router.push(defaultRoute);
            }}
          >
            Go to Dashboard
          </Button>
        </VStack>
      </Center>
    );
  }

  return <>{children}</>;
}
