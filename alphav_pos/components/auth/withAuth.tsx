'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store';
import { hasRoutePermission, isUserAuthorized, UserRole } from '@/app/utils/rbac';

interface WithAuthOptions {
  requiredRoles?: UserRole[];
  redirectTo?: string;
  requireAuth?: boolean;
}

export function withAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthOptions = {}
) {
  const {
    requiredRoles = [],
    redirectTo = '/',
    requireAuth = true
  } = options;

  return function AuthenticatedComponent(props: P) {
    const router = useRouter();
    const currentUser = useSelector((state: RootState) => state.user.currentUser);

    useEffect(() => {
      // Skip auth check if not required
      if (!requireAuth) return;

      // Check if user is authenticated
      if (!isUserAuthorized(currentUser)) {
        router.push(redirectTo);
        return;
      }

      // Check specific role requirements
      if (requiredRoles.length > 0) {
        // Extract role names from role objects
        const userRoleNames = currentUser!.roles.map((role: any) => {
          if (typeof role === 'string') {
            return role;
          } else if (role && typeof role === 'object' && role.role_name) {
            return role.role_name;
          }
          return '';
        });

        const hasRequiredRole = requiredRoles.some(role =>
          userRoleNames.includes(role)
        );

        if (!hasRequiredRole) {
          // Redirect to unauthorized page or default route
          router.push('/unauthorized');
          return;
        }
      }
    }, [currentUser, router]);

    // Don't render if auth is required but user is not authenticated
    if (requireAuth && !isUserAuthorized(currentUser)) {
      return null;
    }

    // Don't render if specific roles are required but user doesn't have them
    if (requiredRoles.length > 0 && currentUser) {
      // Extract role names from role objects
      const userRoleNames = currentUser.roles.map((role: any) => {
        if (typeof role === 'string') {
          return role;
        } else if (role && typeof role === 'object' && role.role_name) {
          return role.role_name;
        }
        return '';
      });

      const hasRequiredRole = requiredRoles.some(role =>
        userRoleNames.includes(role)
      );

      if (!hasRequiredRole) {
        return null;
      }
    }

    return <WrappedComponent {...props} />;
  };
}

// Convenience HOCs for specific roles
export const withAdminAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin'] });

export const withManagerAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'supervisor'] });

export const withSupervisorAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'supervisor'] });

export const withPOSAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'supervisor', 'pos'] });

export const withInventoryAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'supervisor', 'inventory'] });

export const withSuppliesAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'supervisor', 'supplies'] });

export const withReportsAuth = <P extends object>(Component: React.ComponentType<P>) =>
  withAuth(Component, { requiredRoles: ['admin', 'manager', 'reports'] });
