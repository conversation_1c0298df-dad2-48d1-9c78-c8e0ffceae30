"use client"
import React,{useState,useEffect,useMemo,useRef,useCallback} from 'react'
import {Text,Stack,Box,Center,Card,VStack,HStack,Input,Table,Circle,Float ,Heading,Flex,Separator,Button,Skeleton,ProgressCircle} from '@chakra-ui/react';
import { InputGroup } from "@/components/ui/input-group"
import { FiSearch, FiX} from 'react-icons/fi'
import Manager from '@/components/pos/manager'
import { useAppDispatch, useAppSelector } from "@/lib/hooks";
import { withManagerAuth } from '@/components/auth/withAuth';

const Page = () => {
  const user = useAppSelector((state) => state.user.currentUser);
  return (<Manager user={user}/>);
};

export default withManagerAuth(Page);