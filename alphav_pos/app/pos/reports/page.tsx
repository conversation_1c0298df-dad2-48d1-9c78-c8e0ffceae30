"use client"
import React, { useState, useEffect, useCallback,Suspense } from 'react'
import NextLink from 'next/link';
import { formatCurrency } from '@/app/utils/actions'
import { Text, Stack,Spinner, Box, Center, Card, VStack, HStack, Input, Table, Heading, Flex, Button, Skeleton, Grid,Tabs} from '@chakra-ui/react';
import {  fetchData,fetchData2} from '@/app/utils/pos';
import { useAppSelector } from "@/lib/hooks";
import { toaster } from "@/components/ui/toaster"
import { StatHelpText, StatRoot, StatValueText } from "@/components/ui/stat"
import { FiDownload, FiPrinter, FiBarChart2, FiDollarSign, FiPackage, FiTrendingUp } from 'react-icons/fi';
import Search from '@/components/pos/search'
import Table_default from '@/components/pos/Table_default2'
import {NativeSelectField,NativeSelectRoot,} from "@/components/ui/native-select"
import FinancialReports from '@/components/pos/financial_reports'

const ReportsPage = () => {
  const user = useAppSelector((state) => state.user.currentUser);
  const shop_id = user?.currentshop;

  // State variables
  const [isLoading, setIsLoading] = useState(false);
  const [timeFrame, setTimeFrame] = useState('daily');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [salesData, setSalesData] = useState([]);
  const [inventoryData, setInventoryData] = useState([]);
  const [bestSellingItems, setBestSellingItems] = useState([]);
  const [totalRevenue, setTotalRevenue] = useState(0);
  const [totalSales, setTotalSales] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [query, setQuery] = useState('');
  const [activeTab, setActiveTab] = useState('sales');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedReport, setSelectedReport] = useState(null);
  const [showInvoice, setShowInvoice] = useState(false);

  // Load report data based on selected time frame
  const loadReportData = useCallback(async () => {
    if (!shop_id) return;
    setIsLoading(true);
    try {
      // Prepare query parameters with date range if provided
      const queryParams = {
        shop_id,
        timeFrame,
        query,
        currentPage,
        pageSize,
        consolidated: true
      };

      if (startDate) queryParams.startDate = startDate;
      if (endDate) queryParams.endDate = endDate;

      const responseData = await fetchData2('reports', currentPage, pageSize, queryParams);
      const reportData: any = responseData?.data || responseData;

      if (reportData) {
        // Process sales data
        if (reportData.salesData) {
          setSalesData(reportData.salesData);
        }

        setTotalRecords(reportData.metadata?.total || reportData.metadata?.totalRecords || 0);

        // Process inventory data
        if (reportData.inventoryData) {
          setInventoryData(reportData.inventoryData.map((item: any) => ({
            ...item,
            total_value: Number(item.current_quantity) * Number(item.selling_price)
          })));
        }

        // Process best selling items first to get real revenue data
        if (reportData.bestSellingItems && reportData.bestSellingItems.length > 0) {
          const bestItems = reportData.bestSellingItems.map((item: any) => ({
            ...item,
            profit: Number(item.total_profit) || 0
          }));
          setBestSellingItems(bestItems);

          // Calculate totals from best selling items (real data)
          const totalRevenueFromBestSellers = bestItems.reduce((sum: number, item: any) => {
            return sum + (Number(item.actual_revenue) || 0);
          }, 0);

          const totalUnitsSold = bestItems.reduce((sum: number, item: any) => {
            return sum + (Number(item.units_sold) || 0);
          }, 0);

      
          // Use best sellers data if it has more meaningful values
          if (totalRevenueFromBestSellers > 0) {
            setTotalRevenue(totalRevenueFromBestSellers);
            setTotalItems(totalUnitsSold);
            setTotalSales(bestItems.length);
          }
        }

        // Process summary statistics (fallback if best sellers don't have data)
        if (reportData.summaryStats) {
          const summaryRevenue = Number(reportData.summaryStats.totalRevenue) || 0;
          const summaryTransactions = Number(reportData.summaryStats.totalTransactions) || 0;

          // Only use summary stats if we don't have better data from best sellers
          if (totalRevenue === 0 && summaryRevenue > 0) {
            setTotalRevenue(summaryRevenue);
          }
          if (totalSales === 0 && summaryTransactions > 0) {
            setTotalSales(summaryTransactions);
          }

          // Use status counts for transaction count
          const statusCounts = reportData.summaryStats.statusCounts || {};
          const totalTransactionCount = Object.values(statusCounts).reduce((sum: number, count: any) => {
            return sum + (Number(count) || 0);
          }, 0) as number;

          if (totalTransactionCount > 0) {
            setTotalSales(totalTransactionCount);
          }
        }
      }
    } catch (error) {
      toaster.error({
        title: 'Failed to load report data',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  }, [shop_id, timeFrame, currentPage, pageSize, query, startDate, endDate]);


  useEffect(() => {
    if (timeFrame != 'custom') {
      loadReportData();
    }
  }, [loadReportData, timeFrame]);

  // Handle page change
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage.page);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize.pagesize);
    setCurrentPage(1);
  };

  // Handle search
  const handleSearch = (searchValue) => {
    setQuery(searchValue);
    setCurrentPage(1);
  };

  // Handle time frame change
  const handleTimeFrameChange = (event) => {
    const newTimeFrame = event.target.value;
    setTimeFrame(newTimeFrame);
    setCurrentPage(1);
    // custom: show pickers only
    if (newTimeFrame == 'custom') {
      setStartDate('');
      setEndDate('');
      return;
    }
    // compute start/end for other frames
    const today = new Date();
    let startDateObj;
    switch (newTimeFrame) {
      case 'daily': startDateObj = today; break;
      case 'weekly': startDateObj = new Date(today); startDateObj.setDate(today.getDate() - 6); break;
      case 'monthly': startDateObj = new Date(today.getFullYear(), today.getMonth(), 1); break;
      case 'yearly': startDateObj = new Date(today.getFullYear(), 0, 1); break;
      default: startDateObj = today;
    }
    const formatDate = d => d.toISOString().split('T')[0];
    setStartDate(formatDate(startDateObj));
    setEndDate(formatDate(today));
    // loadReportData();
  };

  // View invoice details
  const handleViewInvoice = (report) => {
    setSelectedReport(report);
    setShowInvoice(true);
  };

  // Print invoice
  const handlePrintInvoice = () => {
    // Implementation for printing invoice
    window.print();
  };

  // Download invoice as PDF
  const handleDownloadInvoice = (report) => {
    setIsLoading(true);
    try {

      if (!report || !report.items || report.items.length == 0) {
        toaster.error({
          title: 'No Data',
          description: 'There is no data to generate an invoice.',
        });
        return;
      }

      const fileName = `invoice_${report.transaction_id}_${new Date().toISOString().split('T')[0]}.csv`;

      // Create CSV content for invoice
      const headers = ['Item', 'Quantity', 'Price', 'Total'];
      let csvContent = [
        headers.join(','),
        ...report.items.map(item => [
          item.receipt_item,
          item.receipt_quantity,
          item.receipt_each,
          item.receipt_total
        ].join(','))
      ].join('\n');

      // Add summary information
      csvContent += `\n\nSubtotal,${report.transaction_net}\nTax,${report.transaction_tax}\nTotal,${report.transaction_total}`;

      // Create a download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toaster.success({
        title: 'Invoice Downloaded',
        description: `Invoice for transaction ${report.transaction_id} has been downloaded.`,
      });
    } catch (error) {
      toaster.error({
        title: 'Failed to download invoice',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Close invoice view
  const handleCloseInvoice = () => {
    setShowInvoice(false);
    setSelectedReport(null);
  };

  // Generate and download reports
  const handleGenerateReport = async (reportType) => {
    setIsLoading(true);
    try {
      let data = [];
      let fileName = '';
      let headers = [];

      switch (reportType) {
        case 'sales':
          data = salesData;
          fileName = `sales_report_${timeFrame}_${new Date().toISOString().split('T')[0]}.csv`;
          headers = ['Transaction ID', 'Date', 'Items', 'Total', 'Status'];
          break;
        case 'best_sellers':
          data = bestSellingItems;
          fileName = `best_sellers_report_${timeFrame}_${new Date().toISOString().split('T')[0]}.csv`;
          headers = ['Item ID', 'Name', 'Category', 'Units Sold', 'Revenue', 'Profit'];
          break;
        default:
          throw new Error('Invalid report type');
      }

      if (!data || data.length === 0) {
        toaster.error({
          title: 'No Data',
          description: 'There is no data to generate a report.',
        });
        return;
      }

      // Create CSV content
      let csvContent = headers.join(',') + '\n';

      if (reportType === 'sales') {
        csvContent += data.map(item => [
          item.transaction_id,
          new Date(item.transaction_date).toLocaleString(),
          item.transaction_quantity || 0,
          item.transaction_total || 0,
          item.transaction_status?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        ].join(',')).join('\n');
      } else if (reportType === 'best_sellers') {
        csvContent += data.map(item => [
          item.item_id,
          item.item_name,
          item.category_name,
          item.units_sold,
          item.item_selling,
          item.profit
        ].join(',')).join('\n');
      }

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toaster.success({
        title: 'Report Generated',
        description: `${reportType.replace('_', ' ')} report has been downloaded successfully.`,
      });
    } catch (error) {
      toaster.error({
        title: 'Failed to generate report',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Table columns for sales report
  const salesColumns = [
    { label: 'Transaction ID', key: 'transaction_id', align: 'left' },
    {
      label: 'Date',
      key: 'transaction_date',
      align: 'left',
      format: (value) => new Date(value).toLocaleString()
    },
    {
      label: 'Items',
      key: 'transaction_quantity',
      align: 'left',
      format: (value) => value || 0
    },
    {
      label: 'Total',
      key: 'transaction_total',
      align: 'left',
      format: (value) => value ? `KSH ${parseFloat(value).toFixed(2)}` : 'KSH 0.00'
    },
    {
      label: 'Status',
      key: 'transaction_status',
      align: 'left',
      format: (value) => value?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    },
    { label: 'Action', key: 'actions', align: 'center' }
  ];

  // Get actions for sales table
  const getSalesActions = (item) => [
    { label: 'View Invoice', colorScheme: 'blue', onClick: () => handleViewInvoice(item) },
  ];

  // Helper function to safely convert to string and handle NaN
  const safeValueToString = (value) => {
    if (value === null || value === undefined || isNaN(value)) {
      return "0";
    }
    return value.toString();
  };

  // Stats cards data
  const statsCards = [
    {
      title: "Total Revenue",
      icon: <FiDollarSign />,
      value: safeValueToString(totalRevenue),
      color: "green.500",
      isCurrency: true
    },
    {
      title: "Total Sales",
      icon: <FiBarChart2 />,
      value: safeValueToString(totalSales),
      color: "blue.500",
      isCurrency: false
    },
    {
      title: "Items Sold",
      icon: <FiPackage />,
      value: safeValueToString(totalItems),
      color: "purple.500",
      isCurrency: false
    },
    {
      title: "Best Seller",
      icon: <FiTrendingUp />,
      value: bestSellingItems.length > 0 ? (bestSellingItems[0]?.item_name || "No data") : "No data",
      color: "orange.500",
      isCurrency: false,
      isText: true
    }
  ];

  return (

    <Box p={4} ml={"1.5rem"}>

        {isLoading && (
            <Flex
              position="fixed"
              top="0"
              left="0"
              height="100vh"
              width="100vw"
              backgroundColor="rgba(0, 0, 0, 0.5)"
              justifyContent="center"
              alignItems="center"
              zIndex="9999"
            >
              <Spinner size="xl" color="red.600" borderWidth="4px"/>
            </Flex>
          )}

      <Heading size="lg" mb={6}>Sales & Inventory Reports</Heading>
      {/* <HStack gap={2} mb={3} justifyContent="space-between">

        <HStack align="center" gap={4}>
          <NativeSelectRoot
            size="md"
            width="200px"
          >
            <NativeSelectField
              placeholder="Select time frame"
              value={timeFrame}
              onChange={(e) => handleTimeFrameChange(e)}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="yearly">Yearly</option>
              <option value="custom">Custom Range</option>
            </NativeSelectField>
          </NativeSelectRoot>

          {timeFrame == 'custom' && (
            <>
              <HStack align="center" gap={1}>
                <Text textStyle="xs">Start:</Text>
                <Input
                  type="date"
                  value={startDate}
                  onChange={e => setStartDate(e.target.value)}
                  size="xs"
                  max={endDate || undefined}
                />
              </HStack>

              <HStack align="center" gap={1}>
                <Text textStyle="xs">End:</Text>
                <Input
                  type="date"
                  value={endDate}
                  onChange={e => setEndDate(e.target.value)}
                  size="xs"
                  min={startDate || undefined}
                />
              </HStack>

              <Button
                colorScheme="blue"
                size="xs"
                disabled={!startDate || !endDate ||isLoading}
                onClick={loadReportData}
              >
                Apply
              </Button>
              </>
          )}
        </HStack>

        <HStack>
          <Button colorScheme="blue" onClick={() => handleGenerateReport('sales')}>
          <FiDownload />Generate Sales Report
          </Button>
          <Button  colorScheme="green" onClick={handlePrintInvoice} disabled={!showInvoice}>
          <FiPrinter />Print Invoice
          </Button>
          {/* <Link href="/pos/reports/suppliers" passHref>
            <Button  as={NextLink} colorScheme="purple">
              Supplier Tracking
            </Button>
          </Link> 
          <NextLink href="/pos/reports/suppliers" passHref>
            <Button colorScheme="purple">
              Supplier Tracking
            </Button>
          </NextLink>
        </HStack>
      </HStack> */}
      <Stack
          direction={{ base: 'column', md: 'row' }}
          gap={4}
          mb={3}
          justify="space-between"
          flexWrap="wrap"
          align={{ base: 'stretch', md: 'center' }}
        >
    
        <NativeSelectRoot size="md" width={{ base: '100%', sm: '200px' }}>
          <NativeSelectField
            placeholder="Select time frame"
            value={timeFrame}
            onChange={(e) => handleTimeFrameChange(e)}
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="custom">Custom Range</option>
          </NativeSelectField>
        </NativeSelectRoot>

        {timeFrame === 'custom' && (
          <>
            <HStack align="center" gap={1}>
              <Text textStyle="xs">Start:</Text>
              <Input
                type="date"
                value={startDate}
                onChange={e => setStartDate(e.target.value)}
                size="xs"
                max={endDate || undefined}
              />
            </HStack>

            <HStack align="center" gap={1}>
              <Text textStyle="xs">End:</Text>
              <Input
                type="date"
                value={endDate}
                onChange={e => setEndDate(e.target.value)}
                size="xs"
                min={startDate || undefined}
              />
            </HStack>

            <Button
              colorScheme="blue"
              size="xs"
              disabled={!startDate || !endDate || isLoading}
              onClick={loadReportData}
            >
              Apply
            </Button>
          </>
        )}

<HStack
  align={{ base: 'start', md: 'end' }}
  justify={{ base: 'flex-start', md: 'flex-end' }}
  flexWrap="wrap"
  gap={2}
>
  <Button colorPalette="blue" onClick={() => handleGenerateReport('sales')}>
    <FiDownload />
    Generate Sales Report
  </Button>

  <Button
    colorPalette="green"
    onClick={handlePrintInvoice}
    disabled={!showInvoice}
  >
    <FiPrinter />
    Print Invoice
  </Button>

  <NextLink href="/pos/reports/suppliers" passHref>
    <Button colorPalette="purple">
      Supplier Tracking
    </Button>
  </NextLink>
</HStack>

    </Stack>


      {/* Stats Cards */}
      <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6} mb={6}>
        {statsCards.map((stat, index) => (
          <Card.Root key={index} p={4} borderRadius="lg" boxShadow="sm">
            <Stack direction="column" gap={2}>
              <HStack justifyContent="space-between">
                <Heading color={stat.color} size="sm" fontWeight="semibold">
                  {stat.title}
                </Heading>
                <Box color={stat.color} fontSize="xl">
                  {stat.icon}
                </Box>
              </HStack>
              <StatRoot>
                {stat.isText ? (
                  <Text fontSize="xl" fontWeight="bold">{stat?.value}</Text>
                ) : (
                  <StatValueText
                    textStyle="xl"
                    fontWeight="bold"
                    value={stat?.value}
                    formatOptions={stat.isCurrency ? {
                      style: "currency",
                      currency: "KES",
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    } : undefined}
                    // formatOptions={stat.isCurrency ? { style: "currency", currency: "KSH" } : undefined}
                  />
                )}
                <StatHelpText fontSize="sm" color="gray.500">
                  {timeFrame?.charAt(0).toUpperCase() + timeFrame?.slice(1)} report
                </StatHelpText>
              </StatRoot>
            </Stack>
          </Card.Root>
        ))}
      </Grid>

      {/* Tabs for different report types */}
      <Tabs.Root  variant="enclosed" colorScheme="blue" mb={6} defaultValue={'sales'}>
        <Tabs.List>
          <Tabs.Trigger value='sales'>Sales Reports</Tabs.Trigger>
          <Tabs.Trigger value='bst'>Best Selling Items</Tabs.Trigger>
          <Tabs.Trigger value='financial'>Financial Reports</Tabs.Trigger>
          
        </Tabs.List>

          <Suspense fallback={<Center  h={'70vh'} ><Spinner size="xl" /></Center>}>

          <Tabs.Content value='sales'>
            <Box mb={4}>
              <Search
                placeholder="Search by transaction ID or status"
                tableName="reports"
                onSearch={handleSearch}
              />
            </Box>

            {isLoading ? (
              <Center p={8}>
                <Skeleton height="400px" width="100%" />
              </Center>
            ) : (
              <Table_default
                columns={salesColumns}
                data={salesData}
                getActions={getSalesActions}
                currentPage={currentPage}
                pageSize={pageSize}
                totalItems={totalRecords}
                handlePageChange={handlePageChange}
                handlePageSizeChange={handlePageSizeChange}
                isLoading={isLoading}
                width="100%"
                height="auto"
              />
            )}
          </Tabs.Content>


          {/* Best Selling Items Tab */}
          <Tabs.Content value='financial'>
                <FinancialReports />
          </Tabs.Content>

      <Tabs.Content value='bst'>
            {isLoading ? (
              <Center p={8}>
                <Skeleton height="400px" width="100%" />
              </Center>
            ) : (
              <>
                <Table_default
                  columns={[
                    { label: 'Item ID', key: 'item_id', align: 'left' },
                    { label: 'Name', key: 'item_name', align: 'left' },
                    { label: 'Category', key: 'category_name', align: 'left' },
                    { label: 'Units Sold', key: 'units_sold', align: 'left' },
                    { label: 'Revenue', key: 'item_selling', align: 'left' },
                    { label: 'Profit', key: 'profit', align: 'left' }
                  ]}
                  data={bestSellingItems}
                  getActions={() => []}
                  currentPage={1}
                  pageSize={10}
                  totalItems={bestSellingItems.length}
                  handlePageChange={handlePageChange}
                  handlePageSizeChange={handlePageSizeChange}
                  isLoading={isLoading}
                  width="100%"
                  height="auto"
                />

                <HStack justifyContent="flex-end" mt={4}>
                  <Button  colorScheme="blue" onClick={() => handleGenerateReport('best_sellers')}>
                  <FiDownload />Export Best Sellers Report
                  </Button>
                </HStack>
              </>
            )}
          </Tabs.Content>
        </Suspense>
      </Tabs.Root >

      {/* Invoice Modal */}
      {showInvoice && selectedReport && (
        <Box
          position="fixed"
          top="0"
          left="0"
          right="0"
          bottom="0"
          // bg="rgba(0,0,0,0.7)"

          zIndex="1000"
          display="flex"
          alignItems="center"
          justifyContent="center"
          onClick={handleCloseInvoice}
        >
          <Box
               bg="gray.300"
               _dark={{ bg: "gray.900" }}
            p={6}
            borderRadius="md"
            maxW="800px"
            w="90%"
            maxH="90vh"
            overflowY="auto"
            onClick={(e) => e.stopPropagation()}
          >
            <HStack justifyContent="space-between" mb={4}>
              <Heading size="md">Invoice #{selectedReport.transaction_receipt_id}</Heading>
              <Button size="sm" onClick={handleCloseInvoice}>Close</Button>
            </HStack>

            <VStack align="stretch" gap={4}>
              <HStack justifyContent="space-between">
                <Box>
                  <Text fontWeight="bold">Transaction ID:</Text>
                  <Text>{selectedReport.transaction_id}</Text>
                </Box>
                <Box>
                  <Text fontWeight="bold">Date:</Text>
                  <Text>{selectedReport.transaction_date}</Text>
                </Box>
              </HStack>

              <Box>
                <Text fontWeight="bold" mb={2}>Items:</Text>
                  <Table.Root variant="outline" size="sm" colorPalette="gray">
                    <Table.Header>
                      <Table.Row>
                        <Table.ColumnHeader>Item</Table.ColumnHeader>
                        <Table.ColumnHeader>Quantity</Table.ColumnHeader>
                        <Table.ColumnHeader>Price</Table.ColumnHeader>
                        <Table.ColumnHeader>Total</Table.ColumnHeader>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {selectedReport.items?.map((item, index) => (
                        <Table.Row key={index}>
                          <Table.Cell>{item.receipt_item || 'Unnamed Item'}</Table.Cell>
                          <Table.Cell>{item.receipt_quantity || 0}</Table.Cell>
                          <Table.Cell>{formatCurrency(item.receipt_each)}</Table.Cell>
                          <Table.Cell>{formatCurrency(item.receipt_total)}</Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table.Root>
              </Box>

              <HStack justifyContent="flex-end">
                <Box textAlign="right">
                  <Text><strong>Subtotal:</strong> {selectedReport.transaction_net}</Text>
                  <Text><strong>Tax:</strong> {selectedReport.transaction_tax}</Text>
                  <Text fontWeight="bold"><strong>Total:</strong> {selectedReport.transaction_total}</Text>
                </Box>
              </HStack>

              <HStack justifyContent="center" pt={4}>
                <Button  colorScheme="blue" onClick={handlePrintInvoice}>
                <FiPrinter />Print Invoice
                </Button>
                <Button colorScheme="green" onClick={() => handleDownloadInvoice(selectedReport)}>
                <FiDownload /> Download PDF
                </Button>
              </HStack>
            </VStack>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ReportsPage;
