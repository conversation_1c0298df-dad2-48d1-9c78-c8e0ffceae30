
"use client";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, HStack, <PERSON>, <PERSON>, But<PERSON>, Input, Spinner } from "@chakra-ui/react";
import React, { useState, useEffect } from "react";
import { Field } from "@/components/ui/field";
import Image from "next/image";
import AnimatedHeader from "@/components/pos/animate";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {insertData} from '@/app/utils/pos';
import { useAppDispatch,useAppSelector} from "@/lib/hooks";
import { setUser, logout } from "@/lib/features/users";
import { useRouter } from "next/navigation";


const loginSchema = z.object({
  email: z.string().email("Invalid email address").nonempty("Email is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const signUpSchema = loginSchema.extend({
  confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters"),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address").nonempty("Email is required"),
});

const passwordChangeSchema = z
  .object({
    newPassword: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export default function Page() {
  const [isLogin, setIsLogin] = useState(true);
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const [isChangePassword, setIsChangePassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [Error, setError] = useState('');
  const [user_id, setUser_id] = useState('');
  const [Message, setMesssage] = useState('');
  const dispatch = useAppDispatch();
  const router = useRouter(); 

  // Create a stable schema that includes all possible fields
  const allFieldsSchema = z.object({
    email: z.string().email("Invalid email address").optional(),
    password: z.string().min(6, "Password must be at least 6 characters").optional(),
    confirmPassword: z.string().min(6, "Confirm password must be at least 6 characters").optional(),
    newPassword: z.string().min(6, "Password must be at least 6 characters").optional(),
  });

  const { handleSubmit, register, formState: { errors }, reset } = useForm({
    resolver: zodResolver(allFieldsSchema),
  });

  // Custom validation function
  const validateForm = (data: any) => {
    if (isForgotPassword) {
      return forgotPasswordSchema.safeParse(data);
    } else if (isLogin) {
      return loginSchema.safeParse(data);
    } else if (isChangePassword) {
      return passwordChangeSchema.safeParse(data);
    } else {
      return signUpSchema.safeParse(data);
    }
  };

  const handleForgotPassword = () => {
    setIsForgotPassword(true);
    setIsChangePassword(false);
    setIsLogin(false);
    reset();
  };

  const handleChangePassword = () => {
    setIsChangePassword(true);
    setIsForgotPassword(false);
    setIsLogin(false);
    reset();
  };

  const handleBackToLogin = () => {
    setIsLogin(true);
    setIsForgotPassword(false);
    setIsChangePassword(false);
    reset();
  };

  const onSubmit = async (data) => {
    // Validate with the appropriate schema
    const validation = validateForm(data);
    if (!validation.success) {
      setError(validation.error.errors[0]?.message || "Validation failed");
      return;
    }

    setIsLoading(true);
    setError("");
    try {
      let response;
   
      if (isLogin) {
        response = await insertData('auth_login', data);
        if (response?.requires_password_change) {
          handleChangePassword();
          setMesssage(response?.message);
          setUser_id(response?.user_id);
        } else {
          // Debug: Log the response to see what we're getting
          console.log('Login response:', response);

          // Ensure the user object has the required fields for RBAC
          const userData = {
            ...response,
            authenticated: true,
            // Handle roles - they might be objects or strings
            roles: response.roles && response.roles.length > 0
              ? response.roles
              : [{ role_name: 'pos', role_id: 'default' }] // Default role object
          };

          console.log('Setting user data:', userData);
          dispatch(setUser(userData));
          router.push("/pos");
        }
      } else if (isChangePassword) {
        const for_data = { ...data, user_id };
        response = await insertData(`auth_forgot`, for_data);
        if (response) {
          // Clear Redux state and storage after successful password change
          dispatch(logout());
          localStorage.clear();
          sessionStorage.clear();

          // Show success message
          setMesssage("Password changed successfully! Please login with your new password.");

          // Redirect back to login form
          handleBackToLogin();
        }
      } else if (isForgotPassword) {
        response = await insertData("auth_reset", data);
        if (response?.success) {
          setMesssage("Password reset instructions sent to your email.");
        } else {
          setError(response?.message || "Failed to process request.");
        }
      }
    } catch (error) {
      setError(error.message);
    } finally {
      setIsLoading(false);
      setTimeout(() => setMesssage(""), 4000);
      setTimeout(() => setError(""), 2000);
    }
  };

  return (
    <>
      {/* CSS Animations */}
      <style jsx global>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
      `}</style>

      <Flex minHeight="100vh" gap={0} direction={{ base: "column", lg: "row" }}>
        <Box position="absolute" top={0} left={0} width="100%" height="100%" zIndex={2}>
          <AnimatedHeader />
        </Box>

        {/* Image Section - Hidden on mobile/tablet, shown on desktop */}
        <Box
          flex="1"
          height={{ base: "0", lg: "100vh" }}
          position="relative"
          overflow="hidden"
          zIndex={10}
          display={{ base: "none", lg: "block" }}
        >
          <Image
            src="/pos/11.jpg"
            alt="login"
            fill
            style={{ objectFit: "cover" }}
            priority
            sizes="50vw"
          />
        </Box>

        {/* Form Section - Centered on mobile, right side on desktop */}
        <Box
          flex={{ base: "none", lg: "1" }}
          display="flex"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          width="100%"
          p={{ base: 4, md: 6 }}
        >
          <Box
            p={{ base: 6, md: 8 }}
            borderRadius="lg"
            boxShadow="lg"
            maxW={{ base: "90%", sm: "400px", md: "450px" }}
            w="100%"
            zIndex={10}
            bg="rgba(255, 255, 255, 0.9)"
            _dark={{ bg: "rgba(0, 0, 0, 0.9)" }}
            mx="auto"
          >
            <VStack gap={6} align="stretch">
              <Text fontSize="4xl" fontWeight="bold" textAlign="center">
                {isForgotPassword ? "Forgot Password" : isLogin ? "Login" : isChangePassword? "Change Password" : "Sign Up"}
              </Text>
              {Error && <Alert.Root status="error">
                <Alert.Indicator /><Alert.Title>{Error}</Alert.Title>
              </Alert.Root>}
              {Message && <Alert.Root status="success">
                <Alert.Indicator /><Alert.Title>{Message}</Alert.Title>
              </Alert.Root>}

            {!isChangePassword &&(
                <Field id="email" label="Email">
                  <Input
                    type="email"
                    border="1px solid"
                    borderColor="gray.200"
                    _dark={{borderColor: "gray.600" }}
                    _focus={{
                      boxShadow: "0 0 0 3px rgba(70, 130, 180, 1)"
                    }}
                    placeholder="Enter your email"
                    {...register("email")}
                  />
                  {errors.email && <Text color="red.500">{String(errors.email.message)}</Text>}
                </Field>
              )}

              {/* Password Field */}
              {!isForgotPassword && !isChangePassword && (
                <Field id="password" label="Password">
                  <Input
                    type="password"
                    placeholder="Enter your password"
                    size={{ base: "md", md: "lg" }}
                    border="1px solid"
                    borderColor="gray.200"
                    _dark={{borderColor: "gray.600" }}
                    _focus={{
                      boxShadow: "0 0 0 3px rgba(70, 130, 180, 1)"
                    }}
                    _hover={{ borderColor: "gray.300" }}
                    transition="all 0.2s"
                    {...register("password")}
                  />
                  {errors.password && (
                    <Text color="red.500" fontSize={{ base: "xs", md: "sm" }} mt={2}>
                      {String(errors.password.message)}
                    </Text>
                  )}
                </Field>
              )}

              {/* Confirm Password Field for Sign Up */}
              {!isLogin && !isForgotPassword && !isChangePassword && (
                <Field id="confirm-password" label="Confirm Password">
                  <Input
                    type="password"
                    placeholder="Confirm your password"
                    size={{ base: "md", md: "lg" }}
                    borderRadius="xl"
                    border="1px solid"
                    borderColor="gray.200"
                    _dark={{ bg: "gray.700", borderColor: "gray.600" }}
                    _focus={{
                      boxShadow: "0 0 0 3px rgba(70, 130, 180, 1)"
        
                    }}
                    _hover={{ borderColor: "gray.300" }}
                    transition="all 0.2s"
                    {...register("confirmPassword")}
                  />
                  {errors.confirmPassword && (
                    <Text color="red.500" fontSize={{ base: "xs", md: "sm" }} mt={2}>
                      {String(errors.confirmPassword.message)}
                    </Text>
                  )}
                </Field>
              )}

              {/* Change Password Fields */}
              {isChangePassword && (
                <>
                  <Field id="newPassword" label="New Password">
                    <Input
                      type="password"
                      placeholder="Enter new password"
                      size={{ base: "md", md: "lg" }}
                      borderRadius="xl"
                      border="1px solid"
                      borderColor="gray.200"
                      _dark={{ bg: "gray.700", borderColor: "gray.600" }}
                      _focus={{
                        boxShadow: "0 0 0 3px rgba(70, 130, 180, 1)"
                      }}
                      _hover={{ borderColor: "gray.300" }}
                      transition="all 0.2s"
                      {...register("newPassword")}
                    />
                    {errors.newPassword && (
                      <Text color="red.500" fontSize={{ base: "xs", md: "sm" }} mt={2}>
                        {String(errors.newPassword.message)}
                      </Text>
                    )}
                  </Field>

                  <Field id="confirmPassword" label="Confirm Password">
                    <Input
                      type="password"
                      placeholder="Confirm new password"
                      size={{ base: "md", md: "lg" }}
                      borderRadius="xl"
                      border="1px solid"
                      borderColor="gray.200"
                      _dark={{ bg: "gray.700", borderColor: "gray.600" }}
                      _focus={{
                        boxShadow: "0 0 0 3px rgba(70, 130, 180, 1)"
                      }}
                      _hover={{ borderColor: "gray.300" }}
                      transition="all 0.2s"
                      {...register("confirmPassword")}
                    />
                    {errors.confirmPassword && (
                      <Text color="red.500" fontSize={{ base: "xs", md: "sm" }} mt={2}>
                        {String(errors.confirmPassword.message)}
                      </Text>
                    )}
                  </Field>
                </>
              )}

              {/* Submit Button */}
              <Button
                width="full"
                py={{ base: 6, md: 7 }}
                borderRadius="sm"
                fontSize={{ base: "md", md: "lg" }}
                fontWeight="bold"
                bgGradient="linear(135deg, pink.400, purple.500)"
                _hover={{
                  transform: "translateY(-2px)",
                  boxShadow: "0 10px 25px rgba(70, 130, 180, 1)"
                }}
                _active={{ transform: "translateY(0)" }}
                disabled={isLoading}
                onClick={handleSubmit(onSubmit)}
                transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: "-100%",
                  width: "100%",
                  height: "100%",
                  transition: "left 0.5s",
                }}
                cursor={isLoading ? "not-allowed" : "pointer"}
               
              >
                {isLoading ? (
                  <HStack gap={2}>
                    <Spinner size="sm" color="red.600" />
                    <Text>Processing...</Text>
                  </HStack>
                ) : (
                  isLogin ? "Sign In" : isForgotPassword ? "Reset Password" : isChangePassword ? "Change Password" : "Create Account"
                )}
              </Button>

              {/* Footer Links */}
              <VStack gap={3} pt={4}>
                {isLogin && !isForgotPassword && !isChangePassword && (
                  <Text
                    textAlign="center"
                    fontSize="sm"
                    color="cyan.500"
                    _dark={{ color: "cyan.500" }}
                    cursor="pointer"
                    onClick={handleForgotPassword}
                    _hover={{
                      color: "blue.500",
                      transform: "translateY(-1px)"
                    }}
                    transition="all 0.2s"
                    fontWeight="medium"
                  >
                    Forgot your password?
                  </Text>
                )}

                {isForgotPassword && !isChangePassword && (
                  <Text
                    textAlign="center"
                    fontSize="sm"
                    color="gray.600"
                    _dark={{ color: "gray.400" }}
                    cursor="pointer"
                    onClick={handleBackToLogin}
                    _hover={{
                      color: "pink.500",
                      transform: "translateY(-1px)"
                    }}
                    transition="all 0.2s"
                    fontWeight="medium"
                  >
                    ← Back to Sign In
                  </Text>
                )}

                {isChangePassword && (
                  <Text
                    textAlign="center"
                    fontSize="sm"
                    color="gray.600"
                    _dark={{ color: "gray.400" }}
                    cursor="pointer"
                    onClick={handleBackToLogin}
                    _hover={{
                      color: "pink.500",
                      transform: "translateY(-1px)"
                    }}
                    transition="all 0.2s"
                    fontWeight="medium"
                  >
                    ← Back to Sign In
                  </Text>
                )}

                {/* Divider */}
                <Box w="full" h="1px" bg="gray.200" _dark={{ bg: "gray.700" }} />

                {/* Footer Text */}
                <Text
                  textAlign="center"
                  fontSize="xs"
                  color="gray.500"
                  _dark={{ color: "gray.500" }}
                >
                  Secure • Reliable • Professional
                </Text>
              </VStack>
            </VStack>
          </Box>
        </Box>
      </Flex>
    </>
  );
}
