


export interface ApiResponse<T> {
  data: T | T[];
  token?: string;
  metadata?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// shop.ts
export interface Shop {
    shop_id: string;
    shop_name: string;
    shop_owner_id: string;
    shop_owner_names: string;
    shop_email: string[];
    shop_website?: string;
    shop_phone: string[];
    shop_location_name: string;
    shop_location_address?: string;
  }
  
  // product.ts
  export interface Product {
    item_id: string;
    item_name: string;
    item_selling: number;
    item_quantity: number;
    item_availability: boolean;
    item_pic_url: string[];
    item_cat_id: string;
    item_disc_id: string;
    item_model_no: string;
    item_description: string;
    item_buying: number;
    item_sub_cat:string;
  }
  
  // transaction.ts
  export interface Transaction {
    trans_id: any;
    transaction_id: string;
    transaction_status: string;
    transaction_total: number;
    transaction_receipt_id: string;
    transaction_quantity: number;
    transaction_tax: number;
    transaction_net: number;
    transaction_discount: number;
    shop_id: string;
  }

  export interface Payment_Method {
    payment_method_id: string;
    payment_method_name: string;
    payment_method_description: string;
    payment_number:string;
    status:any;
    shop_id: string;
  }
  export interface Payment_Method_Trans {
    payment_transaction_id: string;
    payment_order_id: string;
    payment_method_id: number;
    payment_amount:number;
    shop_id: string;
  }
  // order.ts
  export interface Order {
    order_id: string;
    order_item: string;
    order_email: string;
    order_phone: string;
    order_address: string;
    order_receipt_id: string;
    order_delivery_time: string;
    order_trans_id: string;
    order_shop_id: string;
  }
  
  // receipt.ts
  export interface Receipt {
    receipt_id: string;
    receipt_item: string;
    receipt_item_id: string;
    receipt_quantity: number;
    receipt_each: number;
    receipt_total: number;
    receipt_tax: number;
    receipt_net: number;
    receipt_shop_id: string;
  }
  
  // category.ts
  export interface Category {
    category_id: string;
    sub_category:string[];
    category_name: string;
    shop_id: string;
  }

  
  // discount.ts
  export interface Discount {
    discount_id: string;
    discount_name: string;
    shop_id: string;
    discount_type: string;
    discount_amount: number;
  }
  
  export interface Status {
    status_id: string;
    status_name: string;
    status_description: string;
  }


  export interface User {
    currentshop: any;
    user_id: string;
    username: string;
    email: string;
    password: string;
    status?: boolean; 
    is_verified?: boolean; 
    first_names?: string;
    identification?: string; 
    last_names?: string;
    roles: [];
    shops: [];
  }
  
  export interface Role {
    role_id: string;
    role_name: string;
    role_status?: string;
  }
  