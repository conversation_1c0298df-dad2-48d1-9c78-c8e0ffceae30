
import { genUploader } from "uploadthing/client";
import type { ourFileRouter } from "@/app/api/uploadthing/core"; 

const { uploadFiles: uploadThingUploadFiles } = genUploader<typeof ourFileRouter>();

export async function uploadFiles(files: File[], endpoint: "imageUploader") {
  if (!files?.length) throw new Error("No files provided");

  const processedFiles = files.map((file) => {
    if (file.type.startsWith('image/')) {
      return new File([file], `${file.name.split('.')[0]}.webp`, {
        type: 'image/webp',
      });
    }
    return file;
  });

  const uploadedFiles = await uploadThingUploadFiles(endpoint, {
    files: processedFiles,
  });

  if (!uploadedFiles) {
    throw new Error("Upload failed");
  }

  return uploadedFiles.map((file) => file.ufsUrl);
}

