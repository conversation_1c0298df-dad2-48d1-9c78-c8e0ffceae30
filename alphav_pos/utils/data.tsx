import { Box, SimpleGrid,VStack, Icon,HStack, Text, Stack, Flex,Card} from '@chakra-ui/react'
import { FcAssistant, FcDonate, FcInTransit,FcCloth,FcCloseUpMode,FcLike } from 'react-icons/fc'
// import { Image } from "@chakra-ui/react"
import Image  from "next/image"
import {useColorModeValue} from "@/components/ui/color-mode"



export const featurespage3 = [
    {
      icon: "/6.png",
      title: 'Expectant Mothers',
      text: 'upport and care tailored for expectant mothers during this special phase. From essential products to helpful guides, we ensure comfort and preparation for the journey ahead.',
    },
    {
      icon:  "/4.png",
      title: 'New Borns',
      text: 'Designed with the tiniest ones in mind, offering essentials for newborn care including gentle fabrics, soothing products, and items that prioritize their safety and comfort.',
    },
    {
      icon:  "/34.png",
      title: 'Toddlers',
      text: 'Products made for curious explorers, providing durable and safe toys, clothing, and accessories to support their developmental milestones and growing independence.',
    },
    {
    icon:  "/35.png",
    title: 'Children',
    text: 'Fun and functional solutions for active children, from educational toys to comfortable clothing that keeps up with their endless energy and creativity.',
    },
    {
    icon:  "/1.png",
    title: 'Pre-teens',
    text: 'Stylish and practical products for pre-teens, blending fun designs with age-appropriate features to cater to their evolving tastes and needs.',
    },
    {
        icon:  "/9.png",
        title: 'Teens',
        text: 'Modern and trendy products for teenagers, focusing on self-expression and individuality, while ensuring quality and functionality for their dynamic lifestyle.',
    },
  //   {
  //     icon:  "/5.png",
  //     title: 'Donations',
  //     text: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore...',
  // },
  ];
  
  export const featurespage4 = [
    {icon:  "/20.png", title: 'Baby care package'},
    {icon:  "/23.png", title: 'New born accessories'},
    {icon:  "/30.png", title: 'Martenity Package'},
    {icon:  "/31.png",title: 'Baby Training'},
    {icon:  "/21.png", title: 'Baby Feeding Accessories'},
    {icon:  "/22.png",title:  'Beds & Cabinets'},
    {icon:  "/28.png", title: 'Birthday Wear'},
    {icon:  "/14.png",title: 'Baby Walker & Strollers & Car Sets'},
    {icon:  "/26.png",title: 'Back to school'},
    {icon:  "/15.png", title: 'Kids Toys'},
    {icon:  "/5.png", title: 'Donations'},
    {icon:  "/6.png", title: 'Custom oders'},


  ];

export var ourstory ="At Alpha's, we believe that style knows no age, and every little one deserves to stand out. Our story started with a simple mission – to create a world where tiny humans can dress just as boldly and beautifully as grown-ups, with clothes that celebrate their personality, creativity, and spirit. We know that every little giggle, every playful twirl, and every tiny step matters."

export var header_note ="Creating Childhood memories, One Outfit at a Time"

export const features_page2 = [
  {
    icon: <FcCloth/>,
    iconBg:'red.400',
    text: 'New Born Accessories',
  },

  {
    icon:<FcCloth/>,
    iconBg:'pink.400',
    text: 'Kids Toys',
  },
  {
    icon:<FcCloth/>,
    iconBg:'purple.400',
    text: 'Clothing & Shoes',
  },
  {
    icon: <FcCloth/>,
    iconBg:'red.400',
    text: 'Furniture & Custom Items',
  },
  {
    icon: <FcCloth/>,
    iconBg:'red.400',
    text: 'Car seats and Stollers',
  },
  {
    icon: <FcCloth/>,
    iconBg:'red.400',
    text: 'Feeding & Bathing Accessories',
  },
  {
    icon: <FcCloth/>,
    iconBg:'red.400',
    text: 'Baby Care packages',
  },
  // {
  //   icon: <FcCloth/>,
  //   iconBg:'pink.400',
  //   text: 'Toilet Trainning',
  // },

];

import { motion } from "motion/react"

export const variants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: { staggerChildren: 0.3 },
  },
};



export const images ={
  hidden:{opacity:0,x:30},
  show:{opacity:1,x:0,transition:{duration:1,}}
}

export const images2 ={
  hidden:{opacity:0,y:30},
  show:{opacity:1,x:0,transition:{duration:1,}}
}


export const fromRight = {
  hidden: { opacity: 0, x: 30 },
  show: {
    opacity: 1,
    x: 0,
    transition: { duration: 1 },
  },
};

export const fromLeft = {
  hidden: { opacity: 0, x: -30 },
  show: {
    opacity: 1,
    x: 0,
    transition: { duration: 1 },
  },
};

export const MotionStack = motion.create(Stack);
export const MotionImage = motion.create(Image);
export const MotionSimpleGrid = motion.create(SimpleGrid);
export const MotionFlex = motion.create(Flex);
export const MotionBox = motion.create(Box);
export const MotionVStack = motion.create(VStack);
export const MotionHStack = motion.create(HStack);
export const MotionCard= motion.create(Card.Root);



export const Ximages = [
  `p6.png`,
  'p9.png',
  'p10.png',
  'p3.png',
  `p1.png`,
  `p2.png`,
  `p3.png`,
  `p4.png`,
  // `p5.png`,
  `p7.png`,
  `p11.png`
];

export const phonex ='**********'
export const phonex2 ='**********'


import { Caveat } from 'next/font/google';
export const caveat = Caveat({subsets: ['latin'],style: 'normal'});