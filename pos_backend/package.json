{"name": "pos_backend", "version": "1.0.0", "description": "pos_backend", "main": "app.js", "scripts": {"test": "nodemon", "start": "node app.js", "dev": "nodemon app.js", "postinstall": "npm install pg"}, "author": "cyril mugada", "license": "ISC", "dependencies": {"axios": "^1.7.9", "backblaze-b2": "^1.7.0", "base-64": "^1.0.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cloudinary": "^2.6.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-flash": "^0.0.2", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "iron-session": "^8.0.4", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "node-cron": "^4.1.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "nvm": "^0.0.4", "pdfkit": "^0.17.1", "pg": "^8.15.6", "pg-hstore": "^2.3.4", "rimraf": "^6.0.1", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "sharp": "^0.33.5", "streamifier": "^0.1.1", "toposort-class": "^1.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "nodemon": "^3.1.9"}, "packageManager": "pnpm@10.6.4+sha512.da3d715bfd22a9a105e6e8088cfc7826699332ded60c423b14ec613a185f1602206702ff0fe4c438cb15c979081ce4cb02568e364b15174503a63c7a8e2a5f6c"}