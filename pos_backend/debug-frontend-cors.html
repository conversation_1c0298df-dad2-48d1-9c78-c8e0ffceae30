<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Debug Test</title>
</head>
<body>
    <h1>CORS Debug Test for alphav-pos.vercel.app</h1>
    <div id="results"></div>
    
    <script>
        const API_BASE = 'https://pos-backend-five.vercel.app';
        const resultsDiv = document.getElementById('results');
        
        function log(message, isError = false) {
            const p = document.createElement('p');
            p.textContent = message;
            p.style.color = isError ? 'red' : 'green';
            resultsDiv.appendChild(p);
            console.log(message);
        }
        
        // Test 1: Simple health check
        async function testHealthCheck() {
            log('🔍 Testing health check...');
            try {
                const response = await fetch(`${API_BASE}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Health check passed: ${JSON.stringify(data)}`);
                } else {
                    log(`❌ Health check failed: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                log(`❌ Health check error: ${error.message}`, true);
            }
        }
        
        // Test 2: Transaction endpoint (your problematic one)
        async function testTransaction() {
            log('🔍 Testing transaction endpoint...');
            try {
                const response = await fetch(`${API_BASE}/api/transaction/TRANS0050?shop_id=SH0002`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-user-id': 'b277e915-04d9-467d-9922-c15792bccd31',
                        'x-shop-id': 'SH0002'
                    },
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Transaction endpoint passed: ${JSON.stringify(data).substring(0, 100)}...`);
                } else {
                    log(`❌ Transaction endpoint failed: ${response.status} ${response.statusText}`, true);
                    const text = await response.text();
                    log(`Response: ${text.substring(0, 200)}`, true);
                }
            } catch (error) {
                log(`❌ Transaction endpoint error: ${error.message}`, true);
            }
        }
        
        // Test 3: Dashboard calculations
        async function testDashboard() {
            log('🔍 Testing dashboard calculations...');
            try {
                const response = await fetch(`${API_BASE}/api/cal_dash?query[shop_id]=SH0002&shop_id=SH0002`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-user-id': 'b277e915-04d9-467d-9922-c15792bccd31',
                        'x-shop-id': 'SH0002'
                    },
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ Dashboard endpoint passed: ${JSON.stringify(data).substring(0, 100)}...`);
                } else {
                    log(`❌ Dashboard endpoint failed: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                log(`❌ Dashboard endpoint error: ${error.message}`, true);
            }
        }
        
        // Test 4: Preflight check
        async function testPreflight() {
            log('🔍 Testing preflight request...');
            try {
                const response = await fetch(`${API_BASE}/api/transaction/TRANS0050`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'https://alphav-pos.vercel.app',
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'content-type,x-user-id,x-shop-id'
                    }
                });
                
                log(`Preflight response: ${response.status}`);
                log(`CORS headers: ${response.headers.get('Access-Control-Allow-Origin')}`);
                
                if (response.ok) {
                    log(`✅ Preflight passed`);
                } else {
                    log(`❌ Preflight failed: ${response.status}`, true);
                }
            } catch (error) {
                log(`❌ Preflight error: ${error.message}`, true);
            }
        }
        
        // Run all tests
        async function runAllTests() {
            log('🚀 Starting CORS debug tests from browser...');
            log(`Current origin: ${window.location.origin}`);
            
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testPreflight();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTransaction();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDashboard();
            
            log('🏁 All tests completed');
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', runAllTests);
    </script>
</body>
</html>
