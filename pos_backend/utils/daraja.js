
const moment = require("moment");
const axios = require('axios')
const base64 = require('base-64');
const { Pool } = require("pg");
require('dotenv').config();

const pool = new Pool({
    user: process.env.POSTGRES_USER,
    host: process.env.POSTGRES_HOST,
    database: process.env.POSTGRES_DATABASE,
    password:process.env.POSTGRES_PASSWORD,
    port: process.env.POSTGRES_PORT,
});

const getAccessToken = async () => {
    const client = await pool.connect();
    try {
        const result = await client.query("SELECT token, expires_at FROM access_tokens ORDER BY id DESC LIMIT 1");
        if (result.rows.length > 0) {
            const { token, expires_at } = result.rows[0];
            if (moment().isBefore(moment(expires_at))) {
                return token;
            }
        }
        const response = await axios.get(process.env.ACCESS_URL, {
            headers: {Authorization:process.env.GET_TOKEN_DARAJA_AUTH,},
        });
        const newToken = response.data.access_token;
        const expiresAt = moment().add(1, "hour").toISOString();
        await client.query("DELETE FROM access_tokens");
        await client.query("INSERT INTO access_tokens (token, expires_at) VALUES ($1, $2)", [newToken, expiresAt]);
        return newToken;
    } finally {
        client.release();
    }
};


exports.checkTransactionStatus = async (transactionId) => {
  const timestamp = moment().format("YYYYMMDDHHmmss");
  const requestData = {
    Initiator: process.env.INITIATOR_NAME,
    SecurityCredential: process.env.SECURITY_CREDENTIAL,
    CommandID: "TransactionStatusQuery",
    TransactionID: transactionId,
    PartyA: businessShortCode,
    IdentifierType: "4",
    ResultURL: process.env.TRANSACTION_STATUS_RESULT_URL,
    QueueTimeOutURL: process.env.TRANSACTION_STATUS_TIMEOUT_URL,
    Remarks: "Transaction Status Query",
    Occasion: "Verify Transaction",
  };
  try {
    const accessToken = await getAccessToken();
    const response = await axios.post(process.env.TRANS_STATUS_URL, requestData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return { success: true, data: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data || error.message };
  }
};

exports.initiateSTKPush = async (phone, amount, accountReference,businessShortCode,transactionType) => {
  const timestamp = moment().format("YYYYMMDDHHmmss");
  const password = base64.encode(`${businessShortCode}${process.env.STK_PASSKEY}${timestamp}`);
  const requestData = {
    BusinessShortCode: businessShortCode,
    Password: password,
    Timestamp: timestamp,
    TransactionType: transactionType,
    Amount: amount,
    PartyA: phone,
    PartyB: businessShortCode,
    PhoneNumber: phone,
    CallBackURL: process.env.STK_CALLBACK_URL,
    AccountReference: accountReference,
    TransactionDesc: "Payment",
  };
  try {
    const accessToken = await getAccessToken();
    const response = await axios.post(process.env.STK_PUSH_URL, requestData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return { success: true, data: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data || error.message };
  }
};

exports.initiateConfriamtion = async (ShortCode) => {
  const requestData = {
    ShortCode:ShortCode,
    ResponseType:"Completed",
    ConfirmationURL:process.env.CONFRIM_DOMAIN_URL,
    ValidationURL:process.env.VALIDATE_DOMAIN_URL,
  };
  try {
    const accessToken = await getAccessToken();
    const response = await axios.post(process.env.C2B_PUSH_URL, requestData, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return { success: true, data: response.data };
  } catch (error) {
    return { success: false, error: error.response?.data || error.message };
  }
};

