// utils/session.js
const  { sealData, unsealData } = require("iron-session");

const sessionSecret = process.env.SESSION_SECRET;
if (!sessionSecret) {
  throw new Error("SESSION_SECRET environment variable is required");
}

exports.createSession= async (data) => {
  return await sealData(data, {
    password: sessionSecret,
    ttl: 60 * 60 * 24 * 7,
  });
}

exports.getSession = async (cookie) => {
  return await unsealData(cookie, {
    password: sessionSecret,
  });
}