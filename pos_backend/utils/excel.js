const XLSX = require('xlsx');

const generateExcel = (headers, data) => {
  const worksheet = XLSX.utils.json_to_sheet(data, {
    header: headers
  });

  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

  // Add styles
  const style = {
    font: { bold: true },
    fill: { fgColor: { rgb: 'FFFF00' } },
    border: { 
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    }
  };

  // Apply styles to header row
  const range = XLSX.utils.decode_range(worksheet['!ref']);
  for (let C = range.s.c; C <= range.e.c; ++C) {
    const address = XLSX.utils.encode_cell({ r: 0, c: C });
    if (worksheet[address]) {
      worksheet[address].s = style;
    }
  }

  // Auto-fit columns
  worksheet['!cols'] = headers.map(() => ({ wch: 20 }));

  return XLSX.write(workbook, { 
    type: 'buffer',
    bookType: 'xlsx',
    compression: true
  });
};

module.exports = { generateExcel };
