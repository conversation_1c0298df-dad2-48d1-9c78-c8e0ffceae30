const { CustomerFeedback } = require('../../db/models/customerFeedback');
const genericController = require('./genericController');

exports.getAllCategories = genericController.getAll(CustomerFeedback);
exports.getCustomerFeedbackById = genericController.getById(CustomerFeedback, 'customer_feedback_id');
exports.createCustomerFeedback = genericController.create(CustomerFeedback);
exports.updateCustomerFeedback = genericController.update(CustomerFeedback, 'customer_feedback_id');
exports.deleteCustomerFeedback = genericController.delete(CustomerFeedback, 'customer_feedback_id');