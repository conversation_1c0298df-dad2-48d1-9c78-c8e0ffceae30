const { Item, Transaction,SupplySale, Receipt, Sequelize, sequelize } = require('../../db/models');
const { Op, QueryTypes, literal } = require("sequelize");

const moment = require("moment");

// ✅ Create date ranges with proper timezone handling
const todayStart = moment().startOf("day").toDate();
const todayEnd = moment().endOf("day").toDate();
const weekStart = moment().startOf("week").toDate();
const weekEnd = moment().endOf("week").toDate();



exports.getDashboardMetrics = async (req, res) => {
    const shop_id = req.query.query?.shop_id || null;
    const whereClause = shop_id ? { shop_id } : {};
    const sevenDaysAgo = moment().subtract(7, "days").toDate();

    try {
        const [totalProducts, topSellingProducts, lowStockItems] = await Promise.all([
            Item.sum("item_quantity", { where: whereClause }),

            sequelize.query(
                `
                SELECT r.receipt_item_id, i.item_name, SUM(r.receipt_quantity) AS receipt_total
                FROM receipts r
                JOIN items i ON r.receipt_item_id = i.item_id
                WHERE r.updated_at >= :sevenDaysAgo
                ${shop_id ? "AND r.shop_id = :shop_id" : ""}
                GROUP BY r.receipt_item_id, i.item_name
                ORDER BY receipt_total DESC
                LIMIT 5
                `,
                {
                    replacements: { sevenDaysAgo, shop_id },
                    type: QueryTypes.SELECT,
                }
            ),

            Item.findAll({
                where: { ...whereClause, item_quantity: { [Op.lt]: 2 } },
                attributes: ["item_id", "item_name", "item_quantity"],
            }),
        ]);
    

        res.json({ totalProducts, topSellingProducts, lowStockItems});
    } catch (error) {
        console.error("Error fetching dashboard metrics:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.getSalesAndProductMetrics = async (req, res) => {
  const shop_id = req.query.query?.shop_id || null;
  const startDate = req.query.startDate ? new Date(req.query.startDate) : null;
  const endDate = req.query.endDate ? new Date(req.query.endDate) : null;

  const whereClause = shop_id ? { shop_id } : {};

  // ✅ Fixed: Create separate date objects to avoid mutation
  const todayStartLocal = moment().startOf("day").toDate();
  const todayEndLocal = moment().endOf("day").toDate();

  const start = startDate || todayStartLocal;
  const end = endDate || todayEndLocal;

  
  try {
    const [totalSales, totalProducts,totalSupplies] = await Promise.all([
      Transaction.sum("trans_total", {
        where: {
          trans_status: 15,
          ...whereClause,
          updated_at: { [Op.between]: [start, end] },
        },
      }).then((sum) => sum || 0),

      Item.sum("item_quantity", { where: whereClause }),
      SupplySale.sum("quantity_sold", { where: whereClause }),
    ]);

    const pieData = await getWeeklyTransactionsByType(req);

    res.json({ totalSales, totalProducts,totalSupplies,pieData});
  } catch (error) {
    console.error("Error fetching sales and product metrics:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};


exports.getTodaysTransactions = async (req, res) => {
    const shop_id = req.query.query?.shop_id || null;
    const whereClause = shop_id ? { shop_id } : {};


    try {
        const transactions = await Transaction.findAll({
            where: {
                trans_status: 15,
                ...whereClause,
                updated_at: { [Op.between]: [todayStart, todayEnd] },
            },
            attributes: ["trans_total", "updated_at"],
        });
    

        const totalsArray = transactions.map((t) => t.trans_total);
        res.json({ success: true, data: totalsArray });
    } catch (error) {
        console.error("Error fetching today's transactions:", error);
        res.status(500).json({ success: false, message: "Server error" });
    }
};
exports.getWeeklyTransactions = async (req, res) => {
    const shop_id = req.query.query?.shop_id || null;
    const whereClause = shop_id ? { shop_id } : {};


    try {
        const transactions = await Transaction.findAll({
            where: {
                trans_status: 15,
                ...whereClause,
                updated_at: { [Op.between]: [weekStart, weekEnd] },
            },
            attributes: ["trans_total", "updated_at"],
        });

        let dailyTotals = {};
        for (let i = 0; i < 7; i++) {
            dailyTotals[moment(weekStart).add(i, "days").format("YYYY-MM-DD")] = 0;
        }

        transactions.forEach((t) => {
            const day = moment(t.updated_at).format("YYYY-MM-DD");
            dailyTotals[day] += parseFloat(t.trans_total) || 0;
        });

        res.json({ success: true, data: dailyTotals });
    } catch (error) {
        console.error("Error fetching weekly transactions:", error);
        res.status(500).json({ success: false, message: "Server error" });
    }
};

var getWeeklyTransactionsByType = async (req) => {
    const shop_id = req.query.query?.shop_id || req.query.shop_id || null;
    const trans_type = req.query.trans_type || req.query.query?.trans_type || null;

    try {
        const whereClause = {
            updated_at: { [Op.between]: [weekStart, weekEnd] },
            trans_status: { [Op.in]: [15, 20, 23] }
        };
        if (shop_id) {
            whereClause.shop_id = shop_id;

        }

        if (trans_type) {
            whereClause.trans_type = trans_type.toUpperCase();
        }

        const transactions = await Transaction.findAll({
            where: whereClause,
            attributes: ["trans_id", "trans_type", "trans_total", "created_at", "trans_status"],
            order: [['created_at', 'DESC']] 
        });
   
        var result = {
            SALE: { count: 0, total: 0},
            SUPPLY: { count: 0, total: 0},
            ORDER: { count: 0, total: 0 }
        };
        transactions.forEach((transaction) => {
            const type = transaction.trans_type;
            const total = parseFloat(transaction.trans_total) || 0;
            if (result[type]) {
                result[type].count += 1;
                result[type].total += total;
            }
        });

      
        return result;

    } catch (error) {
        console.error("Error fetching weekly transactions by type:", error);
        throw error;
    }
};



