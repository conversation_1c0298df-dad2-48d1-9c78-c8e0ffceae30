const { Supplier, SupplyItem,Item, SupplySale,Transaction,Receipt,Paymenttransaction,Status} = require('../../db/models');
const { Op, Sequelize } = require('sequelize');
const { sequelize } = require('../../db/models');
const moment = require('moment');

const getTimeFrameQuery = (timeFrame) => {
    const now = moment(); // Lock now once
    let startDate, endDate;
    
    switch (timeFrame) {
        case 'daily':
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
            break;
        case 'weekly':
            startDate = now.clone().startOf('week');
            endDate = now.clone().endOf('week');
            break;
        case 'monthly':
            startDate = now.clone().startOf('month');
            endDate = now.clone().endOf('month');
            break;
        case 'yearly':
            startDate = now.clone().startOf('year');
            endDate = now.clone().endOf('year');
            break;
        default:
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
    }

    return {
        updated_at: {
            [Op.between]: [startDate.toDate(), endDate.toDate()]
        }
    };
};


// ----------- SUPPLIERS CRUD -----------

// GET suppliers (with pagination, search, shop filter)
exports.getSuppliers = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, query: { shop_id, query: searchTerm, timeFrame, startDate, endDate } = {} } = req.query;

    const offset = (page - 1) * pageSize;
    const where = { ...(shop_id && { shop_id }) };
    if (timeFrame) {
      Object.assign(where, getTimeFrameQuery(timeFrame));
    } else if (startDate && endDate) {
      where.created_at = { [Op.between]: [new Date(startDate), new Date(endDate)] };
    }

    if (searchTerm) {
      where[Op.or] = [
        { supplier_name: { [Op.iLike]: `%${searchTerm}%` } },
        { supplier_contact: { [Op.iLike]: `%${searchTerm}%` } }
      ];
    }
    const { rows, count } = await Supplier.findAndCountAll({
      where,
      limit: parseInt(pageSize),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });
    res.json({
      data: rows,
      metadata: { total: count, page: parseInt(page), pageSize: parseInt(pageSize) }
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// POST create supplier
exports.createSupplier = async (req, res) => {
  try {
    const { supplier_email, shop_id } = req.body;
    const existing = await Supplier.findOne({ where: { supplier_email, shop_id } });
    if (existing) {
      return res.status(400).json({ error: 'Supplier email already exists for this shop' });
    }
    const supplier = await Supplier.create(req.body);
    res.status(201).json(supplier);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// PUT update supplier
exports.updateSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    await Supplier.update(req.body, { where: { supplier_id: id } });
    res.json({ message: 'Supplier updated' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// DELETE supplier
exports.deleteSupplier = async (req, res) => {
  try {
    const { id } = req.params;
    await Supplier.destroy({ where: { supplier_id: id } });
    res.json({ message: 'Supplier deleted' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// ----------- SUPPLY ITEMS CRUD -----------

// GET supply items (with pagination, search, shop filter)
exports.getSupplyItems = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, query: { shop_id, query: searchTerm, timeFrame, startDate, endDate } = {} } = req.query;

    const offset = (page - 1) * pageSize;
    const where = { ...(shop_id && { shop_id }) };
    if (timeFrame) {
      where.supply_date = getTimeFrameQuery(timeFrame).updated_at;
    } else if (startDate && endDate) {
      where.supply_date = { [Op.between]: [new Date(startDate), new Date(endDate)] };
    }

    if (searchTerm) {
      where[Op.or] = [{ item_name: { [Op.iLike]: `%${searchTerm}%` } }];
    }
    const { rows, count } = await SupplyItem.findAndCountAll({
      where,
      limit: parseInt(pageSize),
      offset: parseInt(offset),
      order: [['supply_date', 'DESC']],
      include: [
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['supplier_id', 'supplier_name', 'supplier_contact', 'supplier_email', 'supplier_address', 'supplier_status', 'shop_id'],
          where: { supplier_id: { [Op.eq]: sequelize.col('SupplyItem.supplier_id') } }
        },
        {
          model: Supplier,
          as: 'destination',
          attributes: ['supplier_id', 'supplier_name', 'supplier_contact', 'supplier_email', 'supplier_address', 'supplier_status', 'shop_id'],
          where: { supplier_id: { [Op.eq]: sequelize.col('SupplyItem.destination_id') } }
        }, {
          model: Transaction,
          as:'transaction',
          attributes: ['trans_id', 'trans_total', 'trans_net', 'trans_quantity', 'trans_status', 'shop_id', 'trans_type', 'username'],
          include: [
            {
              model: Receipt,
              as:'receipts',
              include: [
                {
                  model: Item,
                  as: 'item',
                  attributes: ['item_id', 'item_quantity'],
                  where: { item_id: { [Op.eq]: sequelize.col('transaction.receipts.receipt_item_id') } }
                }
              ]
            },
            {
              model: Paymenttransaction,
              as:'payments',
              attributes: ['payment_id', 'trans_id', 'payment_method_id', 'payment_amount', 'shop_id', 'payment_response']
            },
          
          ],
          
        },
        {
          model: Status,
          as:'item_status',
          attributes: ['status_id', 'status_name']
        }
      ]
    });

    // const data = rows.map(item => ({
    //   ...item.toJSON(),
    //   available: item.quantity - (item.quantity_sold || 0)
    // }));
    const data = rows.map(item => ({
      ...item.toJSON(),
      supply_date: item.supply_date.toISOString().split('T')[0],
      available: item.quantity - (item.quantity_sold || 0)
    }));

    res.json({
      data,
      metadata: { total: count, page: parseInt(page), pageSize: parseInt(pageSize) }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};



// POST create supply item
  exports.createSupplyItem = async (req, res) => {
    const t = await sequelize.transaction();
    try {
      const { item_name,quantity,created_by,supplier_id, destination_id, shop_id, amount_expected, amount_paid, attachments, supply_date } = req.body;
      let supplyItems = [];
      let transaction;

      transaction = await Transaction.create({
        trans_total: amount_expected,
        trans_net: amount_expected,
        trans_quantity: 0,
        trans_status: 13,  
        shop_id,
        trans_type:'SUPPLY',
        income_type:'income',
        username: created_by,
      }, { transaction: t });

      if (req.body.selected_items && Array.isArray(req.body.selected_items)) {
        const totalQuantity = req.body.selected_items.reduce((sum, item) => sum + item.quantity, 0);
        await Transaction.update(
          { trans_quantity: totalQuantity },
          { where: { trans_id: transaction.trans_id },
          transaction: t }
        );
          const supplyItem = await SupplyItem.create({
            supplier_id:supplier_id,
            destination_id:destination_id,
            item_name: item_name,
            quantity: quantity,
            amount_expected: amount_expected,
            amount_paid:amount_paid?amount_paid:0,
            attachments: attachments || [],
            supply_date: supply_date || new Date(),
            shop_id,
            trans_id: transaction.trans_id,
            status: 13
          }, { transaction: t }); 
          
          supplyItems.push(supplyItem);
        for (const item of req.body.selected_items) {
          await Receipt.create({
            receipt_item: item.item_name,
            receipt_item_id: item.item_id,
            receipt_quantity: item.quantity,
            receipt_each: item.item_selling,
            original_quantity:item.quantity,
            receipt_total:0,
            receipt_tax: 0,
            receipt_net:0,
            trans_id: transaction.trans_id,
            shop_id
          }, { transaction: t });
        }
      } else {

        await Transaction.update(
          { trans_quantity: quantity },
          { where: { trans_id: transaction.trans_id },
          transaction: t  }
        );
        const supplyItem = await SupplyItem.create({
          supplier_id,
          destination_id,
          item_name,
          quantity,
          amount_expected:amount_expected*quantity,
          amount_paid,
          selling_price:amount_expected,
          attachments: attachments || [],
          supply_date: supply_date || new Date(),
          shop_id,
          status: 13
        },{ transaction: t });
        
        supplyItems.push(supplyItem);
      }

      if (amount_paid > 0) {
        await Paymenttransaction.create({
          trans_id: item.trans_id,
          payment_method_id: 'PM0015',
          payment_amount: amount_paid,
          shop_id,
          payment_response: { status: "success", message: "Payment Processed", amount: amount_paid }
        }, { transaction: t });
      }
      await t.commit();
      res.status(201).json({
        transaction,
        supplyItems
      });
    } catch (err) {
      await t.rollback();
      console.error("Error creating supply item:", err);
      res.status(400).json({ error: err.message });
    }
  };

// PUT update supply item
exports.updateSupplyItem = async (req, res) => {
  try {
    const { id } = req.params;
    var x ={
      ...req.body,
      updated_at: new Date()
    }
    await SupplyItem.update(x, { where: { supply_id: id } });
    res.json({ message: 'Supply item updated' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};



// DELETE supply item

exports.updateSupplyItemRecipt = async (req, res) => {
  const { id } = req.params;
  const { items, total_amount, is_transaction,shop_id,quantity_sold } = req.body;
  const t = await sequelize.transaction();
  try {
   const supplyItem = await SupplyItem.findOne({where: { supply_id: id }, transaction: t});

    if (!supplyItem) {
      throw new Error('Supply item not found');
    }

   if (is_transaction) {
          const paymentAmount = Math.abs(parseFloat(total_amount));
          const updatedPaid = parseFloat(supplyItem?.amount_paid || 0) + paymentAmount;
          const totalItemsQuantity = items.reduce((sum, item) => { return sum + parseFloat(item.quantity); }, 0);
          const updatedQuantity = Math.max(0, parseFloat(supplyItem.quantity) - totalItemsQuantity);
          const updatedSold = (parseFloat(supplyItem.quantity_sold) || 0) + totalItemsQuantity;
          if (totalItemsQuantity > supplyItem.quantity) {
            throw new Error('Total quantity sold cannot exceed available quantity');
          }
          await SupplyItem.update({
            amount_paid: updatedPaid,
            available: updatedQuantity,
            quantity_sold: updatedSold
          },{ where: { supply_id: id }, transaction: t });

      for (const item of items) {
        const receipt = await Receipt.findOne({
          where: { receipt_id: item.receipt_id, trans_id: supplyItem.trans_id },
          transaction: t
        });

        if (!receipt) {
          throw new Error(`Receipt ${item.receipt_id} not found`);
        }
        if (receipt && parseFloat(item.quantity) > parseFloat(receipt?.dataValues?.receipt_quantity)) {
          throw new Error(`Quantity sold (${item.quantity}) exceeds Ordered quantity (${receipt.receipt_quantity})`);
        }
        
        await Paymenttransaction.create({
          trans_id: item.trans_id,
          payment_method_id: 'PM0015',
          payment_amount: paymentAmount,
          shop_id,
          payment_response: { status: "success", message: "Payment Processed", amount: total_amount }
        }, { transaction: t });
        
          
        await Receipt.update(
          { 
            receipt_quantity: Math.max(0, (receipt.receipt_quantity || 0) - item.quantity),
            sold_quantity: (receipt.sold_quantity || 0) + item.quantity
          },
          { where: { receipt_id: receipt.receipt_id }, transaction: t }
        );
            
            const item_x = await Item.findOne({
              where: {item_id: item.receipt_item_id},
              transaction: t
            });
            if (item_x) {
              await Item.update(
                { item_quantity: item_x.item_quantity - item.quantity },
                { where: { item_id: item_x.item_id }, transaction: t }
              )
            }
            if (updatedQuantity == 0) {
              await Transaction.update(
                { trans_status: 15 , updated_at: new Date()},
                { where: { trans_id: item.trans_id }, transaction: t }
              );
            }
          
        }
    }else{
        const updatedQuantity = parseFloat(supplyItem.quantity) - parseFloat(quantity_sold);
        const updatedSold = parseFloat(supplyItem.quantity_sold || 0) + parseFloat(quantity_sold);
        const updatedPaid = parseFloat(supplyItem?.amount_paid || 0) + parseFloat(total_amount);
        
        const updateData = {
          ...req.body,
          available: updatedQuantity,
          quantity_sold: updatedSold,
          amount_paid: updatedPaid
        };
        Object.keys(updateData).forEach(key => (updateData[key] === undefined || updateData[key] === '') && delete updateData[key]);
      
        await SupplyItem.update(
          updateData,
          { where: { supply_id: id }, transaction: t }
        );

   
 }

    await t.commit();
    res.json({ message: 'Supply item updated successfully' });
  } catch (err) {
    await t.rollback();
    console.error('Error updating supply item:', err);
    res.status(400).json({error: err.message});
  }finally{
    const supplyItem = await SupplyItem.findOne({where: { supply_id: id }});

    if (supplyItem?.dataValues?.quantity_sold == supplyItem?.dataValues?.quantity) {
      if (is_transaction) {
          await Transaction.update(
            { trans_status: 15, updated_at: new Date() },
            { where: { trans_id: supplyItem?.dataValues?.trans_id }}
          );
        }else{
          await SupplyItem.update(
            { status: 15, updated_at: new Date()},
            { where: { supply_id: id }}
          );
        }
    }
  }
};





exports.deleteSupplyItem = async (req, res) => {
  try {
    const { id } = req.params;
    await SupplyItem.destroy({ where: { supply_id: id } });
    res.json({ message: 'Supply item deleted' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// ----------- SUPPLY SALES CRUD -----------

// GET supply sales (with pagination, search, shop filter)

exports.getSupplySales = async (req, res) => {
  try {
    const { page = 1, pageSize = 10, query: { shop_id, query: searchTerm, timeFrame, startDate, endDate } = {} } = req.query;

    const offset = (page - 1) * pageSize;
    const where = { ...(shop_id && { shop_id }) };
    if (timeFrame) {
      where.sale_date = getTimeFrameQuery(timeFrame).updated_at;
    } else if (startDate && endDate) {
      where.sale_date = { [Op.between]: [new Date(startDate), new Date(endDate)] };
    }

    if (searchTerm) {
      where[Op.or] = [
        { customer_name: { [Op.iLike]: `%${searchTerm}%` } },
        { item_name: { [Op.iLike]: `%${searchTerm}%` } }
      ];
    }

    const { rows, count } = await SupplySale.findAndCountAll({
      where,
      limit: parseInt(pageSize),
      offset: parseInt(offset),
      order: [['sale_date', 'DESC']],
      // include: [
      //   {
      //     model: SupplyItem,
      //     as: 'supply_item',
      //     include: [
      //       {
      //         model: Transaction,
      //         as: 'transaction',
      //         include: [
      //           {
      //             model: Receipt,
      //             as: 'receipts',
      //             include: [
      //               {
      //                 model: Item,
      //                 as: 'item'
      //               }
      //             ]
      //           }
      //         ]
      //       }
      //     ]
      //   }
      // ]
    });

    const data = rows.map(row => ({
      ...row.toJSON(),
      unit_price: row.is_transaction ? 0 : row.selling_price,
      sale_date: row.sale_date.toISOString().split('T')[0]
    }));

    res.json({
      data: data,
      metadata: { total: count, page: parseInt(page), pageSize: parseInt(pageSize) }
    });

  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

// POST create supply sale
exports.createSupplySale = async (req, res) => {
  try {
    const saleData = {
      ...req.body,
      buyer_supplier_id: req.body.buyer_supplier_id || null, 
      attachments: req.body.attachments || [] 
    };
    const supplySale = await SupplySale.create(saleData);
    // const supplySale = await SupplySale.create(req.body);
    res.status(201).json(supplySale);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// PUT update supply sale
exports.updateSupplySale = async (req, res) => {
  try {
    const { id } = req.params;
    var x ={
      ...req.body,
      updated_at: new Date()
    }
    await SupplySale.update(x, { where: { sale_id: id } });
    res.json({ message: 'Supply sale updated' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};

// DELETE supply sale
exports.deleteSupplySale = async (req, res) => {
  try {
    const { id } = req.params;
    await SupplySale.destroy({ where: { sale_id: id } });
    res.json({ message: 'Supply sale deleted' });
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
};