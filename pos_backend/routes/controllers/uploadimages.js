
// const fs = require('fs');
// const path = require('path');
// const sharp = require('sharp');
// const {rimraf} = require('rimraf');
// require("dotenv").config();

// const uploadsDir = path.join(__dirname, process.env.PIC_UPLOADS);

// if (!fs.existsSync(uploadsDir)) {
//   fs.mkdirSync(uploadsDir, { recursive: true });
// }



// // Upload Image(s)
// exports.UploadPic = async (req, res) => {
//   if (!req.files || req.files.length === 0) {
//     return res.status(400).json({ message: 'No files uploaded.' });
//   }

//   try {
//     const optimizedFiles = await Promise.all(
//       req.files.map(async (file) => {
//       const originalPath = file.path
//       const optimizedFileName = `${path.basename(originalPath, path.extname(originalPath))}-optimized.webp`;
//       const optimizedFilePath = path.join(uploadsDir, optimizedFileName);
//       await sharp(originalPath)
//         .resize(800, 800, {fit: 'inside',withoutEnlargement: true,})
//         .webp({ quality: 80 })
//         .toFile(optimizedFilePath);
//       // await fs.promises.unlink(originalPath);  
//       return optimizedFileName;
//     }));


//     res.status(200).json({
//       message: 'Images uploaded successfully!',
//       files: optimizedFiles
//     });
//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({ message: 'Internal server error.' });
//   }
// };



// exports.getPic = (req, res) => {
//   const filename = req.params.filename;
//   const filePath = path.join(uploadsDir, filename);

//   if (fs.existsSync(filePath)) {
//     const ext = path.extname(filename).toLowerCase();
//     const contentType = ext === '.webp' ? 'image/webp' : 'image/jpeg';
//     res.setHeader('Content-Type', contentType);
//     fs.createReadStream(filePath).pipe(res);
//   } else {
//     res.status(404).json({ message: 'File not found.' });
//   }
// };


// exports.deletePic = (req, res) => {
//   const filename = req.params.filename;
//   const filePath = path.join(uploadsDir, filename);

//   if (fs.existsSync(filePath)) {
//     fs.unlink(filePath, (err) => {
//       if (err) {
//         console.error(`Error deleting file: ${err.message}`);
//         return res.status(500).json({ message: 'Error deleting file.' });
//       }
//       res.status(200).json({ message: 'File deleted successfully.' });
//     });
//   } else {
//     res.status(404).json({ message: 'File not found.' });
//   }
// };

// //cloudinary
// const fs = require('fs');
// const path = require('path');
// const sharp = require('sharp');
// const cloudinary = require('cloudinary').v2;
// require('dotenv').config();

// // Configure Cloudinary
// cloudinary.config({
//   cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
//   api_key: process.env.CLOUDINARY_API_KEY,
//   api_secret: process.env.CLOUDINARY_API_SECRET,
// });

// // Upload Image(s)
// exports.UploadPic = async (req, res) => {
//   if (!req.files || req.files.length === 0) {
//     return res.status(400).json({ message: 'No files uploaded.' });
//   }
//   try {
//     const optimizedFiles = await Promise.all(
//       req.files.map(async (file) => {
//         const originalPath = file.path;
//         const optimizedImageBuffer = await sharp(originalPath)
//           .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
//           .webp({ quality: 80 })
//           .toBuffer();
//         const cloudinaryResponse = await new Promise((resolve, reject) => {
//           cloudinary.uploader.upload_stream(
//             { folder: 'uploads', resource_type: 'auto' },
//             (error, result) => {
//               if (error) {
//                 reject(error);
//               } else {
//                 resolve(result);
//               }
//             }
//           ).end(optimizedImageBuffer);
//         });
//         await fs.promises.unlink(originalPath);

//         return cloudinaryResponse.secure_url;
//       })
//     );

//     res.status(200).json({
//       message: 'Images uploaded successfully!',
//       files: optimizedFiles,
//     });
//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({ message: 'Internal server error.' });
//   }
// };

// // Get Image from Cloudinary
// exports.getPic = (req, res) => {
//   const filename = req.params.filename;
//   const publicId = `uploads/${path.basename(filename, path.extname(filename))}`;
//   const cloudinaryUrl = cloudinary.url(publicId, { secure: true });
//   res.redirect(cloudinaryUrl);
// };

// // Delete Image from Cloudinary
// exports.deletePic = async (req, res) => {
//   const filename = req.params.filename;
//   const publicId = `uploads/${path.basename(filename, path.extname(filename))}`;

//   try {
//     const result = await cloudinary.uploader.destroy(publicId);
//     if (result.result === 'ok') {
//       res.status(200).json({ message: 'File deleted successfully.' });
//     } else {
//       res.status(404).json({ message: 'File not found.' });
//     }
//   } catch (error) {
//     console.error('Error deleting file:', error);
//     res.status(500).json({ message: 'Error deleting file.' });
//   }
// };





//blackblaze

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const B2 = require('backblaze-b2');
const axios =require('axios');
require('dotenv').config();
const cache = new Map(); 
// const uploadthing = require('uploadthing');

const b2 = new B2({
  applicationKeyId: process.env.B2_APPLICATION_KEY_ID,
  applicationKey: process.env.B2_APPLICATION_KEY,
});


exports.UploadPic = async (req, res) => {
  if (!req.files || req.files.length === 0) {
    return res.status(400).json({ message: 'No files uploaded.' });
  }
    

  try {
    await b2.authorize();
    const optimizedFiles = await Promise.all(
      req.files.map(async (file) => {
        if (!file.buffer) {
          throw new Error('Invalid file buffer');
        }
        let uploadBuffer, uploadExtension, uploadMime;
        let isImage = /^image\//.test(file.mimetype);
        if (isImage) {
            uploadBuffer = await sharp(file.buffer)
              .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
              .webp({ quality: 80 })
              .toBuffer();
            uploadExtension = 'webp';
            uploadMime = 'image/webp';
          } else {
            uploadBuffer = file.buffer;
            uploadExtension = path.extname(file.originalname).replace('.', '') || 'bin';
            uploadMime = file.mimetype;
          }
        const optimizedFileName = `${Date.now()}-${Math.round(Math.random() * 1e9)}.${uploadExtension}`;
        const uploadUrlResponse = await b2.getUploadUrl({
          bucketId: process.env.B2_BUCKET_ID,
        });
        await b2.uploadFile({
          uploadUrl: uploadUrlResponse.data.uploadUrl,
          uploadAuthToken: uploadUrlResponse.data.authorizationToken,
          fileName: `uploads/${optimizedFileName}`,
          data: uploadBuffer,
          mime: uploadMime
        });
        return optimizedFileName;
      })
    );

    res.status(200).json({
      message: 'Images uploaded successfully!',
      files: optimizedFiles,
    });
  } catch (error) {
    console.error('Error processing files:', error);
    res.status(500).json({ message: 'Internal server error.' });
  }
};



// exports.UploadPic = async (req, res) => {
//   if (!req.files || req.files.length === 0) {
//     return res.status(400).json({ message: 'No files uploaded.' });
//   }

//   try {
//     const optimizedFiles = await Promise.all(
//       req.files.map(async (file) => {
//         if (!file.buffer) {
//           throw new Error('Invalid file buffer');
//         }
//         let uploadBuffer, uploadExtension, uploadMime;
//         let isImage = /^image\//.test(file.mimetype);
//         if (isImage) {
//           uploadBuffer = await sharp(file.buffer)
//             .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
//             .webp({ quality: 80 })
//             .toBuffer();
//           uploadExtension = 'webp';
//           uploadMime = 'image/webp';
//         } else {
//           uploadBuffer = file.buffer;
//           uploadExtension = path.extname(file.originalname).replace('.', '') || 'bin';
//           uploadMime = file.mimetype;
//         }

//         const uploadResponse = await uploadthing.upload({
//           file: uploadBuffer,
//           fileName: `${Date.now()}-${Math.round(Math.random() * 1e9)}.${uploadExtension}`,
//           mimeType: uploadMime,
//         });

//         return uploadResponse.url;
//       })
//     );

//     res.status(200).json({
//       message: 'Images uploaded successfully!',
//       files: optimizedFiles,
//     });
//   } catch (error) {
//     console.error('Error processing files:', error);
//     res.status(500).json({ message: 'Internal server error.' });
//   }
// };




exports.getPic = async (req, res) => {
  const filename = req.params.filename;
  if (cache.has(filename)) {
    const signedUrl = cache.get(filename);
    const imageResponse = await axios.get(signedUrl, { responseType: 'stream' });
    imageResponse.data.pipe(res);
    return;
  }
  try {
    await b2.authorize();
    const signedUrlResponse = await b2.getDownloadAuthorization({
      bucketId: process.env.B2_BUCKET_ID,
      fileNamePrefix: `uploads/${filename}`,
      validDurationInSeconds: 3600,
    });
    const signedUrl = `https://f005.backblazeb2.com/file/${process.env.B2_BUCKET_NAME}/uploads/${filename}?Authorization=${signedUrlResponse.data.authorizationToken}`;
    cache.set(filename, signedUrl);
    const imageResponse = await axios.get(signedUrl, { responseType: 'stream' });
    res.setHeader('Content-Type', imageResponse.headers['content-type']);
    res.setHeader('Content-Length', imageResponse.headers['content-length']);
    imageResponse.data.pipe(res);
    return imageResponse
  } catch (error) {
    console.error('Error generating signed URL:', error);
    res.status(500).json({ message: 'Error retrieving file.' });
  }
};



exports.deletePic = async (req, res) => {
  const filename = req.params.filename;
  const filePath = `uploads/${filename}`;

  try {
    await b2.authorize();
    const listFilesResponse = await b2.listFileNames({
      bucketId: process.env.B2_BUCKET_ID,
      startFileName: filePath,
      maxFileCount: 1,
    });

    if (listFilesResponse.data.files.length === 0) {
      return res.status(404).json({ message: 'File not found.' });
    }

    const fileId = listFilesResponse.data.files[0].fileId;
    const deleteResponse = await b2.deleteFileVersion({
      fileId: fileId,
      fileName: filePath,
    });

    if (deleteResponse.status === 200) {
      res.status(200).json({ message: 'File deleted successfully.' });
    } else {
      res.status(404).json({ message: 'File not found.' });
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    res.status(500).json({ message: 'Error deleting file.' });
  }
};