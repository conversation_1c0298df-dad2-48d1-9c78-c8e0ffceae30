exports.getAll = (Model) => async (req, res) => {
    try {
      const data = await Model.findAll();
      const total = await Model.count();
      res.status(200).json({
        data: data,
        metadata: {
          total
        },
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  
  exports.getById = (Model, primaryKeyField) => async (req, res) => {
    try {
      const { id } = req.params;
      const data = await Model.findByPk(id);
      const total = await Model.count();
      if (data) {

        res.status(200).json({
          data: data,
          metadata: {
            total
          },
        });
      } else {
        res.status(404).json({ error: `${primaryKey<PERSON>ield} not found` });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  
  exports.create = (Model) => async (req, res) => {
    try {
      const newData = await Model.create(req.body);
      res.status(201).json(newData);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  

  exports.createMany = (Model) => async (req, res) => {
    try {
      const dataArray = Array.isArray(req.body) ? req.body : [req.body];
      const newData = await Model.bulkCreate(dataArray);
      res.status(201).json(newData);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: error.message });
    }
  };
  
  exports.updateMany = (Model, primaryKeyField) => async (req, res) => {
    try {
      const dataArray = Array.isArray(req.body) ? req.body : [req.body];
      const results = await Promise.all(
        dataArray.map(async (data) => {
          if (!data[primaryKeyField]) {
            throw new Error(`Missing ${primaryKeyField} in request body`);
          }
          const [updated] = await Model.update(data, { where: { [primaryKeyField]: data[primaryKeyField] } });
          if (updated) {
            return Model.findByPk(data[primaryKeyField]); // Return updated record
          } else {
            return { error: `${primaryKeyField} ${data[primaryKeyField]} not found` };
          }
        })
      );
      res.status(200).json(results);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: error.message });
    }
  };
  
  
  exports.update = (Model, primaryKeyField) => async (req, res) => {
    try {
      const { id } = req.params;
      const [updated] = await Model.update(req.body, { where: { [primaryKeyField]: id } });
      if (updated) {
        const updatedData = await Model.findByPk(id);
        res.status(200).json(updatedData);
      } else {
        res.status(404).json({ error: `${primaryKeyField} not found` });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  
  exports.delete = (Model, primaryKeyField) => async (req, res) => {
    try {
      const { id } = req.params;
      const deleted = await Model.destroy({ where: { [primaryKeyField]: id } });
      if (deleted) {
        res.status(204).send();
      } else {
        res.status(404).json({ error: `${primaryKeyField} not found` });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  