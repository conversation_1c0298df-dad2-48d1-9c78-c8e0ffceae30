const {Category}  = require('../../db/models');
const genericController = require('./_genericController');

exports.getAllCategories = genericController.getAll(Category);
exports.getCategoryById = genericController.getById(Category, 'category_id');
exports.createCategory = genericController.createMany(Category);
// exports.updateCategory = genericController.updateMany(Category, 'category_id');
exports.deleteCategory = genericController.delete(Category, 'category_id');

exports.updateCategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { sub_category } = req.body;

    const category = await Category.findByPk(id);
    if (!category) {
      return res.status(404).json({ error: "Category not found" });
    }
    const existingSet = new Set(category.sub_category.map(sc => sc.trim().toLowerCase()));
    const newSubCategories = sub_category
      .map(sc => sc.trim())
      .filter(sc => !existingSet.has(sc.toLowerCase()))

    if (newSubCategories.length === 0) {
      return res.status(200).json({
        success: false,
        message: "No new sub-categories to add.",
        data: category
      });
    }

    const updatedCategory = await category.update({
      sub_category: [...category.sub_category, ...newSubCategories]
    });

    res.json({
      success: true,
      data: updatedCategory
    });
  } catch (error) {
    console.error("Update error:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

exports.getCategoryByShopId = async (req, res) => {
    try {
        const { shop_id } = req.params;
        if (!shop_id) return res.status(400).json({ error: "shop_id is required" });
        const categories = await Category.findAll({ where: { shop_id } });
        if (!categories || categories.length === 0) {
            return res.status(404).json({ error: "No categories found for this shop_id" });
        }
        res.json({
            data: categories,
            metadata: { total: categories.length }
        });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
};

exports.removeSubCategory = async (req, res) => {
    try {
        const { category_id, subCategoryIndex } = req.body.data;
        if (!category_id || subCategoryIndex == undefined) {
            return res.status(400).json({ error: "category_id and subCategoryIndex are required" });
        }
        const category = await Category.findByPk(category_id);
        if (!category) return res.status(404).json({ error: "Category not found" });

        if (subCategoryIndex < 0 || subCategoryIndex >= category.sub_category.length) {
            return res.status(400).json({ error: "Invalid subcategory index" });
        }
        const updatedSubCategories = [...category.sub_category];
        updatedSubCategories.splice(subCategoryIndex, 1);

        category.sub_category = updatedSubCategories;
        await category.save();

        res.json({ 
            success: true, 
            message: "Sub-category removed successfully", 
            data: category 
        });
    } catch (error) {
        console.error("Error removing sub-category:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};