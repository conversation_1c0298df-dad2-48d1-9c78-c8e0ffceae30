const { Transaction, Receipt, Product, Shop, FinancialTransaction } = require('../../db/models');
const { Op } = require('sequelize');
const cron = require('node-cron');

// Schedule financial aggregation every 10 minutes
cron.schedule('*/10 * * * *', async () => {
  try {
    const shops = await Shop.findAll();
    for (const shop of shops) {
      const now = new Date();
      const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);
      
      await aggregateFinancialData(shop.id, tenMinutesAgo, now);
    }
  } catch (error) {
    console.error('Error in financial aggregation cron:', error);
  }
});

const aggregateFinancialData = async (shop_id, period_start, period_end) => {
  try {
    // Get previous period data
    const previousPeriodEnd = new Date(period_start);
    const previousPeriodStart = new Date(previousPeriodEnd.getTime() - 10 * 60 * 1000);
    
    const previousData = await getFinancialData(shop_id, previousPeriodStart, previousPeriodEnd);
    const currentData = await getFinancialData(shop_id, period_start, period_end);

    // Calculate changes
    const changes = {
      total_sales_change: calculatePercentageChange(previousData.total_sales, currentData.total_sales),
      total_costs_change: calculatePercentageChange(previousData.total_costs, currentData.total_costs),
      net_profit_change: calculatePercentageChange(previousData.net_profit, currentData.net_profit),
      inventory_value_change: calculatePercentageChange(previousData.inventory_value, currentData.inventory_value)
    };

    // Save aggregated data
    await FinancialTransaction.create({
      shop_id,
      period_start,
      period_end,
      data: {
        ...currentData,
        ...changes
      }
    });
  } catch (error) {
    console.error('Error aggregating financial data:', error);
  }
};

const calculatePercentageChange = (previous, current) => {
  if (!previous || !current) return 0;
  return ((current - previous) / previous) * 100;
};

const getFinancialData = async (shop_id, period_start, period_end) => {
  try {
    // Get total sales
    const totalSales = await Transaction.sum('trans_total', {
      where: {
        shop_id,
        trans_status: 18,
        created_at: {
          [Op.between]: [period_start, period_end]
        }
      }
    });

    // Get total costs (from receipts)
    const totalCosts = await Receipt.sum('receipt_total', {
      where: {
        shop_id,
        created_at: {
          [Op.between]: [period_start, period_end]
        }
      }
    });

    // Get transactions history
    const transactions = await FinancialTransaction.findAll({
      where: {
        shop_id,
        period_start: {
          [Op.gte]: period_start
        }
      },
      order: [['period_start', 'DESC']],
      limit: 10
    });

    res.json({
      total_sales,
      total_costs,
      net_profit,
      inventory_value,
      transactions
    });
      where: {
        shop_id,
        is_active: true
      }
    });

    // Calculate profit
    const netProfit = (totalSales || 0) - (totalCosts || 0);

    return {
      total_sales: totalSales || 0,
      total_costs: totalCosts || 0,
      net_profit: netProfit,
      inventory_value: inventoryValue || 0
    };
  } catch (error) {
    console.error('Error getting financial data:', error);
    throw error;
  }
};

exports.withdraw = async (req, res) => {
  try {
    const { amount } = req.body;
    const { shop_id } = req.user;

    // Get current balance
    const currentBalance = await getFinancialData(shop_id, new Date(), new Date());

    if (amount <= 0 || amount > currentBalance.available_balance) {
      return res.status(400).json({ success: false, message: 'Invalid withdrawal amount' });
    }

    // Create withdrawal transaction
    await FinancialTransaction.create({
      shop_id,
      type: 'withdrawal',
      amount: -amount,
      description: 'Funds withdrawal'
    });

    res.json({ success: true, message: 'Withdrawal successful' });
  } catch (error) {
    console.error('Withdrawal error:', error);
    res.status(500).json({ success: false, message: 'Error processing withdrawal' });
  }
};

exports.deposit = async (req, res) => {
  try {
    const { amount } = req.body;
    const { shop_id } = req.user;

    if (amount <= 0) {
      return res.status(400).json({ success: false, message: 'Invalid deposit amount' });
    }

    // Create deposit transaction
    await FinancialTransaction.create({
      shop_id,
      type: 'deposit',
      amount,
      description: 'Funds deposit'
    });

    res.json({ success: true, message: 'Deposit successful' });
  } catch (error) {
    console.error('Deposit error:', error);
    res.status(500).json({ success: false, message: 'Error processing deposit' });
  }
};

exports.getFinancialReports = async (req, res) => {
  try {
    const { shop_id, period_start, period_end } = req.query;
    
    // Get total sales
    const totalSales = await Transaction.sum('trans_total', {
      where: {
        shop_id,
        trans_status: 18,
        created_at: {
          [Op.between]: [period_start, period_end]
        }
      }
    });

    // Get total costs (from receipts)
    const totalCosts = await Receipt.sum('receipt_total', {
      where: {
        shop_id,
        created_at: {
          [Op.between]: [period_start, period_end]
        }
      }
    });

    // Get best selling items
    const bestSellingItems = await Receipt.findAll({
      where: {
        shop_id,
        created_at: {
          [Op.between]: [period_start, period_end]
        }
      },
      attributes: ['receipt_item_id', [sequelize.fn('SUM', sequelize.col('receipt_quantity')), 'total_quantity']],
      group: ['receipt_item_id'],
      order: [[sequelize.col('total_quantity'), 'DESC']],
      limit: 5,
      include: [{
        model: Product,
        attributes: ['item_name', 'item_selling', 'item_cost']
      }]
    });

    // Get transactions history
    const transactions = await FinancialTransaction.findAll({
      where: {
        shop_id,
        period_start: {
          [Op.gte]: period_start
        }
      },
      order: [['period_start', 'DESC']],
      limit: 10
    });

    res.json({
      total_sales,
      total_costs,
      net_profit,
      inventory_value,
      transactions
    });
      where: { shop_id },
      attributes: [[sequelize.fn('SUM', sequelize.col('item_cost')), 'total_cost']]
    });

    res.json({
      total_sales: totalSales || 0,
      total_costs: totalCosts || 0,
      net_profit: netProfit,
      best_selling_items: bestSellingItems,
      inventory_value: inventoryValue || 0,
      period: {
        start: period_start,
        end: period_end
      }
    });
  } catch (error) {
    console.error('Error in getFinancialReports:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.getWithdrawalOptions = async (req, res) => {
  try {
    const { shop_id } = req.query;
    
    // Get available balance (total sales - total costs)
    const totalSales = await Transaction.sum('trans_total', {
      where: {
        shop_id,
        trans_status: 18
      }
    });

    const totalCosts = await Receipt.sum('receipt_total', {
      where: {
        shop_id
      }
    });

    const availableBalance = (totalSales || 0) - (totalCosts || 0);

    res.json({
      available_balance: availableBalance,
      shop_id
    });
  } catch (error) {
    console.error('Error in getWithdrawalOptions:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

exports.withdrawFunds = async (req, res) => {
  try {
    const { shop_id, amount, description } = req.body;
    
    // Create withdrawal record
    const withdrawal = await Transaction.create({
      trans_id: `WD${Date.now()}`,
      trans_status: 0,
      trans_total: amount,
      trans_type: 'WITHDRAWAL',
      description,
      shop_id,
      username: req.user?.username
    });

    res.json(withdrawal);
  } catch (error) {
    console.error('Error in withdrawFunds:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};
