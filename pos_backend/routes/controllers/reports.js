const { Item, Order, Transaction, Shop, Paymenttransaction, Receipt, Category, Status, Paymentmethod, FinancialSummary, Expense, Liability, SupplySale, SupplyItem } = require('../../db/models');
const { Op, Sequelize } = require('sequelize');
const PDFDocument = require('pdfkit');
const moment = require('moment');
const axios = require('axios');

const RECEIPT_STATUS = {
    PENDING: '16',
    COMPLETED: '15',
    CANCELLED: '18'
};

const getTimeFrameQuery = (timeFrame, dateField = 'updated_at') => {
    const now = moment(); // Lock now once
    let startDate, endDate;

    switch (timeFrame) {
        case 'daily':
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
            break;
        case 'weekly':
            startDate = now.clone().startOf('week');
            endDate = now.clone().endOf('week');
            break;
        case 'monthly':
            startDate = now.clone().startOf('month');
            endDate = now.clone().endOf('month');
            break;
        case 'yearly':
            startDate = now.clone().startOf('year');
            endDate = now.clone().endOf('year');
            break;
        default:
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
    }

    return {
        [dateField]: {
            [Op.between]: [startDate.toDate(), endDate.toDate()]
        }
    };
};

// Helper function to get date range for summary queries
const getDateRange = (timeFrame, startDate, endDate) => {
    if (timeFrame && timeFrame !== 'custom') {
        const timeQuery = getTimeFrameQuery(timeFrame, 'date');
        return timeQuery.date[Op.between];
    } else if (startDate && endDate) {
        return [new Date(startDate), new Date(endDate)];
    } else {
        // Default to current month
        const now = moment();
        return [
            now.clone().startOf('month').toDate(),
            now.clone().endOf('month').toDate()
        ];
    }
};



const generateReports = async (req, res) => {
    try {
        const queryParams = req.query.query || req.query;
        const {
            page = 1,
            pageSize = 10,
            shop_id = queryParams.shop_id,
            timeFrame = queryParams.timeFrame,
            status = queryParams.status,
            query: searchTerm = queryParams.query,
            currentPage = queryParams.currentPage || page,
            pageSize: nestedSize = queryParams.pageSize || pageSize,
            startDate = queryParams.startDate,
            endDate = queryParams.endDate,
            consolidated = queryParams.consolidated
        } = req.query;

        if (!shop_id) {
            return res.status(400).json({
                error: 'Shop ID is required for generating reports'
            });
        }

        const offset = (currentPage - 1) * nestedSize;

        const [summaryStartDate, summaryEndDate] = getDateRange(timeFrame, startDate, endDate);

        const financialSummaries = await FinancialSummary.findAll({
            where: {
                shop_id,
                date: { [Op.between]: [summaryStartDate, summaryEndDate] }
            },
            order: [['date', 'DESC']]
        });

        const financialTotals = financialSummaries.reduce((acc, summary) => {
            acc.totalSales += parseFloat(summary.totalSales || 0);
            acc.totalPurchases += parseFloat(summary.totalPurchases || 0);
            acc.totalExpenses += parseFloat(summary.totalExpenses || 0);
            acc.totalLiabilities += parseFloat(summary.totalLiabilities || 0);
            acc.netProfit += parseFloat(summary.netProfit || 0);
            acc.cashIn += parseFloat(summary.cashIn || 0);
            acc.cashOut += parseFloat(summary.cashOut || 0);
            acc.netCashFlow += parseFloat(summary.netCashFlow || 0);
            return acc;
        }, {
            totalSales: 0,
            totalPurchases: 0,
            totalExpenses: 0,
            totalLiabilities: 0,
            netProfit: 0,
            cashIn: 0,
            cashOut: 0,
            netCashFlow: 0
        });

        const timeFrameQuery = timeFrame && timeFrame !== 'custom'
            ? getTimeFrameQuery(timeFrame)
            : (startDate && endDate
                ? { shop_id, updated_at: { [Op.between]: [new Date(startDate), new Date(endDate)] } }
                : { shop_id }
              );

        const whereClause = {
            ...timeFrameQuery,
            shop_id,
            ...(status && { trans_status: status }),
            ...(searchTerm ? { [Op.or]: [
                { trans_id: { [Op.iLike]: `%${searchTerm}%` } },
                { username: { [Op.iLike]: `%${searchTerm}%` } }
            ] } : {})
        };
       

        const salesDataRaw = await Transaction.findAndCountAll({
            where: {
                ...whereClause,
                trans_status: {
                    [Op.and]: [
                      { [Op.ne]: 0 },
                      { [Op.in]: [15, 20, 23] }
                    ]
                  }
            },
            include: [
                {
                    model: Shop,
                    required: true,
                    attributes: ['shop_id','shop_name']
                },
                {
                    model: Receipt,
                    as: 'receipts',
                    required: false,
                    include: [
                        {
                            model: Item,
                            as: 'item',
                            required: false,
                            attributes: ['item_name', 'item_selling', 'item_buying']
                        }
                    ]
                },
                {
                    model: Paymenttransaction,
                    as: 'payments',
                    required: false,
                    attributes: ['payment_method_id', 'payment_amount', 'payment_transaction_id'],
                    include: [
                        {
                            model: Paymentmethod,
                            as: 'paymentMethod',
                            attributes: ['payment_method_name']
                        }
                    ]
                },
                {
                    model: Status,
                    as: 'status',
                    required: true,
                }
            ],
            limit: parseInt(nestedSize),
            offset: offset,
            distinct: true,
            order: [['updated_at', 'DESC']]
        });

        const salesData = salesDataRaw.rows.map(transaction => ({
            transaction_id: transaction.trans_id,
            transaction_date: moment(transaction.updated_at).format('YYYY-MM-DD'),
            transaction_quantity: (transaction.receipts || []).reduce((sum, r) => sum + (r.receipt_quantity || 0), 0),
            transaction_total: transaction.trans_total,
            transaction_status: transaction.status ? transaction.status.status_name : '',
            items: (transaction.receipts || []).map(receipt => ({
                receipt_item: receipt.item ? receipt.item.item_name : '',
                receipt_quantity: receipt.receipt_quantity,
                receipt_each: receipt.receipt_each,
                receipt_total: receipt.receipt_total
            })),
            payments: (transaction.payments || []).map(payment => ({
                method: payment.paymentMethod ? payment.paymentMethod.payment_method_name : payment.payment_method_id,
                amount: payment.payment_amount,
                id: payment.payment_transaction_id
            })),
            transaction_net: transaction.trans_net,
            transaction_tax: transaction.trans_tax,
        }));

        // --- INVENTORY DATA (SIMPLIFIED) ---
        const lowStockItems = await Item.findAll({
            where: {
                shop_id, // Always filter by shop_id
                item_quantity: { [Op.lt]: 10 }
            },
            attributes: [
                'item_id',
                'item_name',
                'item_quantity',
                'item_selling',
                'item_buying'
            ],
            raw: true
        });

        // Calculate inventory data with values
        const inventoryData = lowStockItems.map(item => {
            const inventoryVal = parseFloat(item.item_quantity || 0) * parseFloat(item.item_buying || 0);
            return {
                ...item,
                inventory_value: inventoryVal,
                quantity_sold_period: 0, // Simplified for performance
                potential_profit: (parseFloat(item.item_selling || 0) - parseFloat(item.item_buying || 0)) * parseInt(item.item_quantity || 0)
            };
        });

        // Calculate total inventory value for all items (not just low stock)
        const allInventoryItems = await Item.findAll({
            where: {
                shop_id,
                item_quantity: { [Op.gt]: 0 }
            },
            attributes: [
                'item_quantity',
                'item_buying'
            ],
            raw: true
        });

        const totalInventoryValue = allInventoryItems.reduce((sum, item) => {
            return sum + (parseFloat(item.item_quantity || 0) * parseFloat(item.item_buying || 0));
        }, 0);

        // --- ENHANCED SUMMARY STATS USING FINANCIAL SUMMARIES ---
        const totalRevenue = financialTotals.totalSales;

        // Daily stats from financial summaries
        const dailyStats = financialSummaries.map(summary => ({
            date: moment(summary.date).format('YYYY-MM-DD'),
            sales: parseFloat(summary.totalSales || 0),
            expenses: parseFloat(summary.totalExpenses || 0),
            profit: parseFloat(summary.netProfit || 0),
            cashFlow: parseFloat(summary.netCashFlow || 0)
        }));

        // Status counts from transactions (for detailed transaction status)
        const statusCounts = await Transaction.findAll({
            where: {
                ...timeFrameQuery,
                shop_id,
                trans_status: {
                    [Op.and]: [
                        { [Op.ne]: 0 },
                        { [Op.in]: [15, 20, 23] }
                    ]
                }
            },
            attributes: [
                'trans_status',
                [Sequelize.fn('COUNT', Sequelize.col('trans_id')), 'count']
            ],
            include: [{
                model: Status,
                as: 'status',
                attributes: ['status_name'],
                required: true
            }],
            group: ['trans_status', 'status.status_name', 'status.status_id'],
            raw: true
        });

        // Enhanced summary stats with financial data
        const summaryStats = {
            // Transaction metrics
            totalTransactions: salesDataRaw.count,
            totalRevenue: totalRevenue,
            averageTransactionValue: totalRevenue && salesDataRaw.count ? totalRevenue / salesDataRaw.count : 0,

            // Financial metrics from summaries
            totalSales: financialTotals.totalSales,
            totalPurchases: financialTotals.totalPurchases,
            totalExpenses: financialTotals.totalExpenses,
            totalLiabilities: financialTotals.totalLiabilities,
            netProfit: financialTotals.netProfit,
            cashIn: financialTotals.cashIn,
            cashOut: financialTotals.cashOut,
            netCashFlow: financialTotals.netCashFlow,

            // Inventory metrics
            totalInventoryValue: totalInventoryValue,
            lowStockItems: inventoryData.length,

            // Status breakdown
            statusCounts: statusCounts.reduce((acc, curr) => {
                acc[curr['status.status_name']] = parseInt(curr.count);
                return acc;
            }, {}),

            // Daily breakdown with financial data
            dailyStats: dailyStats,

            // Summary period info
            summaryPeriod: {
                startDate: moment(summaryStartDate).format('YYYY-MM-DD'),
                endDate: moment(summaryEndDate).format('YYYY-MM-DD'),
                totalDays: financialSummaries.length
            }
        };

     // --- BEST SELLING ITEMS (SHOP-SPECIFIC) ---
        const bestSellingItemsRaw = await Receipt.findAll({
            where: {
                shop_id, 
                ...(timeFrame && timeFrame !== 'custom'
                    ? getTimeFrameQuery(timeFrame, 'created_at')
                    : (startDate && endDate
                        ? { created_at: { [Op.between]: [new Date(startDate), new Date(endDate)] } }
                        : {}
                    )
                )
            },
            attributes: [
                'receipt_item_id',
                [Sequelize.fn('SUM', Sequelize.col('receipt_quantity')), 'totalSold'],
                [Sequelize.fn('SUM', Sequelize.col('receipt_total')), 'totalRevenue']
            ],
            include: [
                {
                    model: Item,
                    as: 'item',
                    attributes: ['item_id', 'item_name', 'item_selling', 'item_buying'],
                    include: [
                        {
                            model: Category,
                            as: 'Category',
                            attributes: ['category_name', 'category_id']
                        }
                    ]
                }
            ],
            group: [
                'receipt_item_id',
                'item.item_id',
                'item.item_name',
                'item.item_selling',
                'item.item_buying',
                'item.Category.category_id',
                'item.Category.category_name'
            ],
            order: [[Sequelize.fn('SUM', Sequelize.col('receipt_quantity')), 'DESC']],
            limit: 3
        });

        const bestSellingItems = bestSellingItemsRaw.map(receipt => {
            const item = receipt.item || {};
            const category = item.Category || {};
            const itemSelling = parseFloat(item.item_selling || 0);
            const itemBuying = parseFloat(item.item_buying || 0);
            const unitsSold = parseInt(receipt.get('totalSold') || 0);
            const actualRevenue = parseFloat(receipt.get('totalRevenue') || 0);
            return {
                item_id: item.item_id || '',
                item_name: item.item_name || '',
                item_selling: itemSelling,
                item_buying: itemBuying,
                units_sold: unitsSold,
                actual_revenue: actualRevenue,
                estimated_revenue: unitsSold * itemSelling,
                profit_margin: itemSelling > 0 ? ((itemSelling - itemBuying) / itemSelling * 100).toFixed(2) : 0,
                total_profit: unitsSold * (itemSelling - itemBuying),
                category_name: category.category_name || ''
            };
        });

        // --- ADDITIONAL FINANCIAL INSIGHTS ---
        // Get recent expenses for the shop
        const recentExpenses = await Expense.findAll({
            where: {
                shop_id,
                date: { [Op.between]: [summaryStartDate, summaryEndDate] }
            },
            order: [['date', 'DESC']],
            limit: 5,
            attributes: ['id', 'name', 'amount', 'date', 'description']
        });

        // Get recent liabilities for the shop
        const recentLiabilities = await Liability.findAll({
            where: {
                shop_id,
                created_at: { [Op.between]: [summaryStartDate, summaryEndDate] }
            },
            order: [['created_at', 'DESC']],
            limit: 5,
            attributes: ['id', 'name', 'amount', 'dueDate', 'description']
        });

        // --- METADATA ---
        const metadata = {
            currentPage: parseInt(currentPage),
            pageSize: parseInt(nestedSize),
            totalPages: Math.ceil(salesDataRaw.count / nestedSize),
            totalRecords: salesDataRaw.count
        };

        // Enhanced inventory data with calculated fields (already calculated above)
        const enhancedInventoryData = inventoryData.map(item => ({
            item_id: item.item_id,
            item_name: item.item_name,
            current_quantity: parseInt(item.item_quantity || 0),
            buying_price: parseFloat(item.item_buying || 0),
            selling_price: parseFloat(item.item_selling || 0),
            quantity_sold_period: parseInt(item.quantity_sold_period || 0),
            inventory_value: parseFloat(item.inventory_value || 0),
            potential_profit: parseFloat(item.potential_profit || 0),
            stock_status: parseInt(item.item_quantity || 0) < 5 ? 'critical' : 'low',
            turnover_rate: 0 // Simplified for performance
        }));

        // Enhanced payload with financial insights
        const payload = {
            salesData,
            inventoryData: enhancedInventoryData,
            summaryStats,
            bestSellingItems,
            metadata,
            // Additional financial data from summary tables
            financialInsights: {
                recentExpenses: recentExpenses.map(expense => ({
                    id: expense.id,
                    name: expense.name,
                    amount: parseFloat(expense.amount),
                    date: moment(expense.date).format('YYYY-MM-DD'),
                    description: expense.description
                })),
                recentLiabilities: recentLiabilities.map(liability => ({
                    id: liability.id,
                    name: liability.name,
                    amount: parseFloat(liability.amount),
                    dueDate: liability.dueDate ? moment(liability.dueDate).format('YYYY-MM-DD') : null,
                    description: liability.description
                })),
                financialSummaries: financialSummaries.map(summary => ({
                    date: moment(summary.date).format('YYYY-MM-DD'),
                    totalSales: parseFloat(summary.totalSales || 0),
                    totalExpenses: parseFloat(summary.totalExpenses || 0),
                    netProfit: parseFloat(summary.netProfit || 0),
                    netCashFlow: parseFloat(summary.netCashFlow || 0)
                }))
            },
            // Shop-specific metadata
            shopInfo: {
                shop_id: shop_id,
                reportPeriod: {
                    startDate: moment(summaryStartDate).format('YYYY-MM-DD'),
                    endDate: moment(summaryEndDate).format('YYYY-MM-DD'),
                    timeFrame: timeFrame || 'custom'
                }
            }
        };

        res.json(payload);
    } catch (error) {
        console.error('Error generating reports:', error);
        res.status(500).json({ 
            error: 'Failed to generate reports',
            details: error.message
        });
    }
};

const generateInvoice = async (req, res) => {
    try {
        const { transaction_id } = req.params;
        const { etims = false } = req.query;
        
        const transaction = await Transaction.findOne({
            where: { trans_id: transaction_id },
            include: [
                { 
                    model: Receipt,
                    as: 'receipts',
                    include: [
                        { 
                            model: Item,
                            attributes: ['name', 'price']
                        },
                        {
                            model: Status,
                            as: 'status',
                            attributes: ['status_name']
                        }
                    ]
                },
                { model: Shop }
            ]
        });

        if (!transaction) {
            return res.status(404).json({ error: 'Transaction not found' });
        }

        // Only generate invoice for completed receipts
        const completedReceipts = transaction.receipts.filter(
            receipt => receipt.trans_status === RECEIPT_STATUS.COMPLETED
        );

        if (completedReceipts.length === 0) {
            return res.status(400).json({ 
                error: 'Cannot generate invoice - no completed receipts found' 
            });
        }

        // Generate eTims invoice if requested (only for completed receipts)
        let etimsData = null;
        if (etims) {
            const items = completedReceipts.map(receipt => ({
                name: receipt.Item.name,
                quantity: receipt.receipt_quantity,
                price: receipt.receipt_each
            }));
            etimsData = await generateEtimsInvoice(transaction, items);
        }

        // Create PDF document
        const doc = new PDFDocument();
        
        // Set response headers for PDF download
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename=invoice-${transaction_id}.pdf`);
        
        doc.pipe(res);

        // Add invoice content
        doc.fontSize(25).text('Invoice', { align: 'center' });
        doc.moveDown();
        doc.fontSize(12).text(`Invoice Number: ${transaction_id}`);
        doc.text(`Date: ${moment(transaction.created_at).format('MMMM Do YYYY, h:mm:ss a')}`);
        doc.text(`Shop: ${transaction.Shop.name}`);

        // Add eTims information if available
        if (etimsData && etimsData.success) {
            doc.moveDown();
            doc.text('KRA eTims Information:', { underline: true });
            doc.text(`eTims Invoice Number: ${etimsData.etimsInvoiceNumber}`);
            doc.text(`Control Code: ${etimsData.controlCode}`);
            
            if (etimsData.qrCode) {
                doc.image(Buffer.from(etimsData.qrCode, 'base64'), {
                    fit: [100, 100],
                    align: 'right'
                });
            }
        }

        doc.moveDown();
        // Add items table (only completed items)
        doc.text('Items:', { underline: true });
        completedReceipts.forEach(receipt => {
            doc.text(`${receipt.Item.name} x ${receipt.receipt_quantity} @ ${receipt.receipt_each} = ${receipt.receipt_total}`);
        });

        doc.moveDown();
        // Calculate totals for completed receipts only
        const completedTotal = completedReceipts.reduce((sum, receipt) => sum + Number(receipt.receipt_total), 0);
        const completedTax = completedReceipts.reduce((sum, receipt) => sum + Number(receipt.receipt_tax), 0);
        const completedNet = completedReceipts.reduce((sum, receipt) => sum + Number(receipt.receipt_net), 0);

        doc.fontSize(14)
            .text(`Subtotal: ${completedNet.toFixed(2)}`, { continued: true })
            .text(`Tax: ${completedTax.toFixed(2)}`, { continued: true })
            .text(`Total Amount: ${completedTotal.toFixed(2)}`, { bold: true });

        doc.end();
    } catch (error) {
        console.error('Error generating invoice:', error);
        res.status(500).json({ error: 'Failed to generate invoice' });
    }
};

const generateEtimsInvoice = async (transaction, items) => {
    try {
        // KRA eTims API configuration
        const KRA_ETIMS_URL = process.env.KRA_ETIMS_URL;
        const KRA_ETIMS_USERNAME = process.env.KRA_ETIMS_USERNAME;
        const KRA_ETIMS_PASSWORD = process.env.KRA_ETIMS_PASSWORD;

        const etimsPayload = {
            invoice: {
                invoiceNumber: transaction.id,
                datetime: moment(transaction.created_at).format('YYYY-MM-DD HH:mm:ss'),
                currency: 'KES',
                items: items.map(item => ({
                    description: item.name,
                    quantity: item.quantity,
                    unitPrice: item.price,
                    taxRate: 16, // Standard VAT rate
                    discount: 0
                })),
                totalAmount: transaction.amount,
                paymentType: transaction.payment_method || 'CASH'
            },
            trader: {
                pin: process.env.KRA_PIN,
                businessName: transaction.Shop.name,
                branchName: transaction.Shop.location || 'Main Branch'
            }
        };

        const response = await axios.post(KRA_ETIMS_URL, etimsPayload, {
            auth: {
                username: KRA_ETIMS_USERNAME,
                password: KRA_ETIMS_PASSWORD
            }
        });

        return {
            success: true,
            controlCode: response.data.controlCode,
            qrCode: response.data.qrCode,
            etimsInvoiceNumber: response.data.invoiceNumber
        };
    } catch (error) {
        console.error('eTims Integration Error:', error);
        return {
            success: false,
            error: error.message
        };
    }
};


const getSupplierSalesReport = async (req, res) => {
    try {
        // Handle both flat and nested query parameter formats
        const queryParams = req.query.query || req.query;

        const {
            page = 1,
            pageSize = 10,
            shop_id = queryParams.shop_id,
            timeFrame = queryParams.timeFrame,
            query: searchQuery = queryParams.query,
            startDate = queryParams.startDate,
            endDate = queryParams.endDate
        } = req.query;

        console.log('Received query req.query:', req.query);

        // Ensure shop_id is provided
        if (!shop_id) {
            return res.status(400).json({
                error: 'Shop ID is required for supplier sales report'
            });
        }

        const offset = (page - 1) * pageSize;
        let where = { shop_id }; // Always filter by shop_id

        console.log('Received query timeFrame:', timeFrame);

        // Apply date filtering
        if (timeFrame && timeFrame !== 'custom') {
            Object.assign(where, getTimeFrameQuery(timeFrame, 'sale_date'));
        } else if (startDate && endDate) {
            where.sale_date = { [Op.between]: [new Date(startDate), new Date(endDate)] };
        }

        console.log('timeFrame:', timeFrame);

        // Apply search filtering
        if (searchQuery) {
            where[Op.or] = [
                { customer_name: { [Op.iLike]: `%${searchQuery}%` } },
                { item_name: { [Op.iLike]: `%${searchQuery}%` } }
            ];
        }

        console.log('Final where clause:', where);

        const { rows, count } = await SupplySale.findAndCountAll({
            where,
            limit: parseInt(pageSize),
            offset: parseInt(offset),
            order: [['sale_date', 'DESC']],
            include: [
                {
                    model: SupplyItem,
                    as: 'supply_item',
                    attributes: ['item_name', 'amount_paid', 'quantity'],
                    required: false
                }
            ]
        });

        // Enhanced response with financial calculations
        const enhancedRows = rows.map(row => ({
            ...row.toJSON(),
            profit_margin: row.selling_price && row.supply_item?.amount_paid
                ? ((row.selling_price - (row.supply_item.amount_paid / row.supply_item.quantity)) / row.selling_price * 100).toFixed(2)
                : 0,
            unit_cost: row.supply_item?.amount_paid && row.supply_item?.quantity
                ? (row.supply_item.amount_paid / row.supply_item.quantity).toFixed(2)
                : 0
        }));

        res.json({
            success: true,
            data: enhancedRows,
            metadata: {
                total: count,
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                totalPages: Math.ceil(count / pageSize),
                shop_id: shop_id,
                reportPeriod: {
                    timeFrame: timeFrame || 'custom',
                    startDate: startDate || null,
                    endDate: endDate || null
                }
            }
        });
    } catch (err) {
        console.error('Error in getSupplierSalesReport:', err);
        res.status(500).json({
            error: 'Failed to generate supplier sales report',
            details: err.message
        });
    }
};

  

module.exports = {
    generateReports,
    generateInvoice,
    getSupplierSalesReport
};
