const { Discount } = require('../../db/models');
const genericController = require('./_genericController');

exports.getAllDiscount = genericController.getAll(Discount);
exports.getDiscountById = genericController.getById(Discount, 'discount_id');
exports.createDiscount = genericController.createMany(Discount);
exports.updateDiscount = genericController.updateMany(Discount, 'discount_id');
exports.deleteDiscount = genericController.delete(Discount, 'discount_id');



exports.getDiscountsByShopId = async (req, res) => {
    try {
        const { shop_id } = req.params;
        if (!shop_id) return res.status(400).json({ error: "shop_id is required" });

        const discounts = await Discount.findAll({ where: { shop_id } });

        if (!discounts || discounts.length === 0) {
            return res.status(404).json({ error: "No discounts found for this shop_id" });
        }

        res.json({
            data: discounts,
            metadata: { total: discounts.length }
        });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
};
