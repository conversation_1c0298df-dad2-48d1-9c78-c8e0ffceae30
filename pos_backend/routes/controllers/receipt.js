const { Receipt,Transaction,Item } = require('../../db/models');
const genericController = require('./_genericController');
const { Op } = require('sequelize')
exports.getAllreceipt = genericController.getAll(Receipt);
exports.getreceiptById = genericController.getById(Receipt, 'receipt_id');
exports.createreceipt = genericController.create(Receipt);
exports.updatereceipt = genericController.update(Receipt, 'receipt_id');
exports.deletereceipt = genericController.delete(Receipt, 'receipt_id');

exports.getreceiptByTrans = async (req, res) => {
    try {
    const { id } = req.params;
    const {shop_id} = req.query;
    const transaction = await Transaction.findOne({where: { trans_id: id, trans_status: { [Op.or]: [0, 18] } },});
    if (!transaction) return res.status(400).json({error: 403} );
    if (transaction.shop_id != shop_id) return res.status(403).json({ error: 403 });
    const receipts = await Receipt.findAll({  where: { trans_id: id,shop_id:shop_id },  order: [['created_at', 'DESC']]});
    res.json(receipts);
    } catch (error) {
      console.log(error)
      res.status(500).json({ error: "Internal server error" });
    }
  };
  

