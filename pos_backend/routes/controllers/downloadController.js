const { Op } = require('sequelize');
const { User, Role, Shop, Category, Discount, Payment_Method } = require('../../db/models');
const { generateExcel } = require('../../utils/excel');

const getDownloadData = async (req, res) => {
  const { type, query } = req.query;
  const isAdmin = req.user?.roles?.some(r => r.role_name === 'admin');
  const queryParams = { ...query, ...(isAdmin ? {} : { shop_id: req.user?.shop_id }) };

  let data = [];
  let headers = [];

  try {
    switch (type) {
      case 'users':
        const users = await User.findAll({
          where: queryParams,
          include: [
            {
              model: Role,
              as: 'roles',
              attributes: ['role_name']
            },
            {
              model: Shop,
              as: 'shops',
              attributes: ['shop_name']
            },
            {
              model: Status,
              as: 'user_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Username', 'First Names', 'Last Names', 'Email', 'Status', 'Roles', 'Shops'];
        data = users.map(user => ({
          Username: user.username,
          'First Names': user.first_names,
          'Last Names': user.last_names,
          Email: user.email,
          Status: user.user_status?.status_name || '',
          Roles: user.roles?.map(role => role.role_name).join(', ') || '',
          Shops: user.shops?.map(shop => shop.shop_name).join(', ') || ''
        }));
        break;

      case 'roles':
        const roles = await Role.findAll({
          where: queryParams,
          include: [
            {
              model: Status,
              as: 'role_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Role Name', 'Description', 'Status'];
        data = roles.map(role => ({
          'Role Name': role.role_name,
          Description: role.role_description,
          Status: role.role_status?.status_name || ''
        }));
        break;

      case 'shops':
        const shops = await Shop.findAll({
          where: queryParams,
          include: [
            {
              model: Status,
              as: 'shop_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Shop Name', 'Owner Names', 'Email', 'Phone', 'Status', 'Location'];
        data = shops.map(shop => ({
          'Shop Name': shop.shop_name,
          'Owner Names': shop.shop_owner_names,
          Email: shop.shop_email?.join(', ') || '',
          Phone: shop.shop_phone?.join(', ') || '',
          Status: shop.shop_status?.status_name || '',
          Location: `${shop.shop_location_name}, ${shop.shop_location_address}`
        }));
        break;

      case 'categories':
        const categories = await Category.findAll({
          where: queryParams,
          include: [
            {
              model: Status,
              as: 'category_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Category Name', 'Description', 'Status', 'Subcategories'];
        data = categories.map(category => ({
          'Category Name': category.category_name,
          Description: category.category_description,
          Status: category.category_status?.status_name || '',
          Subcategories: category.sub_category?.join(', ') || ''
        }));
        break;

      case 'discounts':
        const discounts = await Discount.findAll({
          where: queryParams,
          include: [
            {
              model: Status,
              as: 'discount_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Discount Name', 'Description', 'Type', 'Amount', 'Start Date', 'End Date', 'Status'];
        data = discounts.map(discount => ({
          'Discount Name': discount.discount_name,
          Description: discount.discount_description,
          Type: discount.discount_type,
          Amount: discount.discount_amount,
          'Start Date': discount.discount_start_date,
          'End Date': discount.discount_end_date,
          Status: discount.discount_status?.status_name || ''
        }));
        break;

      case 'payments':
        const payments = await Payment_Method.findAll({
          where: queryParams,
          include: [
            {
              model: Status,
              as: 'payment_status',
              attributes: ['status_name']
            }
          ]
        });

        headers = ['Payment Method', 'Description', 'Status'];
        data = payments.map(payment => ({
          'Payment Method': payment.payment_method_name,
          Description: payment.payment_method_description,
          Status: payment.payment_status?.status_name || ''
        }));
        break;

      default:
        return res.status(400).json({ error: 'Invalid type' });
    }

    const fileName = `${type}_${new Date().toISOString().split('T')[0]}.xlsx`;
    const excelData = generateExcel(headers, data);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
    res.send(excelData);

  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Failed to generate download' });
  }
};

module.exports = {
  getDownloadData
};
