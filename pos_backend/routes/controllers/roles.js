const { Role } = require('../../db/models');
const genericController = require('./_genericController');

//
// exports.getRoleById = genericController.getById(Role, 'role_id');
// exports.createRole = genericController.create(Role);
// exports.updateRole = genericController.update(Role, 'role_id');
// exports.deleteRole = genericController.delete(Role, 'role_id')

exports.getAllRole = genericController.getAll(Role);

exports.getAllRoles = async (req, res) => {
    try {
        const { page = 1, pageSize = 10 } = req.query;
        const offset = (page - 1) * pageSize;

        const data = await Role.findAndCountAll({
            limit: parseInt(pageSize),
            offset: parseInt(offset),
            order: [['role_name', 'ASC']]
        });

        res.status(200).json({
            data: data.rows,
            metadata: {
                total: data.count,
                page: parseInt(page),
                pageSize: parseInt(pageSize),
                totalPages: Math.ceil(data.count / pageSize)
            }
        });
    } catch (error) {
        console.error(error.stack);
        res.status(500).json({ error: error.message });
    }
};

exports.getRoleById = genericController.getById(Role, 'role_id');
exports.createRole = genericController.create(Role);
exports.updateRole = genericController.update(Role, 'role_id');
exports.deleteRole = genericController.delete(Role, 'role_id');