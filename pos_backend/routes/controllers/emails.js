exports.getWelcomeEmail = (username, generatedPassword) => {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Account Created</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
            }
            .email-container {
                background-color: #ffffff;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                max-width: 400px;
                text-align: center;
                animation: fadeIn 1.5s ease-in-out;
            }
            .email-container h1 {
                color: #333333;
            }
            .email-container p {
                color: #555555;
                line-height: 1.6;
            }
            .email-container .highlight {
                color: #007BFF;
                font-weight: bold;
            }
            .email-container img {
                width: 250px;
                animation: bounce 2s infinite;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-30px); }
                60% { transform: translateY(-15px); }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <img src="https://greatint.co.ke/static/media/2.9c10e4151c25fde8dea7.png" alt="Welcome Image">
            <h1>Your Account Has Been Created</h1>
            <p>Your Username: <span class="highlight">${username}</span></p>
            <p>Your Temporary Password: <span class="highlight">${generatedPassword}</span></p>
            <p>Please log in and change your password.</p>
        </div>
    </body>
    </html>`;
};

exports.getPasswordResetEmail = (username, temporaryPassword) => {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .email-container {
                background-color: #ffffff;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                max-width: 500px;
                text-align: center;
                animation: fadeIn 1.5s ease-in-out;
            }
            .email-container h1 {
                color: #333333;
                margin-bottom: 20px;
            }
            .email-container p {
                color: #555555;
                line-height: 1.6;
                margin-bottom: 15px;
            }
            .email-container .highlight {
                color: #dc3545;
                font-weight: bold;
                font-size: 18px;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 5px;
                border: 2px dashed #dc3545;
                margin: 20px 0;
                display: inline-block;
            }
            .email-container .warning {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                color: #856404;
            }
            .email-container .security-note {
                background-color: #d1ecf1;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
                color: #0c5460;
                font-size: 14px;
            }
            .email-container img {
                width: 200px;
                margin-bottom: 20px;
                animation: pulse 2s infinite;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <img src="https://greatint.co.ke/static/media/2.9c10e4151c25fde8dea7.png" alt="Password Reset">
            <h1>🔐 Password Reset Request</h1>
            <p>Hello <strong>${username}</strong>,</p>
            <p>We received a request to reset your password. Your temporary password is:</p>

            <div class="highlight">
                ${temporaryPassword}
            </div>

            <div class="warning">
                <strong>⚠️ Important:</strong> This is a temporary password. You must change it immediately after logging in.
            </div>

            <p><strong>Next Steps:</strong></p>
            <ol style="text-align: left; display: inline-block;">
                <li>Log in using your email and the temporary password above</li>
                <li>You will be prompted to change your password</li>
                <li>Choose a strong, unique password</li>
                <li>Complete the password change process</li>
            </ol>

            <div class="security-note">
                <strong>🛡️ Security Note:</strong><br>
                • This temporary password expires after first use<br>
                • If you didn't request this reset, please contact support immediately<br>
                • Never share your password with anyone<br>
                • Generated on: ${new Date().toLocaleString()}
            </div>

            <p style="margin-top: 30px; font-size: 12px; color: #888;">
                If you're having trouble logging in, please contact our support team.
            </p>
        </div>
    </body>
    </html>`;
};
