const { Shop,Status,SupplySale,SupplyItem,Supplier,Item,Category,Usershop} = require('../../db/models');
const genericController = require('./_genericController');

exports.createShop = genericController.create(Shop);
exports.updateShop = genericController.update(Shop, 'shop_id');

// exports.deleteShop = async (req, res) => {
//     try {
//         const { id } = req.params;
//         const shop = await Shop.findByPk(id);
//         if (!shop) {
//             return res.status(404).json({ error: 'Shop not found' });
//         }
//         await SupplySale.destroy({ where: { shop_id: id } });
//         await SupplyItem.destroy({ where: { shop_id: id } });
//         await Supplier.destroy({ where: { shop_id: id } });
//         await Item.destroy({ where: { shop_id: id } });
//         await Category.destroy({ where: { shop_id: id } });
//         await Usershop.destroy({ where: { shop_id: id } });
//         await shop.destroy();
//         res.status(200).json({ 
//             message: 'Shop and all associated records deleted successfully' 
//         });
//     } catch (error) {
//         console.error(error.stack);
//         res.status(500).json({ error: error.message });
//     }
// };


exports.deleteShop = async (req, res) => {
    try {
        const { id } = req.params;
        const shop = await Shop.findByPk(id);
        if (!shop) {
            return res.status(404).json({ error: 'Shop not found' });
        }

        await SupplySale.destroy({ where: { shop_id: id } });
        
        await SupplyItem.destroy({ where: { shop_id: id } });
        
        await Item.destroy({ where: { shop_id: id } });
        
        await Supplier.destroy({ where: { shop_id: id } });
        
        await Category.destroy({ where: { shop_id: id } });
        await Usershop.destroy({ where: { shop_id: id } });
        
        await shop.destroy();
        
        res.status(200).json({ 
            message: 'Shop and all associated records deleted successfully' 
        });
    } catch (error) {
        console.error(error.stack);
        res.status(500).json({ error: error.message });
    }
};
exports.getAllShops = async (req, res) => {
    try {
        const data = await Shop.findAll({
            include: [{ model: Status, as: 'status', attributes: ['status_name'] }]
        });
        const total = await Shop.count();

        res.status(200).json({
            data: data,
            metadata: { total }
        });
    } catch (error) {
        console.error(error.stack);
        res.status(500).json({ error: error.message });
    }
};

exports.getShopById = async (req, res) => {
    try {
        const { id } = req.params;
        const data = await Shop.findOne({
            where: { shop_id: id },
            include: [{ model: Status, as: 'status', attributes: ['status_name'] }]
        });
        const total = await Shop.count();

        if (data) {
            res.status(200).json({
                data: data,
                metadata: { total }
            });
        } else {
            res.status(404).json({ error: `shop_id not found` });
        }
    } catch (error) {
        console.error(error.stack);
        res.status(500).json({ error: error.message });
    }
};
