const { PaymentTransaction } = require('../../db/models');
const genericController = require('./_genericController');

exports.getAllTransaction = genericController.getAll(PaymentTransaction);
exports.getPaymentTransactionById = genericController.getById(PaymentTransaction, 'payment_transaction_id');
exports.createPaymentTransaction = genericController.create(PaymentTransaction);
exports.updatePaymentTransaction = genericController.update(PaymentTransaction, 'payment_transaction_id');
exports.deletePaymentTransaction = genericController.delete(PaymentTransaction, 'payment_transaction_id');