const { Transaction, Receipt, SupplyItem,Status, SupplySale, Item, Shop,User, Liability, Expense, FinancialSummary, LiabilityPayment, sequelize } = require('../../db/models');
const { Op } = require('sequelize');
const moment = require('moment');
const { sendEmail } = require('../../utils/emails');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

const getTimeFrameQuery = (timeFrame) => {
    const now = moment();
    let startDate, endDate;

    switch (timeFrame) {
        case 'daily':
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
            break;
        case 'weekly':
            startDate = now.clone().startOf('week');
            endDate = now.clone().endOf('week');
            break;
        case 'monthly':
            startDate = now.clone().startOf('month');
            endDate = now.clone().endOf('month');
            break;
        case 'yearly':
            startDate = now.clone().startOf('year');
            endDate = now.clone().endOf('year');
            break;
        default:
            startDate = now.clone().startOf('day');
            endDate = now.clone().endOf('day');
    }

    return [startDate.toDate(), endDate.toDate()];
};

// Helper function to get supply analytics
const getSupplyAnalytics = async (shop_id, startDate, endDate) => {
    // Get pending supplies
    const pendingSupplies = await SupplyItem.findAll({
        where: {
            shop_id,
            status: { [Op.ne]: 1 }, // Not completed
            supply_date: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount_paid')), 'total_pending'],
            [sequelize.fn('COUNT', sequelize.col('supply_id')), 'pending_count']
        ],
        raw: true
    });

    // Get paid supplies
    const paidSupplies = await SupplyItem.findAll({
        where: {
            shop_id,
            status: 15, // Completed
            supply_date: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount_paid')), 'total_paid'],
            [sequelize.fn('COUNT', sequelize.col('supply_id')), 'paid_count']
        ],
        raw: true
    });

    // Get top buyers in supply
    const topBuyers = await SupplySale.findAll({
        where: {
            shop_id,
            sale_date: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            'customer_name',
            [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_purchased'],
            [sequelize.fn('COUNT', sequelize.col('supply_id')), 'purchase_count']
        ],
        group: ['customer_name'],
        order: [[sequelize.fn('SUM', sequelize.col('total_amount')), 'DESC']],
        limit: 10,
        raw: true
    });

    return {
        pending: pendingSupplies[0] || { total_pending: 0, pending_count: 0 },
        paid: paidSupplies[0] || { total_paid: 0, paid_count: 0 },
        topBuyers: topBuyers || []
    };
};

// Helper function to get liability data
const getLiabilityData = async (shop_id, startDate, endDate) => {
    // Get liabilities
    const liabilities = await Liability.findAll({
        where: {
            shop_id,
            created_at: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount')), 'total_liabilities'],
            [sequelize.fn('COUNT', sequelize.col('id')), 'liability_count']
        ],
        raw: true
    });

    // Get liability payments
    const payments = await LiabilityPayment.findAll({
        where: {
            shop_id,
            paymentDate: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount')), 'total_payments'],
            [sequelize.fn('COUNT', sequelize.col('id')), 'payment_count']
        ],
        raw: true
    });

    // Get overdue liabilities
    const overdueLiabilities = await Liability.findAll({
        where: {
            shop_id,
            dueDate: { [Op.lt]: new Date() },
            status: { [Op.ne]: 2 } // Not fully paid
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount')), 'total_overdue'],
            [sequelize.fn('COUNT', sequelize.col('id')), 'overdue_count']
        ],
        raw: true
    });

    return {
        liabilities: liabilities[0] || { total_liabilities: 0, liability_count: 0 },
        payments: payments[0] || { total_payments: 0, payment_count: 0 },
        overdue: overdueLiabilities[0] || { total_overdue: 0, overdue_count: 0 }
    };
};

exports.getFinancialReports = async (req, res) => {
    try {
        const { shop_id, timeFrame } = req.query;
        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        const [startDate, endDate] = getTimeFrameQuery(timeFrame);

        const transactionsByType = await Transaction.findAll({
            where: {
              shop_id,
              trans_status: 15,
              [Op.and]: [
                sequelize.where(
                  sequelize.fn('DATE', sequelize.col('updated_at')),
                  {
                    [Op.between]: [startDate, endDate]
                  }
                )
              ]
            },
            attributes: [
              'trans_type',
              [sequelize.fn('SUM', sequelize.col('trans_total')), 'total_amount'],
              [sequelize.fn('COUNT', sequelize.col('trans_id')), 'transaction_count']
            ],
            group: ['trans_type'],
            raw: true
          });
        // Separate transaction types
        const normalSales = transactionsByType.find(t => t.trans_type == null || t.trans_type == 'SALE') || { total_amount: 0, transaction_count: 0 };
        const orderSales = transactionsByType.find(t => t.trans_type == 'ORDER') || { total_amount: 0, transaction_count: 0 };
        const supplySales = transactionsByType.find(t => t.trans_type == 'SUPPLY') || { total_amount: 0, transaction_count: 0 };

        // Get supply analytics
        const supplyAnalytics = await getSupplyAnalytics(shop_id, startDate, endDate);

        // Get expenses
        const expenses = await Expense.findAll({
            where: {
                shop_id,
                date: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('amount')), 'total_expenses'],
                [sequelize.fn('COUNT', sequelize.col('id')), 'expense_count']
            ],
            raw: true
        });

        // Get liabilities and payments
        const liabilityData = await getLiabilityData(shop_id, startDate, endDate);

        // Get inventory items (simplified approach to avoid complex queries)
        const inventoryItems = await Item.findAll({
            where: {
                shop_id,
                item_quantity: { [Op.gt]: 0 }
            },
            attributes: [
                'item_id',
                'item_name',
                'item_quantity',
                'item_buying',
                'item_selling'
            ],
            raw: true
        });

        // Calculate inventory value for each item
        const inventoryValue = inventoryItems.map(item => {
            const inventoryVal = parseFloat(item.item_quantity || 0) * parseFloat(item.item_buying || 0);
            return {
                ...item,
                inventory_value: inventoryVal,
                quantity_sold_period: 0 // Simplified for now
            };
        });

        // Calculate total inventory value
        const totalInventoryValue = inventoryValue.reduce((sum, item) => {
            return sum + parseFloat(item.inventory_value || 0);
        }, 0);



        // Calculate COGS for regular sales
        const salesCOGS = await Receipt.findAll({
            where: {
                shop_id,
                created_at: { [Op.between]: [startDate, endDate] }
            },
            include: [{
                model: Item,
                attributes: []
            }],
            attributes: [
                [sequelize.fn('SUM', sequelize.literal('receipt_quantity * "item"."item_selling"')), 'total_cogs']
            ],
            raw: true
        });

        // Calculate COGS for supply sales - simplified approach
        const supplyCOGS = await SupplySale.findAll({
            where: {
                shop_id,
                sale_date: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_supply_cogs']
            ],
            raw: true
        });

        // Prepare financial statements
        // Get shop money movements
        const moneyMovements = await Transaction.findAll({
            where: {
                shop_id,
                trans_status: { [Op.ne]: 15 },
                updated_at: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                'trans_type',
                [sequelize.fn('SUM', sequelize.col('trans_total')), 'amount']
            ],
            group: ['trans_type'],
            raw: true
        });



        // Calculate totals
        const totalSales = normalSales.total_amount + orderSales.total_amount + supplySales.total_amount;
        const totalTransactions = normalSales.transaction_count + orderSales.transaction_count + supplySales.transaction_count;

        const incomeStatement = {
            revenue: {
                normal_sales: normalSales.total_amount || 0,
                order_sales: orderSales.total_amount || 0,
                supply_sales: supplySales.total_amount || 0,
                total_revenue: totalSales
            },
            cost_of_goods_sold: {
                sales_cogs: salesCOGS[0]?.total_cogs || 0,
                supply_cogs: supplyCOGS[0]?.total_supply_cogs || 0,
                total_cogs: (salesCOGS[0]?.total_cogs || 0) + (supplyCOGS[0]?.total_supply_cogs || 0)
            },
            expenses: {
                total_expenses: expenses[0]?.total_expenses || 0,
                expense_count: expenses[0]?.expense_count || 0
            },
            gross_profit: {
                sales_profit: (normalSales.total_amount || 0) - (salesCOGS[0]?.total_cogs || 0),
                supply_profit: (supplySales.total_amount || 0) - (supplyCOGS[0]?.total_supply_cogs || 0),
                total_gross_profit: totalSales - ((salesCOGS[0]?.total_cogs || 0) + (supplyCOGS[0]?.total_supply_cogs || 0))
            },
            net_profit: totalSales - ((salesCOGS[0]?.total_cogs || 0) + (supplyCOGS[0]?.total_supply_cogs || 0)) - (expenses[0]?.total_expenses || 0)
        };

        // Calculate net money movement
        const moneyIn = moneyMovements
            .filter(m => m.trans_type == 'income')
            .reduce((sum, m) => sum + parseFloat(m.amount), 0);
            
        const moneyOut = moneyMovements
            .filter(m => m.trans_type == 'expense')
            .reduce((sum, m) => sum + parseFloat(m.amount), 0);

        const balanceSheet = {
            money_movements: {
                money_in: moneyIn,
                money_out: moneyOut,
                net_movement: moneyIn - moneyOut
            },
            assets: {
                inventory: {
                    current_inventory_value: totalInventoryValue,
                    inventory_items: inventoryValue.length,
                }
            },
            activity_metrics: {
                money_in_count: moneyMovements.filter(m => m.trans_type == 'income').length,
                money_out_count: moneyMovements.filter(m => m.trans_type == 'expense').length,
                total_transactions: totalTransactions,
                normal_sales_count: normalSales.transaction_count || 0,
                order_sales_count: orderSales.transaction_count || 0,
                supply_sales_count: supplySales.transaction_count || 0
            },
            liabilities: {
                total_liabilities: liabilityData.liabilities.total_liabilities || 0,
                total_payments: liabilityData.payments.total_payments || 0,
                outstanding: (liabilityData.liabilities.total_liabilities || 0) - (liabilityData.payments.total_payments || 0),
                liability_count: liabilityData.liabilities.liability_count || 0,
                overdue_amount: liabilityData.overdue.total_overdue || 0,
                overdue_count: liabilityData.overdue.overdue_count || 0
            },
            supply_analytics: {
                pending_supplies: supplyAnalytics.pending,
                paid_supplies: supplyAnalytics.paid,
                top_buyers: supplyAnalytics.topBuyers
            }
        };

        res.json({
            timeFrame,
            period: {
                start: startDate,
                end: endDate
            },
            income_statement: incomeStatement,
            balance_sheet: balanceSheet
        });

    } catch (error) {
        console.error('Error generating financial reports:', error);
        res.status(500).json({ error: 'Failed to generate financial reports' });
    }
};

exports.createExpense = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { shop_id, expense_details, trans_total, trans_description, date, status,username,expense_category } = req.body;

        const expense = await Expense.create({
            shop_id,
            name:expense_details,
            amount:trans_total,
            description:trans_description,
            date: date ? new Date(date) : new Date(),
            status: status || 1
        }, { transaction: t });
        await Transaction.create({
            shop_id,
            trans_type: 'EXPENSE',
            trans_total: trans_total,
            trans_quantity: 1,
            trans_status: 22,
            trans_tax: 0,
            trans_net: parseFloat(trans_total),
            trans_discount: 0,
            trans_description: trans_description || 'Expense Transaction',
            income_type: 'expense',
            username: username || 'system',
            extra_data: {
                expense_id: expense.id,
                expense_category:expense_category
            }
        }, { transaction: t });

        await t.commit();
        res.json({ success: true, data: expense });
    } catch (error) {
        await t.rollback();
        console.error('Error creating expense:', error);
        res.status(500).json({ error: 'Failed to create expense' });
    }
};

exports.getExpenses = async (req, res) => {
    try {
        const { shop_id, timeFrame } = req.query;

        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        let whereClause = { shop_id };

        if (timeFrame) {
            const [startDate, endDate] = getTimeFrameQuery(timeFrame);
            whereClause.date = { [Op.between]: [startDate, endDate] };
        }

        const expenses = await Expense.findAll({
            where: whereClause,
            order: [['date', 'DESC']]
        });

        res.json({ success: true, data: expenses });
    } catch (error) {
        console.error('Error getting expenses:', error);
        res.status(500).json({ error: 'Failed to get expenses' });
    }
};

exports.updateExpense = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { id } = req.params;
        const { name, amount, description, date, status } = req.body;

        const expense = await Expense.findByPk(id);
        if (!expense) {
            await t.rollback();
            return res.status(404).json({ error: 'Expense not found' });
        }

        await expense.update({
            name: name || expense.name,
            amount: amount || expense.amount,
            description: description || expense.description,
            date: date ? new Date(date) : expense.date,
            status: status !== undefined ? status : expense.status
        }, { transaction: t });

        await t.commit();
        res.json({ success: true, data: expense });
    } catch (error) {
        await t.rollback();
        console.error('Error updating expense:', error);
        res.status(500).json({ error: 'Failed to update expense' });
    }
};

exports.deleteExpense = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { id } = req.params;
        const expense = await Expense.findByPk(id);
        if (!expense) {
            await t.rollback();
            return res.status(404).json({ error: 'Expense not found' });
        }
        await expense.destroy({ transaction: t });

        await t.commit();
        res.json({ success: true, message: 'Expense deleted successfully' });
    } catch (error) {
        await t.rollback();
        res.status(500).json({ error: error.message });
    }
};

exports.getMoneySummary = async (req, res) => {
    try {
        const { shop_id, timeFrame } = req.query;
        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        const [startDate, endDate] = getTimeFrameQuery(timeFrame);

        const moneyMovements = await Transaction.findAll({
            where: {
                shop_id,
                // trans_status: 15,
                updated_at: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                'income_type',
                [sequelize.fn('SUM', sequelize.col('trans_total')), 'amount']
            ],
            group: ['income_type'],
            raw: true
        });
      
        const moneyIn = moneyMovements
            .filter(m => m.income_type == 'income')
            .reduce((sum, m) => sum + parseFloat(m.amount), 0);

        const moneyOut = moneyMovements
            .filter(m => m.income_type == 'expense')
            .reduce((sum, m) => sum + parseFloat(m.amount), 0);

        res.json({
            success: true,
            data: {
                money_in: moneyIn,
                money_out: moneyOut,
                net_movement: moneyIn - moneyOut
            }
        });

    } catch (error) {
        console.error('Error getting money summary:', error);
        res.status(500).json({ error: 'Failed to get money summary' });
    }
};

exports.createLiability = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { shop_id, name, amount, dueDate, description, status,username} = req.body;

        if (!shop_id || !name || !amount) {
            return res.status(400).json({ error: 'Shop ID, name, and amount are required' });
        }

        const liability = await Liability.create({
            shop_id,
            name,
            amount,
            dueDate: dueDate ? new Date(dueDate) : null,
            description,
            status: status || 1
        }, { transaction: t });

        await Transaction.create({
            shop_id,
            trans_type: 'EXPENSE',
            trans_description: trans_description || 'Expense Transaction',
            trans_quantity: 1,
            trans_status: 22,
            trans_tax: 0,
            trans_net: parseFloat(amount),
            trans_discount: 0,
            income_type: 'expense',
            username: username|| 'system',
            extra_data: {
                type: 'liability',
                liability_id: liability.id,
                description: description || 'Liability creation'
            }
        }, { transaction: t });

        await t.commit();
        res.json({ success: true, data: liability });
    } catch (error) {
        await t.rollback();
        console.error('Error creating liability:', error);
        res.status(500).json({ error: 'Failed to create liability' });
    }
};

exports.getLiabilities = async (req, res) => {
    try {
        const { shop_id, status, overdue } = req.query;

        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        let whereClause = { shop_id };

        if (status) {
            whereClause.status = status;
        }

        if (overdue == 'true') {
            whereClause.dueDate = { [Op.lt]: new Date() };
            whereClause.status = { [Op.ne]: 2 }; 
        }

        const liabilities = await Liability.findAll({
            where: whereClause,
            include: [{
                model: LiabilityPayment,
                as: 'payments',
                attributes: ['id', 'amount', 'paymentDate', 'description']
            },
            {
                model: Status,
                as: 'statusInfo',
                attributes: ['status_description']
            }
          
        ],
            order: [['created_at', 'DESC']]
        });

        res.json({ success: true, data: liabilities });
    } catch (error) {
        console.error('Error getting liabilities:', error);
        res.status(500).json({ error: 'Failed to get liabilities' });
    }
};

exports.updateLiability = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { id } = req.params;
        const { name, amount, dueDate, description, status } = req.body;

        const liability = await Liability.findByPk(id);
        if (!liability) {
            await t.rollback();
            return res.status(404).json({ error: 'Liability not found' });
        }

        await liability.update({
            name: name || liability.name,
            amount: amount || liability.amount,
            dueDate: dueDate ? new Date(dueDate) : liability.dueDate,
            description: description || liability.description,
            status: status !== undefined ? status : liability.status
        }, { transaction: t });

        await t.commit();
        res.json({ success: true, data: liability });
    } catch (error) {
        await t.rollback();
        console.error('Error updating liability:', error);
        res.status(500).json({ error: 'Failed to update liability' });
    }
};

exports.deleteLiability = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { id } = req.params;

        const liability = await Liability.findByPk(id);
        if (!liability) {
            await t.rollback();
            return res.status(404).json({ error: 'Liability not found' });
        }

        // Delete associated payments first
        await LiabilityPayment.destroy({
            where: { liabilityId: id },
            transaction: t
        });

        await liability.destroy({ transaction: t });

        await t.commit();
        res.json({ success: true, message: 'Liability deleted successfully' });
    } catch (error) {
        await t.rollback();
        console.error('Error deleting liability:', error);
        res.status(500).json({ error: 'Failed to delete liability' });
    }
};

// Liability Payment Functions
exports.createLiabilityPayment = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { liabilityId, shop_id, amount, paymentDate, description, status,username } = req.body;

        if (!shop_id || !amount) {
            return res.status(400).json({ error: 'Shop ID and amount are required' });
        }

        // If liabilityId is provided, verify the liability exists
        if (liabilityId) {
            const liability = await Liability.findByPk(liabilityId);
            if (!liability) {
                await t.rollback();
                return res.status(404).json({ error: 'Liability not found' });
            }
        }

        const payment = await LiabilityPayment.create({
            liabilityId: liabilityId || null,
            shop_id,
            amount,
            paymentDate: paymentDate ? new Date(paymentDate) : new Date(),
            description,
            status: status || 1
        }, { transaction: t });

        // Create a transaction record for money withdrawal
        await Transaction.create({
            shop_id,
            trans_type: 'EXPENSE',
            trans_description: trans_description || 'Expense Transaction',
            trans_tax: 0,
            trans_net: parseFloat(amount),
            trans_discount: 0,
            trans_total: amount,
            trans_quantity: 1,
            trans_status: 22,
            username: username || 'system',
            extra_data: {
                type: 'liability_payment',
                liability_payment_id: payment.id,
                description: description || 'Liability payment'
            }
        }, { transaction: t });

        await t.commit();
        res.json({ success: true, data: payment });
    } catch (error) {
        await t.rollback();
        console.error('Error creating liability payment:', error);
        res.status(500).json({ error: 'Failed to create liability payment' });
    }
};

exports.getLiabilityPayments = async (req, res) => {
    try {
        const { shop_id, liabilityId } = req.query;

        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        let whereClause = { shop_id };

        if (liabilityId) {
            whereClause.liabilityId = liabilityId;
        }

        const payments = await LiabilityPayment.findAll({
            where: whereClause,
            include: [{
                model: Liability,
                attributes: ['id', 'name', 'amount', 'dueDate']
            }],
            order: [['paymentDate', 'DESC']]
        });

        res.json({ success: true, data: payments });
    } catch (error) {
        console.error('Error getting liability payments:', error);
        res.status(500).json({ error: 'Failed to get liability payments' });
    }
};

// Money withdrawal function for liabilities
exports.withdrawMoney = async (req, res) => {
    const t = await sequelize.transaction();
    try {
        const { shop_id, amount, description, category ,username} = req.body;

        if (!shop_id || !amount) {
            return res.status(400).json({ error: 'Shop ID and amount are required' });
        }
        

        // Create transaction record for money withdrawal
        const transaction = await Transaction.create({
            shop_id,
            trans_type: 'EXPENSE',
            trans_description: trans_description || 'Expense Transaction',
            trans_quantity: 1,
            trans_tax: 0,
            trans_net: parseFloat(amount),
            trans_discount: 0,
            trans_status: 22,
            username: username || 'system',
            extra_data: {
                type: 'money_withdrawal',
                category: category || 'general',
                description: description || 'Money withdrawal'
            }
        }, { transaction: t });

        await t.commit();
        res.json({
            success: true,
            data: transaction,
            message: 'Money withdrawal recorded successfully'
        });
    } catch (error) {
        await t.rollback();
        console.error('Error recording money withdrawal:', error);
        res.status(500).json({ error: 'Failed to record money withdrawal' });
    }
};


const cron = require('node-cron');

// Email reminder functions
const generateFinancialReportPDF = async (shop_id, reportData) => {
    return new Promise((resolve, reject) => {
        try {
            const doc = new PDFDocument();
            const filename = `financial-report-${shop_id}-${moment().format('YYYY-MM-DD')}.pdf`;
            const filepath = path.join(__dirname, '../../temp', filename);

            // Ensure temp directory exists
            if (!fs.existsSync(path.dirname(filepath))) {
                fs.mkdirSync(path.dirname(filepath), { recursive: true });
            }

            doc.pipe(fs.createWriteStream(filepath));

            // Add content to PDF
            doc.fontSize(20).text('Financial Report', 100, 100);
            doc.fontSize(14).text(`Shop ID: ${shop_id}`, 100, 130);
            doc.text(`Period: ${reportData.period.start} to ${reportData.period.end}`, 100, 150);

            // Income Statement
            doc.fontSize(16).text('Income Statement', 100, 180);
            doc.fontSize(12)
                .text(`Total Revenue: $${reportData.income_statement.revenue.total_revenue}`, 100, 200)
                .text(`Total COGS: $${reportData.income_statement.cost_of_goods_sold.total_cogs}`, 100, 220)
                .text(`Gross Profit: $${reportData.income_statement.gross_profit.total_gross_profit}`, 100, 240)
                .text(`Net Profit: $${reportData.income_statement.net_profit}`, 100, 260);

            // Balance Sheet
            doc.fontSize(16).text('Balance Sheet', 100, 300);
            doc.fontSize(12)
                .text(`Inventory Value: $${reportData.balance_sheet.assets.inventory.current_inventory_value}`, 100, 320)
                .text(`Total Liabilities: $${reportData.balance_sheet.liabilities.total_liabilities}`, 100, 340)
                .text(`Outstanding Liabilities: $${reportData.balance_sheet.liabilities.outstanding}`, 100, 360);

            doc.end();

            doc.on('end', () => {
                resolve(filepath);
            });

        } catch (error) {
            reject(error);
        }
    });
};

const sendLiabilityReminder = async (shop, liability) => {
    try {
        const subject = `Payment Reminder: ${liability.name} Due Soon`;
        const html = `
            <h2>Payment Reminder</h2>
            <p>Dear ${shop.shop_name},</p>
            <p>This is a reminder that your liability payment is due soon:</p>
            <ul>
                <li><strong>Liability:</strong> ${liability.name}</li>
                <li><strong>Amount:</strong> $${liability.amount}</li>
                <li><strong>Due Date:</strong> ${moment(liability.dueDate).format('YYYY-MM-DD')}</li>
                <li><strong>Description:</strong> ${liability.description || 'N/A'}</li>
            </ul>
            <p>Please ensure payment is made on time to avoid any penalties.</p>
            <p>Best regards,<br>Your POS System</p>
        `;

        await sendEmail(shop.email, subject, html);
    } catch (error) {
        console.error('Error sending liability reminder:', error);
    }
};

const sendMonthlyFinancialReport = async (shop_id) => {
    try {
        const shop = await Shop.findByPk(shop_id);
        if (!shop || !shop.email) {
            console.log(`No email found for shop ${shop_id}`);
            return;
        }

        const startOfMonth = moment().subtract(1, 'month').startOf('month').toDate();
        const endOfMonth = moment().subtract(1, 'month').endOf('month').toDate();

        // Get financial data for the previous month
        const reportData = await getFinancialReportData(shop_id, startOfMonth, endOfMonth);

        // Generate PDF
        const pdfPath = await generateFinancialReportPDF(shop_id, reportData);

        const subject = `Monthly Financial Report - ${moment().subtract(1, 'month').format('MMMM YYYY')}`;
        const html = `
            <h2>Monthly Financial Report</h2>
            <p>Dear ${shop.shop_name},</p>
            <p>Please find attached your financial report for ${moment().subtract(1, 'month').format('MMMM YYYY')}.</p>

            <h3>Summary:</h3>
            <ul>
                <li><strong>Total Revenue:</strong> $${reportData.income_statement.revenue.total_revenue}</li>
                <li><strong>Total Expenses:</strong> $${reportData.income_statement.expenses.total_expenses}</li>
                <li><strong>Net Profit:</strong> $${reportData.income_statement.net_profit}</li>
                <li><strong>Inventory Value:</strong> $${reportData.balance_sheet.assets.inventory.current_inventory_value}</li>
            </ul>

            <p>The detailed report is attached as a PDF.</p>
            <p>Best regards,<br>Your POS System</p>
        `;

        // Note: You'll need to modify sendEmail to support attachments
        await sendEmail(shop.email, subject, html);

        // Clean up PDF file
        fs.unlinkSync(pdfPath);

    } catch (error) {
        console.error('Error sending monthly financial report:', error);
    }
};

// Helper function to get financial report data
const getFinancialReportData = async (shop_id, startDate, endDate) => {
    // This is a simplified version - you can expand this to match the full getFinancialReports function
    const transactionsByType = await Transaction.findAll({
        where: {
            shop_id,
            trans_status: 15,
            updated_at: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            'trans_type',
            [sequelize.fn('SUM', sequelize.col('trans_total')), 'total_amount']
        ],
        group: ['trans_type'],
        raw: true
    });

    const expenses = await Expense.findAll({
        where: {
            shop_id,
            date: { [Op.between]: [startDate, endDate] }
        },
        attributes: [
            [sequelize.fn('SUM', sequelize.col('amount')), 'total_expenses']
        ],
        raw: true
    });

    const totalRevenue = transactionsByType.reduce((sum, t) => sum + parseFloat(t.total_amount || 0), 0);
    const totalExpenses = expenses[0]?.total_expenses || 0;

    return {
        period: { start: startDate, end: endDate },
        income_statement: {
            revenue: { total_revenue: totalRevenue },
            expenses: { total_expenses: totalExpenses },
            net_profit: totalRevenue - totalExpenses,
            cost_of_goods_sold: { total_cogs: 0 },
            gross_profit: { total_gross_profit: totalRevenue }
        },
        balance_sheet: {
            assets: { inventory: { current_inventory_value: 0 } },
            liabilities: { total_liabilities: 0, outstanding: 0 }
        }
    };
};

// Enhanced Cron Jobs for Financial Summary Updates
// 🔧 Conditional Cron Jobs Setup
// Only run cron jobs in development or non-Vercel environments
// In production/Vercel, these will be handled by Vercel Cron Jobs

if (process.env.NODE_ENV !== 'production' || !process.env.VERCEL) {
    console.log('🔄 Setting up local financial cron jobs...');

    // Run every hour to update financial summaries
    cron.schedule('0 * * * *', async () => {
    // cron.schedule('* * * * *', async () => {
        try {
            console.log('🕐 Running hourly financial summary update...');
            const shops = await Shop.findAll();
            for (const shop of shops) {
                await updateHourlyFinancialSummary(shop.shop_id);
                console.log(`📊 Updated hourly summary for shop ${shop.shop_id}`);
            }
            console.log('✅ Hourly financial summary update completed');
        } catch (error) {
            console.error('❌ Hourly financial summary cron job error:', error);
        }
    });

    // Run daily at midnight for comprehensive daily summaries
    cron.schedule('0 0 * * *', async () => {
        try {
            console.log('🌙 Running daily financial summary update...');
            const shops = await Shop.findAll();
            for (const shop of shops) {
                await updateDailyFinancialSummary(shop.shop_id);
                console.log(`📅 Updated daily summary for shop ${shop.shop_id} - ${new Date().toISOString().split('T')[0]}`);
                await checkLiabilityReminders(shop.shop_id);
            }
            console.log('✅ Daily financial summary update completed');
        } catch (error) {
            console.error('❌ Daily financial summary cron job error:', error);
        }
    });

    // Run weekly on Sundays at 6 AM
    cron.schedule('0 6 * * 0', async () => {
        try {
            console.log('📊 Running weekly financial summary update...');
            const shops = await Shop.findAll();
            for (const shop of shops) {
                await updateWeeklyFinancialSummary(shop.shop_id);
                console.log(`📈 Updated weekly summary for shop ${shop.shop_id}`);
            }
            console.log('✅ Weekly financial summary update completed');
        } catch (error) {
            console.error('❌ Weekly financial summary cron job error:', error);
        }
    });

    // Run monthly on the 1st at 9 AM
    cron.schedule('0 9 1 * *', async () => {
        try {
            console.log('📅 Running monthly financial summary update...');
            const shops = await Shop.findAll();
            for (const shop of shops) {
                await updateMonthlyFinancialSummary(shop.shop_id);
                await sendMonthlyFinancialReport(shop.shop_id);
                console.log(`📊 Updated monthly summary for shop ${shop.shop_id}`);
            }
            console.log('✅ Monthly financial summary update completed');
        } catch (error) {
            console.error('❌ Monthly financial summary cron job error:', error);
        }
    });

    // Run at 2 AM daily to clean up old temporary files
    cron.schedule('0 2 * * *', async () => {
        try {
            console.log('🧹 Running cleanup tasks...');
            await cleanupTempFiles();
            console.log('✅ Cleanup tasks completed');
        } catch (error) {
            console.error('❌ Cleanup cron job error:', error);
        }
    });

    console.log('✅ Local financial cron jobs initialized');
} else {
    console.log('🚀 Running in production/Vercel - cron jobs handled by Vercel Cron');
}

const checkLiabilityReminders = async (shop_id) => {
    try {
        const shop = await Shop.findByPk(shop_id);
        if (!shop) return;
        const upcomingDue = moment().add(7, 'days').toDate();
        const liabilities = await Liability.findAll({
            where: {
                shop_id,
                dueDate: { [Op.lte]: upcomingDue },
                status: { [Op.ne]: 2 } 
            }
        });

        for (const liability of liabilities) {
            await sendLiabilityReminder(shop, liability);
        }
    } catch (error) {
        console.error('Error checking liability reminders:', error);
    }
};

// Comprehensive Financial Summary Update Functions

// Hourly update - for real-time tracking
async function updateHourlyFinancialSummary(shop_id) {
    try {
        const now = moment();
        const startOfDay = now.clone().startOf('day').toDate();
        const currentTime = now.toDate();
        const financialData = await getComprehensiveFinancialData(shop_id, startOfDay, currentTime);

        const [summary, created] = await FinancialSummary.findOrCreate({
            where: { shop_id, date: startOfDay },
            defaults: {
                shop_id,
                date: startOfDay,
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            }
        });

        if (!created) {
            // Update existing record
            await summary.update({
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            });
        }

        console.log(`📊 Updated hourly summary for shop ${shop_id}`);
    } catch (error) {
        console.error(`Error updating hourly financial summary for shop ${shop_id}:`, error);
    }
}

// Daily update - comprehensive end-of-day summary
async function updateDailyFinancialSummary(shop_id) {
    try {
        const yesterday = moment().subtract(1, 'day');
        const startOfDay = yesterday.clone().startOf('day').toDate();
        const endOfDay = yesterday.clone().endOf('day').toDate();

        const financialData = await getComprehensiveFinancialData(shop_id, startOfDay, endOfDay);

        const [summary, created] = await FinancialSummary.findOrCreate({
            where: { shop_id, date: startOfDay },
            defaults: {
                shop_id,
                date: startOfDay,
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            }
        });

        if (!created) {
            await summary.update({
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            });
        }

        console.log(`📅 Updated daily summary for shop ${shop_id} - ${yesterday.format('YYYY-MM-DD')}`);
    } catch (error) {
        console.error(`Error updating daily financial summary for shop ${shop_id}:`, error);
    }
}

// Weekly update - aggregate weekly data
async function updateWeeklyFinancialSummary(shop_id) {
    try {
        const lastWeek = moment().subtract(1, 'week');
        const startOfWeek = lastWeek.clone().startOf('week').toDate();
        const endOfWeek = lastWeek.clone().endOf('week').toDate();

        const financialData = await getComprehensiveFinancialData(shop_id, startOfWeek, endOfWeek);

        // Create a weekly summary entry (using Monday as the date)
        const [summary, created] = await FinancialSummary.findOrCreate({
            where: { shop_id, date: startOfWeek },
            defaults: {
                shop_id,
                date: startOfWeek,
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            }
        });

        if (!created) {
            await summary.update({
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            });
        }

        console.log(`📊 Updated weekly summary for shop ${shop_id} - Week of ${lastWeek.format('YYYY-MM-DD')}`);
    } catch (error) {
        console.error(`Error updating weekly financial summary for shop ${shop_id}:`, error);
    }
}

// Monthly update - comprehensive monthly summary
async function updateMonthlyFinancialSummary(shop_id) {
    try {
        const lastMonth = moment().subtract(1, 'month');
        const startOfMonth = lastMonth.clone().startOf('month').toDate();
        const endOfMonth = lastMonth.clone().endOf('month').toDate();

        const financialData = await getComprehensiveFinancialData(shop_id, startOfMonth, endOfMonth);

        const [summary, created] = await FinancialSummary.findOrCreate({
            where: { shop_id, date: startOfMonth },
            defaults: {
                shop_id,
                date: startOfMonth,
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            }
        });

        if (!created) {
            await summary.update({
                totalSales: financialData.totalSales,
                totalPurchases: financialData.totalPurchases,
                totalExpenses: financialData.totalExpenses,
                totalLiabilities: financialData.totalLiabilities,
                netProfit: financialData.netProfit,
                cashIn: financialData.cashIn,
                cashOut: financialData.cashOut,
                netCashFlow: financialData.netCashFlow
            });
        }

        console.log(`📊 Updated monthly summary for shop ${shop_id} - ${lastMonth.format('YYYY-MM')}`);
    } catch (error) {
        console.error(`Error updating monthly financial summary for shop ${shop_id}:`, error);
    }
}

// Comprehensive financial data calculation
async function getComprehensiveFinancialData(shop_id, startDate, endDate) {
    try {
        // Get all transactions by type
        const transactions = await Transaction.findAll({
            where: {
                shop_id,
                trans_status: 15,
                updated_at: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                'trans_type',
                [sequelize.fn('SUM', sequelize.col('trans_total')), 'total_amount'],
                [sequelize.fn('COUNT', sequelize.col('trans_id')), 'count']
            ],
            group: ['trans_type'],
            raw: true
        });

        // Calculate total sales from all transaction types
        const totalSales = transactions.reduce((sum, t) => {
            return sum + parseFloat(t.total_amount || 0);
        }, 0);

        // Get supply purchases
        const supplyPurchases = await SupplyItem.findAll({
            where: {
                shop_id,
                supply_date: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('amount_paid')), 'total_purchases']
            ],
            raw: true
        });

        // Get expenses
        const expenses = await Expense.findAll({
            where: {
                shop_id,
                date: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('amount')), 'total_expenses']
            ],
            raw: true
        });

        // Get liabilities created in this period
        const liabilities = await Liability.findAll({
            where: {
                shop_id,
                created_at: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('amount')), 'total_liabilities']
            ],
            raw: true
        });

        // Get liability payments made in this period
        const liabilityPayments = await LiabilityPayment.findAll({
            where: {
                shop_id,
                paymentDate: { [Op.between]: [startDate, endDate] }
            },
            attributes: [
                [sequelize.fn('SUM', sequelize.col('amount')), 'total_payments']
            ],
            raw: true
        });

        const totalPurchases = parseFloat(supplyPurchases[0]?.total_purchases || 0);
        const totalExpenses = parseFloat(expenses[0]?.total_expenses || 0);
        const totalLiabilities = parseFloat(liabilities[0]?.total_liabilities || 0);
        const totalLiabilityPayments = parseFloat(liabilityPayments[0]?.total_payments || 0);

        // Calculate cash flows
        const cashIn = totalSales;
        const cashOut = totalExpenses + totalLiabilityPayments;
        const netCashFlow = cashIn - cashOut;
        const netProfit = totalSales - totalPurchases - totalExpenses;

        return {
            totalSales,
            totalPurchases,
            totalExpenses,
            totalLiabilities,
            netProfit,
            cashIn,
            cashOut,
            netCashFlow,
            transactionBreakdown: transactions,
            liabilityPayments: totalLiabilityPayments
        };
    } catch (error) {
        console.error('Error calculating comprehensive financial data:', error);
        return {
            totalSales: 0,
            totalPurchases: 0,
            totalExpenses: 0,
            totalLiabilities: 0,
            netProfit: 0,
            cashIn: 0,
            cashOut: 0,
            netCashFlow: 0
        };
    }
}

// Cleanup function for temporary files
async function cleanupTempFiles() {
    try {
        const tempDir = path.join(__dirname, '../../temp');
        if (fs.existsSync(tempDir)) {
            const files = fs.readdirSync(tempDir);
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

            for (const file of files) {
                const filePath = path.join(tempDir, file);
                const stats = fs.statSync(filePath);

                if (now - stats.mtime.getTime() > maxAge) {
                    fs.unlinkSync(filePath);
                    console.log(`🗑️ Deleted old temp file: ${file}`);
                }
            }
        }
    } catch (error) {
        console.error('Error cleaning up temp files:', error);
    }
}

// Manual trigger functions for financial summary updates
exports.triggerFinancialSummaryUpdate = async (req, res) => {
    try {
        const { shop_id, period } = req.body;

        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        let updateFunction;
        switch (period) {
            case 'hourly':
                updateFunction = updateHourlyFinancialSummary;
                break;
            case 'daily':
                updateFunction = updateDailyFinancialSummary;
                break;
            case 'weekly':
                updateFunction = updateWeeklyFinancialSummary;
                break;
            case 'monthly':
                updateFunction = updateMonthlyFinancialSummary;
                break;
            default:
                updateFunction = updateDailyFinancialSummary;
        }

        await updateFunction(shop_id);

        res.json({
            success: true,
            message: `${period || 'daily'} financial summary updated successfully for shop ${shop_id}`
        });
    } catch (error) {
        console.error('Error triggering financial summary update:', error);
        res.status(500).json({ error: 'Failed to update financial summary' });
    }
};

// Get financial summary data
exports.getFinancialSummary = async (req, res) => {
    try {
        const { shop_id, startDate, endDate, limit } = req.query;

        if (!shop_id) {
            return res.status(400).json({ error: 'Shop ID is required' });
        }

        let whereClause = { shop_id };

        if (startDate && endDate) {
            whereClause.date = {
                [Op.between]: [new Date(startDate), new Date(endDate)]
            };
        }

        const summaries = await FinancialSummary.findAll({
            where: whereClause,
            order: [['date', 'DESC']],
            limit: limit ? parseInt(limit) : 30
        });

        // Calculate totals
        const totals = summaries.reduce((acc, summary) => {
            acc.totalSales += parseFloat(summary.totalSales || 0);
            acc.totalPurchases += parseFloat(summary.totalPurchases || 0);
            acc.totalExpenses += parseFloat(summary.totalExpenses || 0);
            acc.totalLiabilities += parseFloat(summary.totalLiabilities || 0);
            acc.netProfit += parseFloat(summary.netProfit || 0);
            acc.cashIn += parseFloat(summary.cashIn || 0);
            acc.cashOut += parseFloat(summary.cashOut || 0);
            acc.netCashFlow += parseFloat(summary.netCashFlow || 0);
            return acc;
        }, {
            totalSales: 0,
            totalPurchases: 0,
            totalExpenses: 0,
            totalLiabilities: 0,
            netProfit: 0,
            cashIn: 0,
            cashOut: 0,
            netCashFlow: 0
        });

        res.json({
            success: true,
            data: {
                summaries,
                totals,
                count: summaries.length
            }
        });
    } catch (error) {
        console.error('Error getting financial summary:', error);
        res.status(500).json({ error: 'Failed to get financial summary' });
    }
};

// Bulk update all shops' financial summaries
exports.bulkUpdateFinancialSummaries = async (req, res) => {
    try {
        const { period } = req.body;

        console.log(`🔄 Starting bulk ${period || 'daily'} financial summary update...`);

        const shops = await Shop.findAll();
        const results = [];

        for (const shop of shops) {
            try {
                let updateFunction;
                switch (period) {
                    case 'hourly':
                        updateFunction = updateHourlyFinancialSummary;
                        break;
                    case 'daily':
                        updateFunction = updateDailyFinancialSummary;
                        break;
                    case 'weekly':
                        updateFunction = updateWeeklyFinancialSummary;
                        break;
                    case 'monthly':
                        updateFunction = updateMonthlyFinancialSummary;
                        break;
                    default:
                        updateFunction = updateDailyFinancialSummary;
                }

                await updateFunction(shop.shop_id);
                results.push({ shop_id: shop.shop_id, status: 'success' });
            } catch (error) {
                console.error(`Error updating summary for shop ${shop.shop_id}:`, error);
                results.push({ shop_id: shop.shop_id, status: 'error', error: error.message });
            }
        }

        const successCount = results.filter(r => r.status == 'success').length;
        const errorCount = results.filter(r => r.status == 'error').length;

        console.log(`✅ Bulk update completed: ${successCount} success, ${errorCount} errors`);

        res.json({
            success: true,
            message: `Bulk ${period || 'daily'} update completed`,
            results: {
                total: results.length,
                success: successCount,
                errors: errorCount,
                details: results
            }
        });
    } catch (error) {
        console.error('Error in bulk financial summary update:', error);
        res.status(500).json({ error: 'Failed to perform bulk update' });
    }
};