const {sequelize, Transaction, Receipt,User, Status, Shop,Paymentmethod,Paymenttransaction,MpesaConfirmation} = require('../../db/models');
const genericController = require('./_genericController');
// const darajaApi = require('../../services/darajaApi');
// const stripeService = require('../../services/stripeService');
const { fn, col } = require('sequelize');
const{formatPhoneNumber} =require('../../utils/utils')
const { Op } = require('sequelize')
const { v4: uuidv4 } = require("uuid");

const{initiateSTKPush,initiateConfriamtion } = require('../../utils/daraja')
exports.getAllTransaction = genericController.getAll(Transaction);
exports.deleteTransaction = genericController.delete(Transaction, 'trans_id');
exports.updateTrans = genericController.updateMany(Transaction, 'trans_id');

exports.getTransactionById = async (req, res) => {
  try {
    const { id } = req.params;
    const { username } = req.query;
    const transaction = await Transaction.findOne({
      where: { trans_id: id }
    });

    if (transaction) {
      return res.json(transaction);
    }
    if (username) {
      const olderTransaction = await Transaction.findOne({
        where: {
          trans_status: 0,
          username: username
        },
        order: [['created_at', 'ASC']]
      });

      if (olderTransaction) {
        return res.json(olderTransaction);
      }
    }

    return res.status(404).json({ error: 'Transaction not found' });
  } catch (error) {
    return res.status(500).json({ error: 'Internal server error' });
  }
};


exports.getTransactionByIdLimit = async (req, res) => {
  try {
    const { username } = req.query;
    if (!username) {
      return res.status(400).json({ error: "Username is required" });
    }

    const transaction = await Transaction.findOne({
      where: { username },
      order: [['created_at', 'ASC']],
      include: [
        {
          model: Status,
          as: 'status',
          attributes: ['status_id', 'status_name', 'status_description'],
        },
        {
          model: Receipt,
          as: 'receipts',
        },
        {
          model: User,
          as: 'user',
          attributes: ['first_names', 'last_names', 'email'],
        },
        {
          model: Paymenttransaction,
          as: 'payments',
          include: [
            {
              model: Paymentmethod,
              as: 'paymentMethod',
            }
          ]
        },
      ]
    });

    if (!transaction) {
      return res.status(404).json({ error: "No transaction found for this username" });
    }

    res.status(200).json({
      data: transaction
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Internal server error" });
  }
};

exports.createTransaction = async (req, res) => {
  try {
    const { username,shop_id } = req.body;
    const existingTransaction = await Transaction.findOne({where: {trans_status: 0, username: username,shop_id:shop_id}});
    if (existingTransaction) {
      return res.status(200).json(existingTransaction);
    }
    const newTransaction = await Transaction.create({
      ...req.body,
      trans_type: 'SALE',
      income_type:'income',
      trans_status: 0,
    });
    return res.status(201).json(newTransaction);
  } catch (error) {
    return res.status(500).json({
      message: "An error occurred while processing the transaction.",
      error: error.message,
    });
  }
};

exports.createExpenseTransaction = async (req, res) => {
  try {
    const {
      shop_id,
      username,
      trans_total,
      trans_description,
      expense_category,
      expense_details
    } = req.body;

    // Validate required fields
    if (!shop_id || !username || !trans_total) {
      return res.status(400).json({
        message: "Missing required fields: shop_id, username, and trans_total are required",
        error: "Validation failed"
      });
    }

    // Validate trans_total is a positive number
    if (isNaN(trans_total) || parseFloat(trans_total) <= 0) {
      return res.status(400).json({
        message: "trans_total must be a positive number",
        error: "Validation failed"
      });
    }

    const newExpenseTransaction = await Transaction.create({
      shop_id,
      username,
      trans_total: parseFloat(trans_total),
      trans_description: trans_description || 'Expense Transaction',
      trans_type: 'EXPENSE',
      income_type: 'expense',
      trans_status: 22, 
      expense_category: expense_category || null,
      expense_details: expense_details || null,
      trans_quantity: 1,
      trans_tax: 0,
      trans_net: parseFloat(trans_total),
      trans_discount: 0,
      
      created_at: new Date(),
      updated_at: new Date()
    });

    return res.status(201).json({
      success: true,
      message: "Expense transaction created successfully",
      data: newExpenseTransaction
    });

  } catch (error) {
    console.error("Error creating expense transaction:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while creating the expense transaction.",
      error: error.message,
    });
  }
};

exports.createIncomeTransaction = async (req, res) => {
  try {
    const {
      shop_id,
      username,
      trans_total,
      trans_description,
      income_source,
      income_details
    } = req.body;

    // Validate required fields
    if (!shop_id || !username || !trans_total) {
      return res.status(400).json({
        message: "Missing required fields: shop_id, username, and trans_total are required",
        error: "Validation failed"
      });
    }

    // Validate trans_total is a positive number
    if (isNaN(trans_total) || parseFloat(trans_total) <= 0) {
      return res.status(400).json({
        message: "trans_total must be a positive number",
        error: "Validation failed"
      });
    }

    const newIncomeTransaction = await Transaction.create({
      shop_id,
      username,
      trans_total: parseFloat(trans_total),
      trans_description: trans_description || 'Income Transaction',
      trans_type: 'INCOME',
      income_type: 'income',
      trans_status: 23, // Income status
      income_source: income_source || null,
      income_details: income_details || null,
      trans_quantity: 1,
      trans_tax: 0,
      trans_net: parseFloat(trans_total),
      trans_discount: 0,
      created_at: new Date(),
      updated_at: new Date()
    });

    return res.status(201).json({
      success: true,
      message: "Income transaction created successfully",
      data: newIncomeTransaction
    });

  } catch (error) {
    console.error("Error creating income transaction:", error);
    return res.status(500).json({
      success: false,
      message: "An error occurred while creating the income transaction.",
      error: error.message,
    });
  }
};

exports.updateTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { paymentMethod, phoneNumber, trans_status, trans_total, trans_quantity, trans_tax, trans_net, trans_discount } = req.body;
    let paymentResponses = [];
    let totalPaid = 0;
    const transaction = await Transaction.findByPk(id);
    if (!transaction) {
      return res.status(404).json({ message: "Transaction not found" });
    }
    if (!Array.isArray(paymentMethod) || paymentMethod.length == 0) {
      return res.status(400).json({ message: "At least one payment method is required" });
    }

    for (const { paymentMethodId, amount } of paymentMethod) {
          const payment_type = await Paymentmethod.findByPk(paymentMethodId);
          if (!payment_type) {
            return res.status(404).json({ message: `Payment method ${paymentMethodId} not found` });
          }
          totalPaid += amount;
          const paymentResponse = { status: "success", message: "Payment Processed", amount };
          paymentResponses.push(paymentResponse);

          await Paymenttransaction.create({
            trans_id: id,
            payment_method_id: paymentMethodId,
            payment_amount: amount,
            shop_id: transaction.dataValues.shop_id,
            payment_phone: phoneNumber ?? null,
            payment_response: paymentResponse ?? null,
          });
    }


    const newStatus = totalPaid >= trans_total ? "Paid" : "Partially Paid";
    await Transaction.update(
      { trans_status: newStatus, trans_total, trans_quantity, trans_tax, trans_net, trans_discount,trans_status },
      { where: { trans_id: id } }
    );
    return res.status(200).json({
      message: newStatus == "Paid" ? "Transaction fully paid" : "Partial payment received",
      transaction,
      totalPaid,
      remainingBalance: trans_total - totalPaid > 0 ? trans_total - totalPaid : 0,
      paymentResponses,
    });
  } catch (error) {
    console.error("Error updating transaction:", error);
    return res.status(500).json({ message: "Internal server error", error: error.message });
  }
};

exports.getAllMpesa = async (req, res) => {
  try {
    const { query,page = 1, pageSize = 10 } = req.query;
    const { 
      business_shortcode, 
      trans_amount, 
      bill_ref_number, 
      trans_id, 
      msisdn, 
      first_name, 
      middle_name, 
      last_name
    } = query;

    const filters = {};

    const applyFilter = (field, value) => {
      if (Array.isArray(value)) {
        return { [Op.or]: value.map(v => ({ [Op.like]: `%${v}%` })) };
      }
      return { [Op.like]: `%${value}%` };
    };

    if (business_shortcode) filters.business_shortcode = business_shortcode;
    if (trans_amount) filters.trans_amount = parseFloat(trans_amount);
    if (bill_ref_number) filters.bill_ref_number = applyFilter("bill_ref_number", bill_ref_number);
    if (trans_id) filters.trans_id = applyFilter("trans_id", trans_id);
    if (msisdn) filters.msisdn = applyFilter("msisdn", msisdn);
    if (first_name) filters["payment_response.FirstName"] = applyFilter("payment_response.FirstName", first_name);
    if (middle_name) filters["payment_response.MiddleName"] = applyFilter("payment_response.MiddleName", middle_name);
    if (last_name) filters["payment_response.LastName"] = applyFilter("payment_response.LastName", last_name);

    const limit = parseInt(pageSize, 10);
    const offset = (parseInt(page, 10) - 1) * limit;

    const { count, rows: transactions } = await MpesaConfirmation.findAndCountAll({
      where: filters,
      order: [["transaction_time", "DESC"]],
      limit,
      offset,
    });

    res.json({
      success: true,
      transactions,
      totalRecords: count,
      currentPage: parseInt(page, 10),
      totalPages: Math.ceil(count / limit),
    });
  } catch (error) {
    console.error("Error fetching M-Pesa transactions:", error);
    res.status(500).json({ success: false, error: error.message });
  }
};

exports.handleMpesaConfirmation=async(req,res)=>{
  const short_code = req.params.id
  try {
    paymentResponse = await initiateConfriamtion(short_code);
    if (!paymentResponse.success) {
      return res.status(400).json({ message: "M-Pesa payment failed", error: paymentResponse.error });
    }
    // await inputMpesaConfirmation(paymentResponse.data); 
    res.json({
      ResultCode: 0,
      ResultDesc: "Success",
    });
} catch (error) {
  console.error("Error handling M-Pesa confirmation:", error);
  res.status(500).json({
    ResultCode: 1,
    ResultDesc: "payment callback failed",
  });
}
}
exports.inputMpesaConfirmation = async(req,res) => {
  const paymentResponse =req.body
  try {
    await MpesaConfirmation.create({
      confirmation_id:uuidv4(),
      trans_id: paymentResponse.TransID,
      business_shortcode: paymentResponse.BusinessShortCode,
      trans_amount: parseFloat(paymentResponse.TransAmount),
      bill_ref_number: paymentResponse.BillRefNumber ?? null,
      invoice_number: paymentResponse.InvoiceNumber ?? null,
      msisdn: paymentResponse.MSISDN ?? null,
      transaction_time: new Date(paymentResponse.TransTime),
      payment_response: paymentResponse,
    });

    res.json({
      ResultCode: 0,
      ResultDesc: "Success",
    });
  } catch (error) {
   res.status(500).json({ResultCode: 1,ResultDesc: "Failed to process payment",});
  }
};

exports.handlePaymentTransaction = async (req, res) => {
  try {
    const {trans_id, paymentMethodId, phoneNumber, trans_status, trans_total } = req.body;
    
    const amount = trans_total;
    const transaction = await Transaction.findByPk(trans_id);
    if (!transaction) {
      return res.status(404).json({ message: "Transaction not found" });
    }
    const paymentMethod = await Paymentmethod.findByPk(paymentMethodId);
    if (!paymentMethod) {
      return res.status(404).json({ message: "Payment method not found" });
    }
    const paymentMethodName = paymentMethod.dataValues.payment_method_name?.toLowerCase();
    let paymentResponse = null;
    const formattedPhoneNumber = phoneNumber && formatPhoneNumber(phoneNumber)
    switch (paymentMethodName) {
      case "mpesa_express":
        const businessShortCode = paymentMethod.dataValues.payment_number;
        paymentResponse = await initiateSTKPush(formattedPhoneNumber, amount, trans_id, businessShortCode, "CustomerPayBillOnline");
        if (!paymentResponse.success) {
          return res.status(400).json({ message: "M-Pesa STK Push failed", error: paymentResponse.error });
        }
        await Transaction.update(
          { trans_status, payment_response: paymentResponse.data },
          { where: { trans_id: trans_id } }
        );
        break;
      default:
        return res.status(400).json({ message: "Invalid payment method" });
    }
    return res.status(200).json({
      message: "Payment processing",
      paymentResponse,
    });
  } catch (error) {
    console.error("Error handling payment transaction:", error);
    return res.status(500).json({ message: "Internal server error", error: error.message });
  }
};



exports.getreceiptByShop = async (req, res) => {
    try {
      const { trans_status,username,shop_id} =req.query.query
      const {page = 1, pageSize = 10,query} = req.query;
      const searchQuery = typeof query == 'string' ? query : '';
      
      let whereConditions = {};

      if (searchQuery) {
          whereConditions[Op.or] = [
            { email: { [Op.iLike]: `%${searchQuery}%` } },
            { first_names: { [Op.iLike]: `%${searchQuery}%` } },
            { last_names: { [Op.iLike]: `%${searchQuery}%` } },
            { username: { [Op.iLike]: `%${searchQuery}%` } }
          ];
      }
      if (shop_id || username || trans_status) {
        whereConditions = {
            ...whereConditions,
            [Op.and]: [],
        };
        if (shop_id) {
            whereConditions[Op.and].push(
                sequelize.where(sequelize.fn("LOWER", sequelize.col("transactions.shop_id")), shop_id.toLowerCase())
            );
        }

        if (username) {
          const disallowedStatuses = [15, 19, 20];
          const isDisallowedStatus = Array.isArray(trans_status)
            ? trans_status.some(status => disallowedStatuses.includes(Number(status)))
            : disallowedStatuses.includes(Number(trans_status));
        
          if (!isDisallowedStatus) {
            whereConditions[Op.and].push(
              sequelize.where(
                sequelize.fn("LOWER", sequelize.col("transactions.username")),
                username.toLowerCase()
              )
            );
          }
        }
        
        if (trans_status !== undefined && trans_status !== null) {
            // whereConditions[Op.and].push(
            //     sequelize.where(sequelize.col("transactions.trans_status"), trans_status)
            // );
            if (Array.isArray(trans_status)) {
              whereConditions[Op.and].push({
                  trans_status: {
                      [Op.in]: trans_status
                  }
              });
          } else {
              whereConditions[Op.and].push({
                  trans_status: trans_status
              });
          }
        }
        if (whereConditions[Op.and].length == 0) {
            delete whereConditions[Op.and];
        }
    }
    
    const count = await Transaction.count({
        where: whereConditions,
        distinct: true,
        include: [
          {
            model: User,
            as: 'user',
            attributes: [],
          }
        ]
      });

      const rows = await Transaction.findAll({
        where: whereConditions,
        distinct: true,
        include: [
          { 
            model: Status, 
            as: 'status', 
            attributes: [
              'status_id', 
              'status_name',
              [
                sequelize.literal(`
                  CASE 
                    WHEN transactions.trans_status = 0 THEN
                      CASE
                        WHEN transactions.reversed = true THEN 'Transaction Reversed'
                        WHEN transactions.trans_type = 'ORDER' THEN 'order closure pending'
                        WHEN transactions.trans_type = 'SUPPLY' THEN 'supply payment pending'
                        ELSE status.status_description
                      END
                    ELSE status.status_description
                  END
                `),
                'status_description'
              ]
            ], 
          },
          {
            model: Receipt,
            as: 'receipts',
          },
          {
            model: User,
            as: 'user',
            attributes: ['first_names', 'last_names','email','username'],
          },
          {
            model: User,
            as: 'allocatedUser',
            attributes: ['first_names', 'last_names', 'email'],
            required: false,
            where: sequelize.literal('transactions.order_allocation IS NOT NULL'),
            foreignKey: 'user_id'
          },
          {
            model: Paymenttransaction,
            as: 'payments',
            include: [
              {
                model: Paymentmethod,
                as: 'paymentMethod', 
              }
            ]
          },
        ],
        order: [['created_at', 'DESC']],
        limit: Number(pageSize),
        offset: (Number(page) - 1) * Number(pageSize),
      });

      const total = count;
      const totalPages = Math.ceil(total / pageSize);
      res.status(200).json({
        data: rows,
        metadata: {
          total,
          page: Number(page),
          limit: Number(pageSize),
          totalPages,
        },
      });
    } catch (error) { 
        console.log(error);
        res.status(500).json({ error: "Internal server error" });
    }
  };
  