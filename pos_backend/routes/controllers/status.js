const {Status}  = require('../../db/models');
const genericController = require('./_genericController');

exports.getAllStatus = genericController.getAll(Status);
exports.getStatusById = genericController.getById(Status, 'status_id');
exports.createStatus = genericController.create(Status);
exports.updateStatus = genericController.update(Status, 'status_id');
exports.deleteStatus = genericController.delete(Status, 'status_id');
