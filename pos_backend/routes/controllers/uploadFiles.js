const csv = require('csv-parser');
const XLSX = require('xlsx');
const { Item }  = require('../../db/models');
const multer = require('multer');
const streamifier = require('streamifier');

// Multer configuration for file upload
const storage = multer.memoryStorage();
const uploadx = multer({ storage: storage, limits: { fileSize: 500 * 1024 * 1024 }});

/**
 * Parse CSV file buffer into JSON rows
 * @param {Buffer} fileBuffer - The CSV file buffer
 * @returns {Promise<Array>} - A promise that resolves to an array of JSON rows
 */
const parseCSV = (fileBuffer) => {
  return new Promise((resolve, reject) => {
    const results = [];
    streamifier.createReadStream(fileBuffer)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) =>{console.log(error);reject(`Error reading CSV: ${error}`)});
  });
};

/**
 * Parse Excel file buffer into JSON rows
 * @param {Buffer} fileBuffer - The Excel file buffer
 * @returns {Promise<Array>} - A promise that resolves to an array of JSON rows
 */
const parseExcel = (fileBuffer) => {
  return new Promise((resolve, reject) => {
    try {
      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0]; // Use the first sheet
      const sheet = workbook.Sheets[sheetName];
      const rows = XLSX.utils.sheet_to_json(sheet); // Convert sheet to JSON
      resolve(rows);
    } catch (error) {
      console.log(error);
      reject(`Error reading Excel file: ${error}`);
    }
  });
};

/**
 * Create items from JSON rows
 * @param {Array} rows - Array of JSON rows
 * @param {string} shopId - The shop ID
 * @returns {Promise<Array>} - A promise that resolves to an array of created items
 */

    const createItemsFromRows = async (rows, shopId) => {
        const items = [];
        for (const row of rows) {
          let itemData = {};
          if (row.item_name) {
            // Map data using headers
            itemData = {
              item_name: row.item_name || '',
              item_selling: parseFloat(row.item_selling) || 0,
              item_quantity: parseInt(row.item_quantity) || 0,
              item_availability: 1,
              item_model_no: row.item_model_no || '',
              item_description: row.item_description || '',
              item_buying: parseFloat(row.item_buying) || 0,
              item_pic_url: [],
              shop_id: shopId,
            //   item_cat_id: 'CAT1015',
            };
          } else {
            const keys = Object.keys(row);
            itemData = {
                item_name: row[keys[3]] || '',
                item_selling: parseFloat(row[keys[2]]) || 0,
                item_quantity: parseInt(row[keys[1]]) || 0,
                item_availability: 1,
                item_model_no: row[keys[4]] || '',
                item_description: row[keys[0]] || '',
                item_buying: parseFloat(row[keys[5]]) || 0,
                item_pic_url: [],
                shop_id: shopId,
                // item_cat_id: 'CAT1015',  
            };
          }
          console.log("----here-----");
         if(itemData.item_name != ''){
            await Item.create(itemData)
         }
       
          items.push(itemData);
        }
        return items;
      };


  

/**
 * Handle file upload and process CSV/Excel files
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
const handleFileUpload = async (req, res) => {
  const shopId = req.params.shop_id;
  if (!req.file) {return res.status(400).json({ error: 'No file uploaded' });}
  const fileBuffer = req.file.buffer;
  const fileType = req.file.mimetype;
  try {
    let rows;
    if (fileType === 'text/csv') {
      rows = await parseCSV(fileBuffer);
    } else if (
      fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      fileType === 'application/vnd.ms-excel'
    ) {
      rows = await parseExcel(fileBuffer);
    } else {
      console.log(fileType);
      return res.status(400).json({ error: 'Invalid file type. Only CSV and Excel files are allowed.' });
    }
    const items = await createItemsFromRows(rows, shopId);
    res.status(201).json({ data: items });
  } catch (error) {
    console.log(error);
    res.status(500).json({ error: error.toString() });
  }
};

module.exports = { handleFileUpload, uploadx };