const { Item, Category, Discount, Status, sequelize } = require('../../db/models');
const { Op } = require('sequelize');
// exports.getItems = async (req, res) => {
//   const { page = 1, pageSize, query = ''} = req.query;
//   const offset = (page - 1) * pageSize;

//   try {
//   const searchQuery = typeof query == 'string' ? query : query.query;
//   const shop_id = typeof query == 'object' ? query.shop_id : null
//   let whereClause = {};
//   if (searchQuery) {
//     whereClause[Op.or] = [
//       { item_name: { [Op.iLike]: `%${searchQuery}%` } },
//       { item_description: { [Op.iLike]: `%${searchQuery}%` } },
//       { item_model_no: { [Op.iLike]: `%${searchQuery}%` } },
//       { item_sub_cat: { [Op.iLike]: `%${searchQuery}%` } },
      
//     ];
//   }

//   if (shop_id) {
//     whereClause = {
//       ...whereClause,
//       [Op.and]: [
//         sequelize.where(sequelize.fn("LOWER", sequelize.col("item.shop_id")), shop_id.toLowerCase()),
//       ],
//     };
//   }
    
//     const { rows: items, count: total } = await Item.findAndCountAll({
//       where: whereClause,
//       include: [
//         {
//           model: Category,
//           as: 'Category',
//           attributes: ['category_id', 'category_name'], 
//         },
//         {
//           model: Discount,
//           as: 'Discount',
//           attributes: ['discount_id', 'discount_name','discount_amount'],
//         },
//         {
//           model: Status,
//           as: 'status',
//           attributes: ['status_id', 'status_name','status_description'],
//         },
//       ],
//       order: [['created_at', 'DESC']],
//       limit: Number(pageSize),
//       offset: Number(offset),
//     });

//     // Return response with metadata
//     res.status(200).json({
//       data: items,
//       metadata: {
//         total,
//         page: Number(page),
//         limit: Number(pageSize),
//         totalPages: Math.ceil(total / pageSize),
//       },
//     });
//   } catch (err) {
//     console.error('Error fetching items:', err);
//     res.status(500).json({ error: 'Failed to fetch items' });
//   }
// };
exports.getItems = async (req, res) => {
  const { page = 1, pageSize = 10, query = ''} = req.query;
  const offset = (page - 1) * pageSize;

  try {
  const searchQuery = typeof query == 'string' ? query : query.query;
  const shop_id = typeof query == 'object' ? query.shop_id : null
  let whereClause = {};
  if (searchQuery) {
    whereClause[Op.or] = [
      { item_name: { [Op.iLike]: `%${searchQuery}%` } },
      { item_description: { [Op.iLike]: `%${searchQuery}%` } },
      { item_model_no: { [Op.iLike]: `%${searchQuery}%` } },
      { item_sub_cat: { [Op.iLike]: `%${searchQuery}%` } },
      
    ];
  }

  if (shop_id) {
    whereClause = {
      ...whereClause,
      [Op.and]: [
        sequelize.where(sequelize.fn("LOWER", sequelize.col("item.shop_id")), shop_id.toLowerCase()),
      ],
    };
  }
    
    const { rows: items, count: total } = await Item.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Category,
          as: 'Category',
          attributes: ['category_id', 'category_name'],
        },
        {
          model: Discount,
          as: 'Discount',
          attributes: ['discount_id', 'discount_name', 'discount_amount'],
        },
        {
          model: Status,
          as: 'status',
          attributes: ['status_id', 'status_name', 'status_description'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: Number(pageSize),
      offset: Number(offset),
    });

    // Return response with metadata
    res.status(200).json({
      data: items,
      metadata: {
        total,
        page: Number(page),
        limit: Number(pageSize),
        totalPages: Math.ceil(total / pageSize),
      },
    });

    // res.status(200).json(response);
  } catch (err) {
    console.error('Error fetching items:', err);
    res.status(500).json({ 
      error: 'Failed to fetch items',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
};
/**
 * Create a new item
 */
exports.createItem = async (req, res) => {
  try {
    const newItem = await Item.create(req.body);
    res.status(201).json({ data: newItem });
  } catch (err) {
    console.error('Error creating item:', err);
    res.status(500).json({ error: 'Failed to create item: ' + err.message });
  }
};

/**
 * Get a specific item by ID
 */
exports.getItemById = async (req, res) => {
  const { id } = req.params;

  try {
    const item = await Item.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'Category',
          attributes: ['category_id', 'category_name'], 
        },
        {
          model: Discount,
          as: 'Discount',
          attributes: ['discount_id', 'discount_name', 'discount_amount'],
        },
        {
          model: Status,
          as: 'status',
          attributes: ['status_id', 'status_name', 'status_description'],
        },
      ]
    });

    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    res.status(200).json({ data: item });
  } catch (err) {
    console.error('Error fetching item:', err);
    res.status(500).json({ error: 'Failed to fetch item' });
  }
};

/**
 * Update an item
 */
exports.updateItem = async (req, res) => {
  const { id } = req.params;
  try {
    const [updated] = await Item.update(req.body, { where: { item_id: id } });
    
    if (!updated) {
      return res.status(404).json({ error: 'Item not found' });
    }
    
    const updatedItem = await Item.findByPk(id);
    res.status(200).json({ data: updatedItem });
  } catch (err) {
    console.error('Error updating item:', err);
    res.status(500).json({ error: 'Failed to update item' });
  }
};

/**
 * Delete an item
 */
exports.deleteItem = async (req, res) => {
  const { id } = req.params;
  try {
    const item = await Item.findByPk(id);
    
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }
    
    await Item.destroy({ where: { item_id: id } });
    res.status(200).json({ message: 'Item deleted successfully' });
  } catch (err) {
    console.error('Error deleting item:', err);
    res.status(500).json({ error: 'Failed to delete item' });
  }
};

exports.updateItemQuantity = async (req, res) => {
  const { id } = req.params;
  const { quantity, type_ } = req.body;

  try {
    const item = await Item.findByPk(id);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }
    
    const parsedQuantity = Number(quantity);
    
    if (type_ == 1) {
      if (item.item_quantity < parsedQuantity) {
        return res.status(400).json({ error: 'Insufficient stock' });
      }
      item.item_quantity -= parsedQuantity;
    } else { 
      item.item_quantity += parsedQuantity;
    }
    
    await item.save();
    await item.reload();
    
    return res.status(200).json({ data: item });
  } catch (error) {
    console.error('Error updating item quantity:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};
