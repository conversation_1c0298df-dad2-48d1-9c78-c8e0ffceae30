const { Paymentmethod } = require('../../db/models');
const genericController = require('./_genericController');

exports.getAllPaymentMethod = genericController.getAll(Paymentmethod);
// exports.getPaymentMethodById = genericController.getById(Paymentmethod, 'payment_method_id');
exports.createPaymentMethod = genericController.createMany(Paymentmethod);
exports.updatePaymentMethod = genericController.updateMany(Paymentmethod, 'payment_method_id');
exports.deletePaymentMethod = genericController.delete(Paymentmethod, 'payment_method_id');



exports.getPaymentMethodByShop = async (req, res) => {
  try {
    const { id } = req.params;
    const paymentmethod = await Paymentmethod.findAll({  
      where: { shop_id: id,status:true},
      order: [['created_at', 'DESC']]});
  res.json(paymentmethod);
  } catch (error) {
    console.error("Error fetching paymentmethod by trans_id:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

exports.getreceiptByShop = async (req, res) => {
    try {
      const { id } = req.params;
      const paymentmethod = await Paymentmethod.findAll({  
        where: { shop_id: id,status:true},
        order: [['created_at', 'DESC']]});
    res.json(paymentmethod);
    } catch (error) {
      console.error("Error fetching paymentmethod by trans_id:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  };