const { User,Shop,Role,Usershop,Userrole,sequelize,Status,Transaction}  = require('../../db/models');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { sendEmail } = require('../../utils/emails');
const bcrypt =require('bcrypt')
var jwt = require('jsonwebtoken');

const {getWelcomeEmail, getPasswordResetEmail}=require('./emails');
const transaction = require('../../db/models/transaction');

exports.login = async (req, res) => {
    const { email, password } = req.body;
  
    try {
      const user = await User.findOne({
        where: { email },
        include: [
          {
            model: Role,
            as: 'roles',
            through: {
              attributes: [],
            },
          },
          {
            model: Shop,
            as: 'shops',
            through: { attributes: [] },
            where: { shop_status: 16 },
            required: false
          },
        ],
      });
  
      if (!user) { return res.status(404).json({ error: 'User not found' });}

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) { return res.status(401).json({ error: 'Invalid password' });}

      if (!user.is_verified) {
        return res.status(200).json({
          message: 'Please change your password to verify your account.',
          user_id: user.user_id,
          requires_password_change: true,
        });
      }
      
      if (!user.shops || user.shops.length == 0) {
        return res.status(403).json({ error: 'No active shop available. Contact admin.' });
      }

    const txnWhere = { username: user.username, trans_status: 0 };
    const currentShopId = user.shops?.[0]?.shop_id;

    if (currentShopId) txnWhere.shop_id = currentShopId;
    const transaction = await Transaction.findOne({
      where: txnWhere,
      attributes: ['trans_id'],
      order: [['created_at', 'DESC']],
    });


    const token = jwt.sign(
      { user_id: user.user_id, email: user.email },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );


    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV == 'production',
      sameSite: process.env.NODE_ENV == 'production' ? 'None' : 'None',
      maxAge: 3600000,
      path: '/',
    });

    const rawLogo = user.shops?.[0]?.shop_logo_url || '';

    


    res.status(200).json({
        user_id: user.user_id,
        username: user.username,
        email: user.email,
        roles: user.roles,
        shops:user.shops,
        authenticated:true,
        transaction_id:transaction ? transaction.trans_id : "",
        currentshop: currentShopId,
        logo:rawLogo
      });
    } catch (err) {
      console.error('Error logging in:', err);
      res.status(500).json({ error: 'An unexpected error occurred. Please contact admin.' });
    }
};

exports.changePassword = async (req, res) => {
    const { user_id, currentPassword, newPassword, confirmPassword} = req.body;
    try {
        const user = await User.findByPk(user_id);
        if (!user) {return res.status(404).json({ error: 'User not found' });}
        // const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        // if (!isCurrentPasswordValid) {return res.status(401).json({ error: 'Previous password is incorrect (check your main)' });}
        if (newPassword !== confirmPassword) {return res.status(400).json({ error: 'Password do not match' });}
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await user.update({password: hashedPassword,is_verified: true,});
        res.status(200).json({ message: 'Password changed successfully.',authenticated:true });
    } catch (err) {
        res.status(500).json({ error: 'Failed to change password' });}
};


exports.changePasswordAdmin = async (req, res) => {
    const { user_id,newPassword} = req.body;
    try {
        const user = await User.findByPk(user_id);
        if (!user) {return res.status(404).json({ error: 'User not found' });}
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await user.update({password: hashedPassword});
        res.status(200).json({ message: 'Password changed successfully.' });
    } catch (err) {
        console.error('Error changing password:', err);
        res.status(500).json({ error: 'Failed to change password' });}
};



exports.getUsers = async (req, res) => {
  // const { page = 1, pageSize = 10, query = '' } = req.query;
  if (req.query.query && typeof req.query.query === 'object') {
    const queryObj = req.query.query;
    page = queryObj.page || 1;
    pageSize = queryObj.pageSize || 10;
    query = queryObj.query || '';
    shop_id = queryObj.shop_id;
    type = queryObj.type;
  } else {
    page = req.query.page || 1;
    pageSize = req.query.pageSize || 10;
    query = req.query.query || '';
    shop_id = req.query.shop_id;
    type = req.query.type;
  }
  

  const offset = (page - 1) * pageSize;

  try {
    const whereClause = {};
    const shopWhereClause = {};
    const searchQuery = typeof query === 'string' ? query.toLowerCase() : '';

    if (searchQuery) {
      const conditions = [];
      
      conditions.push(
        { username: { [Op.iLike]: `%${searchQuery}%` } },
        { email: { [Op.iLike]: `%${searchQuery}%` } },
        { first_names: { [Op.iLike]: `%${searchQuery}%` } },
        { last_names: { [Op.iLike]: `%${searchQuery}%` } },
        { identification: { [Op.iLike]: `%${searchQuery}%` } }
      );

      whereClause[Op.or] = conditions;
      shopWhereClause.shop_name = { [Op.iLike]: `%${searchQuery}%` };
      
    }
    
      if (shop_id) {
        shopWhereClause = {
          ...shopWhereClause,
          shop_id: shop_id
        };
      }
    
      const { rows: users, count: total } = await User.findAndCountAll({
          where: whereClause,
          include: [
              {
                  model: Role,
                  as: 'roles',
                  attributes: ['role_id', 'role_name'],
                  required: true,
                  where: searchQuery ? { role_name: { [Op.iLike]: `%${searchQuery}%` } } : undefined
              },
              {
                  model: Shop,
                  as: 'shops',
                  through: { attributes: [] },
                  required: true,
                  where: Object.keys(shopWhereClause).length > 0 ? shopWhereClause : undefined,
              },
              {
                  model: Status,
                  as: 'user_status',
                  attributes: ['status_description', 'status_id', 'status_name'],
              },
          ],
          order: [['created_at', 'DESC']],
          limit: Number(pageSize),
          offset: Number(offset),
      });

      res.status(200).json({
          data: users,
          metadata: {
              total,
              page: Number(page),
              limit: Number(pageSize),
              totalPages: Math.ceil(total / pageSize),
          },
      });
  } catch (err) {
      console.error('Error fetching users:', err);
      res.status(500).json({ error: 'Failed to fetch users' });
  }
};


  /**
   * Create a new user
   */
  exports.createUser = async (req, res) => {
    const { status, email, role_ids, shop_ids,first_names,last_names,identification} = req.body;
    const t = await sequelize.transaction();
    try {
        const existingUser = await User.findOne({ where: { email }, transaction: t });
        if (existingUser) {return res.status(400).json({ error: 'User with this email already exists.' });}
        const generatedPassword = uuidv4().slice(0, 16);
        const hashedPassword = await bcrypt.hash(generatedPassword, 10);
        const newUser = await User.create({first_names,last_names,status,identification,email,password: hashedPassword,is_verified: false,}, { transaction: t });
        if (role_ids && role_ids.length > 0) {
            const roles = await Role.findAll({ where: { role_id: role_ids } }, { transaction: t });
            if (roles.length == 0) { return res.status(400).json({ error: 'Invalid role IDs.' });}
            await newUser.addRoles(roles, { transaction: t });}
        if (shop_ids && shop_ids.length > 0) {
            const shops = await Shop.findAll({ where: { shop_id: shop_ids } }, { transaction: t });
            if (shops.length == 0) { return res.status(400).json({ error: 'Invalid shop IDs.' });}
            await newUser.addShops(shops, { transaction: t });
        }
        const emailSubject = 'Welcome to Our Platform';
        const emailText = getWelcomeEmail(email, generatedPassword);
        // store unsent mails
        try {
            await sendEmail(email, emailSubject, emailText);
        } catch (emailErr) {
            await t.rollback();
            console.error("Error sending email:", emailErr);
            return res.status(500).json({ error: 'Failed to send welcome email.' });
        }
        await t.commit();
        return res.status(201).json({
            data: newUser,
            message: 'User created successfully. Check your email for the temporary password.',
        });
    } catch (err) {
        await t.rollback();
        console.error('Error creating user:', err);
        return res.status(500).json({ error: 'Failed to create user' });
    }
};
  
  /**
   * Get a specific user by ID
   */
  exports.getUserById = async (req, res) => {
    const { id } = req.params;
  
    try {
      const user = await User.findByPk(id, {
        include: [
          {
            model: Role,
            as: 'roles',
            through: {
              attributes: [],
            },
          },
        ],
      });
  
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }
  
      res.status(200).json({ data: user });
    } catch (err) {
      console.error('Error fetching user:', err);
      res.status(500).json({ error: 'Failed to fetch user' });
    }
  };
  
  /**
   * Update a user
   */
  exports.updateUser = async (req, res) => {
    const { id } = req.params;
    const { username, email, password, role_ids, shop_ids,first_names,last_names,identification,status } = req.body;
    console.log(req.body);
    try {
        const user = await User.findByPk(id);
        if (!user) {
            return res.status(404).json({ success: false, message: 'User not found' });
        }
        if (username) user.username = username;
        if (email) user.email = email;
        if (password) user.password = await bcrypt.hash(password, 10);
        if (first_names) user.first_names = first_names;
        if (last_names) user.last_names = last_names;
        if (identification) user.identification = identification;
        if (status) user.status = status;

        await user.save();
        if (role_ids) {
            const roles = await Role.findAll({ where: { role_id: role_ids } });
            await user.setRoles(roles);
        }
        if (shop_ids) {
            const shops = await Shop.findAll({ where: { shop_id: shop_ids } });
            await user.setShops(shops);
        }
        const updatedUser = await User.findByPk(id, {
            include: [{ model: Role, as: 'roles' }, { model: Shop, as: 'shops' }]
        });

        res.status(200).json({
            success: true,
            message: 'User updated successfully',
            data: updatedUser
        });
    } catch (err) {
        console.error('Error updating user:', err);
        res.status(500).json({ success: false, message: 'Failed to update user' });
    }
};

  
  /**
   * Delete a user
   */
  exports.deleteUser = async (req, res) => {
    const { id } = req.params;
  
    try {
      const deleted = await User.destroy({ where: { user_id: id } });
      if (!deleted) {
        return res.status(404).json({ error: 'User not found' });
      }
  
      res.status(200).json({ message: 'User deleted successfully' });
    } catch (err) {
      console.error('Error deleting user:', err);
      res.status(500).json({ error: 'Failed to delete user' });
    }
  };
  
  /**
   * Delete a user role
   */
  exports.deleteUserRole = async (req, res) => {
    const { userId, roleId } = req.params;

    try {
      const deleted = await UserRole.destroy({
        where: { user_id: userId, role_id: roleId },
      });

      if (!deleted) {
        return res.status(404).json({ error: 'User role not found' });
      }

      res.status(200).json({ message: 'User role deleted successfully' });
    } catch (err) {
      console.error('Error deleting user role:', err);
      res.status(500).json({ error: 'Failed to delete user role' });
    }
  };

/**
 * Reset user password - generates temporary password and sends via email
 */
exports.auth_reset = async (req, res) => {
    const { email } = req.body;

    // Validate input
    if (!email) {
        return res.status(400).json({
            success: false,
            error: 'Email is required'
        });
    }

    const t = await sequelize.transaction();

    try {
        // Find user by email
        const user = await User.findOne({
            where: { email: email.toLowerCase().trim() },
            transaction: t
        });

        if (!user) {
            // Don't reveal if user exists or not for security
            return res.status(200).json({
                success: true,
                message: 'If an account with this email exists, a password reset email has been sent.'
            });
        }

        // Generate temporary password (16 characters)
        const temporaryPassword = uuidv4().slice(0, 16);
        console.log(`🔑 Generated temporary password for ${email}: ${temporaryPassword}`);

        // Hash the temporary password
        const hashedPassword = await bcrypt.hash(temporaryPassword, 10);

        // Update user in database with temporary password and mark as unverified
        await user.update({
            password: hashedPassword,
            is_verified: false, // Force user to change password on next login
            updated_at: new Date()
        }, { transaction: t });

        console.log(`📝 Updated password in database for user: ${user.user_id}`);

        // Prepare email content
        const emailSubject = 'Password Reset - Temporary Password';
        const emailContent = getPasswordResetEmail(user.first_names || user.username || email, temporaryPassword);

        // Send email with temporary password
        try {
            await sendEmail(email, emailSubject, emailContent);
            console.log(`📧 Password reset email sent successfully to: ${email}`);
        } catch (emailError) {
            // Rollback database changes if email fails
            await t.rollback();
            console.error('❌ Error sending password reset email:', emailError);
            return res.status(500).json({
                success: false,
                error: 'Failed to send password reset email. Please try again later.'
            });
        }

        // Commit transaction
        await t.commit();

        console.log(`✅ Password reset completed successfully for: ${email}`);

        return res.status(200).json({
            success: true,
            message: 'Password reset email sent successfully. Please check your email for the temporary password.',
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        // Rollback transaction on any error
        await t.rollback();
        console.error('❌ Error in password reset process:', error);

        return res.status(500).json({
            success: false,
            error: 'An unexpected error occurred during password reset. Please try again later.'
        });
    }
};