const { ExclusionConstraintError } = require('sequelize');
const { User,Shop,Role,Receipt,Paymenttransaction,Status,Transaction,Item}  = require('../../db/models');
const genericController = require('./_genericController');
const { link } = require('pdfkit');
const { sequelize } = require('../../db/models');

exports.getAllorders = genericController.getAll(Transaction);
exports.getOrderById = genericController.getById(Transaction, 'trans_id');
exports.updateOrder = genericController.update(Transaction, 'trans_id');
exports.deleteOrder = genericController.delete(Transaction, 'trans_id');


// exports.updateTransactionOrder = async (req, res) => {
//     try {
//       const { items, allocated_to,status } = req.body;
//       const { trans_id } = items;
      
//       const [updated] = await Transaction.update({
//         order_allocation: allocated_to,
//         trans_status: status
//       }, { 
//         where: { trans_id: trans_id }
//       });

//       if (updated) {
//         const updatedData = await Transaction.findByPk(trans_id);
//         res.status(200).json(updatedData);
//       } else {
//         res.status(404).json({ error: 'Transaction not found' });
//       }
//     } catch (error) {
//       res.status(500).json({ error: error.message });
//     }
// };


exports.createOrder = async (req, res) => {
    const { items,taxPrice, totalPrice,discount } = req.body;
    var x =req.body
    const t = await sequelize.transaction();
    try {
      const transaction = await Transaction.create({
        trans_total: parseFloat(totalPrice),
        trans_net: parseFloat(totalPrice)-parseFloat(taxPrice),
        trans_quantity: items.reduce((sum, item) => sum + parseFloat(item.quantity), 0),
        trans_status: 19,
        trans_type: 'ORDER',
        income_type:'income',
        username: "EM0003",
        trans_date: new Date(),
        trans_tax: taxPrice,
        shop_id: "SH1000",
        trans_discount: discount ? discount:0,
        extra_data:x
      }, { transaction: t });
    
      // Create receipts
      for (const item of items) {
          const orderItem = await Item.findOne({where: { item_id: item.posId }});
          if (!orderItem) {
              throw new Error(`Item with ID ${item.posId} not found`);
          }
          await Receipt.create({
            receipt_item: item.name,
            receipt_item_id: item.posId,
            receipt_quantity: parseFloat(item.quantity),
            receipt_each: orderItem.dataValues.item_selling ? orderItem.dataValues.item_selling:item.price,
            receipt_total: (orderItem.dataValues.item_selling || item.price) * parseFloat(item.quantity),
            receipt_tax: taxPrice,
            receipt_net: parseFloat(orderItem.dataValues.item_selling) * parseFloat(item.quantity),
            trans_id: transaction.trans_id,
            shop_id:orderItem.dataValues.shop_id || null,
            original_quantity: parseFloat(item.quantity) || 0,
            sold_quantity: parseFloat(item.quantity),
            created_at: new Date(),
            updated_at: new Date()
          }, { transaction: t });

      
      }
      await t.commit();
      res.status(201).json("sucess");
    } catch (err) {
      await t.rollback();
      res.status(400).json({ error: err.message });
    }
  };