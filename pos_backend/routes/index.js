const express = require("express");
const { body, param, query, validationResult } = require('express-validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();
const ItemController = require("./controllers/Item");
const discountController=require("./controllers/discount");
const categoryController=require("./controllers/category");
const UploadController=require("./controllers/uploadimages");
const StatusController=require("./controllers/status");
const receiptController =require("./controllers/receipt")
const paymentransController=require("./controllers/paymenttransaction")
const paymethController=require("./controllers/paymentmethod")
const transactionController=require('./controllers/transaction')
const authController =require('./controllers/authController')
const roleController =require('./controllers/roles')
const ShopController =require('./controllers/shop')
const reportsController = require('./controllers/reports');
const financialReportsController = require('./controllers/financialReports');
const TotalController =require('./controllers/calculations')
const supplyController = require('./controllers/supply');

const OrderController = require('./controllers/order');
const { handleFileUpload, uploadx } = require('./controllers/uploadFiles'); 
require("dotenv").config();

// const uploadDirectory = path.join(__dirname,process.env.PIC_UPLOAD_DIRECTORY);

// if (!fs.existsSync(uploadDirectory)) {
//   fs.mkdirSync(uploadDirectory, { recursive: true });
// }

// const storage = multer.diskStorage({
//   destination: (req, file, cb) => {
//     cb(null, uploadDirectory);
//   },
//     const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
//     const extension = path.extname(file.originalname);
//     cb(null, uniqueSuffix + extension);
//   }
// });

// const upload = multer({ storage });

const upload = multer({storage: multer.memoryStorage()});
router.post('/upload_pic', upload.array('item_pic_url', 5), UploadController.UploadPic);
router.get('/pic/:filename', UploadController.getPic);
router.delete('/delete_pic/:filename', UploadController.deletePic);


router.post('/uploadbulk_items/:shop_id', uploadx.single('file'), handleFileUpload);

const validate = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {return res.status(400).json({ errors: errors.array() });}
  next();};

// Create a new item
router.post('/items',[body('item_name').notEmpty().withMessage('Item name is required'),body('item_description').isString().optional(),body('item_model_no').isString().optional(),body('item_selling').isNumeric().withMessage('Selling price must be a number'),body('item_quantity').isInt({ min: 0 }).withMessage('Quantity must be a non-negative integer'),body('item_cat_id').notEmpty().withMessage('Category ID is required'),body('item_disc_id').isString().optional(),body('item_buying').isNumeric().optional(),body('shop_id').notEmpty().withMessage('Shop ID is required'),],validate,ItemController.createItem);
router.get('/items', ItemController.getItems);
router.get('/items/:id',[param('id').notEmpty().withMessage('ID is required').isString().withMessage('ID must be a string'),],validate,ItemController.getItemById);
router.put('/items/:id',[param('id').notEmpty().withMessage('ID is required').isString().withMessage('ID must be a string'),body('item_name').optional().isString().withMessage('Item name must be a string'),body('item_description').optional().isString().withMessage('Description must be a string'),body('item_model_no').optional().isString().withMessage('Model number must be a string'),body('item_selling').optional().isNumeric().withMessage('Selling price must be a number'),body('item_quantity').optional().isInt({ min: 0 }).withMessage('Quantity must be a non-negative integer'),body('item_cat_id').optional().isString().withMessage('Category ID must be a string'),body('item_disc_id').optional().isString().withMessage('Discount ID must be a string'),body('item_buying').optional().isNumeric().withMessage('Buying price must be a number'),body('shop_id').optional().isString().withMessage('Shop ID must be a string'),],validate,ItemController.updateItem);
router.delete('/items/:id',[param('id').notEmpty().withMessage('ID is required').isString().withMessage('ID must be a string'),],validate,ItemController.deleteItem);
router.put('/items_update/:id', ItemController.updateItemQuantity);

// reports
router.get('/reports', reportsController.generateReports);
router.get('/reports/invoice/:transaction_id', reportsController.generateInvoice);
router.get('/reports/supplier_sales', reportsController.getSupplierSalesReport);

//Suppliers
router.get('/suppliers', supplyController.getSuppliers);
router.post('/suppliers', supplyController.createSupplier);
router.put('/suppliers/:id', supplyController.updateSupplier);
router.delete('/suppliers/:id', supplyController.deleteSupplier);
// Supply Items
router.get('/supply_items', supplyController.getSupplyItems);
router.post('/supply_items', supplyController.createSupplyItem);
router.put('/supply_items/:id', supplyController.updateSupplyItem);
router.put('/supply_items_receipt/:id', supplyController.updateSupplyItemRecipt);
router.delete('/supply_items/:id', supplyController.deleteSupplyItem);
// Supply Sales
router.get('/supply_sales', supplyController.getSupplySales);
router.post('/supply_sales', supplyController.createSupplySale);
router.put('/supply_sales/:id', supplyController.updateSupplySale);
router.delete('/supply_sales/:id', supplyController.deleteSupplySale);

// Financial Reports routes
router.get('/financial-reports', financialReportsController.getFinancialReports);

// Expense Management routes
router.post('/expenses', financialReportsController.createExpense);
router.get('/expenses', financialReportsController.getExpenses);
router.put('/expenses/:id', financialReportsController.updateExpense);
router.delete('/expenses/:id', financialReportsController.deleteExpense);
router.get('/money-summary', financialReportsController.getMoneySummary);

// Liability Management routes
router.post('/liabilities', financialReportsController.createLiability);
router.get('/liabilities', financialReportsController.getLiabilities);
router.put('/liabilities/:id', financialReportsController.updateLiability);
router.delete('/liabilities/:id', financialReportsController.deleteLiability);

// Liability Payment routes
router.post('/liability-payments', financialReportsController.createLiabilityPayment);
router.get('/liability-payments', financialReportsController.getLiabilityPayments);

// Money withdrawal route
router.post('/withdraw-money', financialReportsController.withdrawMoney);

// Financial Summary Management routes
router.get('/financial-summary', financialReportsController.getFinancialSummary);
router.post('/trigger-financial-summary-update', financialReportsController.triggerFinancialSummaryUpdate);
router.post('/bulk-update-financial-summaries', financialReportsController.bulkUpdateFinancialSummaries);


router.post('/category', categoryController.createCategory);
router.get('/category', categoryController.getAllCategories);
router.get('/category/:id', categoryController.getCategoryById);
router.put('/category/:id', categoryController.updateCategory);
router.delete('/category/:id',categoryController.deleteCategory);

router.get('/categories/shop/:shop_id', categoryController.getCategoryByShopId);
router.post('/categories/remove-subcategory', categoryController.removeSubCategory);

// Discount routes
router.post('/discount', discountController.createDiscount);
router.get('/discount', discountController.getAllDiscount);
router.get('/discount/:id', discountController.getDiscountById);
router.put('/discount/:id', discountController.updateDiscount);
router.delete('/discount/:id', discountController.deleteDiscount);
router.get('/discount/shop/:shop_id', discountController.getDiscountsByShopId);


// Shop routes
router.post('/shop', ShopController.createShop);
router.get('/shop', ShopController.getAllShops);
router.get('/shop/:id', ShopController.getShopById);
router.put('/shop/:id', ShopController.updateShop);
router.delete('/shop/:id', ShopController.deleteShop);


// Roles routes
router.post('/roles', roleController.createRole);
router.get('/roles', roleController.getAllRoles);
router.get('/admin/roles', roleController.getAllRole);
router.get('/roles/:id', roleController.getRoleById);
router.put('/roles/:id', roleController.updateRole);
router.delete('/roles/:id', roleController.deleteRole);



// receipt routes
router.post('/receipt', receiptController.createreceipt);
router.get('/receipt', receiptController.getAllreceipt);
router.get('/receipt/:id', receiptController.getreceiptById);
router.put('/receipt/:id', receiptController.updatereceipt);
router.delete('/receipt/:id', receiptController.deletereceipt);
router.get('/receipt_trans/:id', receiptController.getreceiptByTrans);


// paymeth routes
router.post('/paymeth', paymethController.createPaymentMethod);
router.get('/paymeth', paymethController.getAllPaymentMethod);
router.get('/paymeth/:id', paymethController.getreceiptByShop);
router.put('/paymeth/:id', paymethController.updatePaymentMethod);
router.delete('/paymeth/:id', paymethController.deletePaymentMethod);

router.get('/paymeth_shop/:id', paymethController.getPaymentMethodByShop);


// transaction routes
router.post('/transaction', transactionController.createTransaction);
router.post('/create_expense', transactionController.createExpenseTransaction);
router.post('/create_income', transactionController.createIncomeTransaction);
router.get('/transaction', transactionController.getAllTransaction);
router.get('/transaction_', transactionController.getreceiptByShop);
router.get('/transaction/:id', transactionController.getTransactionById);
// router.get('/transaction_limit/:id', transactionController.getTransactionByIdLimit);
router.put('/transaction/:id', transactionController.updateTransaction);
router.delete('/transaction/:id', transactionController.deleteTransaction);
router.put('/transaction_/:id', transactionController.updateTrans);

//daraja
router.get('/paymeth_callback/:id', transactionController.handleMpesaConfirmation);
router.post('/paymeth_submit', transactionController.handlePaymentTransaction);
router.get('/paymeth_mpesa/:id', transactionController.getAllMpesa);
router.post('/paymeth_callback', transactionController.inputMpesaConfirmation);


// paymentrans routes
router.post('/paymentrans', paymentransController.createPaymentTransaction);
router.get('/paymentrans', paymentransController.getAllTransaction);
router.get('/paymentrans/:id', paymentransController.getPaymentTransactionById);
router.put('/paymentrans/:id', paymentransController.updatePaymentTransaction);
router.delete('/paymentrans/:id', paymentransController.deletePaymentTransaction);

// Status routes
router.post('/status', StatusController.createStatus);
router.get('/status', StatusController.getAllStatus);
router.get('/status/:id', StatusController.getAllStatus);
// router.get('/status/:id', StatusController.getStatusById);
router.put('/status/:id', StatusController.updateStatus);
router.delete('/status/:id', StatusController.deleteStatus);



// auth_login routes
router.post('/auth_login', authController.login);
router.post('/auth_create', authController.createUser);
router.delete('/auth_delete/:id', authController.deleteUser);
router.put('/auth_update/:id', authController.updateUser);
router.get('/get_users', authController.getUsers);
router.get('/get_user/:id', authController.getUserById);

router.post('/auth_forgot', authController.changePassword);
router.post('/admin_forgot', authController.changePasswordAdmin);
router.post('/auth_reset', authController.auth_reset);
// router.get('/get_user/:id', authController.);


//caclulations 

router.get('/cal_invet', TotalController.getDashboardMetrics);
router.get('/cal_dash', TotalController.getSalesAndProductMetrics);
router.get('/graph_sale', TotalController.getTodaysTransactions);
router.get('/graph_week', TotalController.getWeeklyTransactions);





router.post('/order', OrderController.createOrder);
router.get('/order', OrderController.getAllorders);
router.get('/order/:id', OrderController.getOrderById);
router.put('/order/:id', OrderController.updateOrder);
// router.put('/order_/:id', OrderController.updateTransactionOrder);
router.delete('/order/:id', OrderController.deleteOrder);

module.exports = router;
