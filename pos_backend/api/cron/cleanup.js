// Vercel Cron Job: System Cleanup Tasks
const { cleanupTempFiles } = require('../../routes/controllers/financialReports');

module.exports = async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is from Vercel Cron or authorized source
  const authHeader = req.headers.authorization;
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!process.env.CRON_SECRET || authHeader !== expectedAuth) {
    console.log('Unauthorized cron request:', { 
      hasSecret: !!process.env.CRON_SECRET,
      authHeader: authHeader ? 'present' : 'missing'
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const startTime = new Date();
    console.log(`🧹 [${startTime.toISOString()}] Starting cleanup tasks...`);
    
    // Run cleanup function
    await cleanupTempFiles();
    
    const endTime = new Date();
    const duration = endTime - startTime;
    
    console.log(`✅ [${endTime.toISOString()}] Cleanup tasks completed in ${duration}ms`);
    
    res.status(200).json({ 
      success: true, 
      message: 'Cleanup tasks completed successfully',
      timestamp: endTime.toISOString(),
      duration: `${duration}ms`,
      type: 'cleanup'
    });
    
  } catch (error) {
    console.error('❌ Cleanup cron job error:', error);
    
    res.status(500).json({ 
      success: false,
      error: 'Cleanup cron job failed', 
      message: error.message,
      timestamp: new Date().toISOString(),
      type: 'cleanup'
    });
  }
};
