// Vercel Cron Job: Weekly Financial Summary Update
const { updateWeeklyFinancialSummary } = require('../../routes/controllers/financialReports');
const { Shop } = require('../../models');

module.exports = async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is from Vercel Cron or authorized source
  const authHeader = req.headers.authorization;
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!process.env.CRON_SECRET || authHeader !== expectedAuth) {
    console.log('Unauthorized cron request:', { 
      hasSecret: !!process.env.CRON_SECRET,
      authHeader: authHeader ? 'present' : 'missing'
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const startTime = new Date();
    console.log(`📊 [${startTime.toISOString()}] Starting weekly financial summary update...`);
    
    // Get all shops and update each one
    const shops = await Shop.findAll();
    const results = [];
    
    for (const shop of shops) {
      try {
        await updateWeeklyFinancialSummary(shop.shop_id);
        console.log(`📈 Updated weekly summary for shop ${shop.shop_id}`);
        results.push({ shop_id: shop.shop_id, status: 'success' });
      } catch (shopError) {
        console.error(`Error updating weekly summary for shop ${shop.shop_id}:`, shopError);
        results.push({ shop_id: shop.shop_id, status: 'error', error: shopError.message });
      }
    }
    
    const endTime = new Date();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    console.log(`✅ [${endTime.toISOString()}] Weekly financial summary update completed in ${duration}ms`);
    console.log(`📊 Results: ${successCount} successful, ${errorCount} errors`);
    
    res.status(200).json({ 
      success: true, 
      message: 'Weekly financial summary updated successfully',
      timestamp: endTime.toISOString(),
      duration: `${duration}ms`,
      type: 'weekly',
      results: {
        total: shops.length,
        successful: successCount,
        errors: errorCount,
        details: results
      }
    });
    
  } catch (error) {
    console.error('❌ Weekly financial summary cron job error:', error);
    
    res.status(500).json({ 
      success: false,
      error: 'Weekly cron job failed', 
      message: error.message,
      timestamp: new Date().toISOString(),
      type: 'weekly'
    });
  }
};
