// Vercel Cron Job: Monthly Financial Summary Update
const { updateMonthlyFinancialSummary, sendMonthlyFinancialReport } = require('../../routes/controllers/financialReports');
const { Shop } = require('../../models');

module.exports = async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is from Vercel Cron or authorized source
  const authHeader = req.headers.authorization;
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!process.env.CRON_SECRET || authHeader !== expectedAuth) {
    console.log('Unauthorized cron request:', { 
      hasSecret: !!process.env.CRON_SECRET,
      authHeader: authHeader ? 'present' : 'missing'
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const startTime = new Date();
    console.log(`📅 [${startTime.toISOString()}] Starting monthly financial summary update...`);
    
    // Get all shops and update each one
    const shops = await Shop.findAll();
    const results = [];
    
    for (const shop of shops) {
      try {
        await updateMonthlyFinancialSummary(shop.shop_id);
        console.log(`📊 Updated monthly summary for shop ${shop.shop_id}`);
        
        // Send monthly financial report
        try {
          await sendMonthlyFinancialReport(shop.shop_id);
          console.log(`📧 Sent monthly report for shop ${shop.shop_id}`);
        } catch (emailError) {
          console.error(`Error sending monthly report for shop ${shop.shop_id}:`, emailError);
        }
        
        results.push({ shop_id: shop.shop_id, status: 'success' });
      } catch (shopError) {
        console.error(`Error updating monthly summary for shop ${shop.shop_id}:`, shopError);
        results.push({ shop_id: shop.shop_id, status: 'error', error: shopError.message });
      }
    }
    
    const endTime = new Date();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    console.log(`✅ [${endTime.toISOString()}] Monthly financial summary update completed in ${duration}ms`);
    console.log(`📊 Results: ${successCount} successful, ${errorCount} errors`);
    
    res.status(200).json({ 
      success: true, 
      message: 'Monthly financial summary updated successfully',
      timestamp: endTime.toISOString(),
      duration: `${duration}ms`,
      type: 'monthly',
      results: {
        total: shops.length,
        successful: successCount,
        errors: errorCount,
        details: results
      }
    });
    
  } catch (error) {
    console.error('❌ Monthly financial summary cron job error:', error);
    
    res.status(500).json({ 
      success: false,
      error: 'Monthly cron job failed', 
      message: error.message,
      timestamp: new Date().toISOString(),
      type: 'monthly'
    });
  }
};
