// Vercel Cron Job: Hourly Financial Summary Update
const { updateHourlyFinancialSummary } = require('../../routes/controllers/financialReports');
const { Shop } = require('../../models');

module.exports = async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is from Vercel Cron or authorized source
  const authHeader = req.headers.authorization;
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!process.env.CRON_SECRET || authHeader !== expectedAuth) {
    console.log('Unauthorized cron request:', { 
      hasSecret: !!process.env.CRON_SECRET,
      authHeader: authHeader ? 'present' : 'missing'
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const startTime = new Date();
    console.log(`🕐 [${startTime.toISOString()}] Starting hourly financial summary update...`);
    
    // Get all shops and update each one (optimized for 60s limit)
    const shops = await Shop.findAll({ limit: 10 }); // Limit to 10 shops per run

    // Process shops in parallel for speed (but limit concurrency)
    const promises = shops.slice(0, 5).map(async (shop) => {
      try {
        await updateHourlyFinancialSummary(shop.shop_id);
        console.log(`📊 Updated hourly summary for shop ${shop.shop_id}`);
        return { shop_id: shop.shop_id, status: 'success' };
      } catch (shopError) {
        console.error(`Error updating hourly summary for shop ${shop.shop_id}:`, shopError);
        return { shop_id: shop.shop_id, status: 'error', error: shopError.message };
      }
    });

    const results = await Promise.all(promises);
    
    const endTime = new Date();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    console.log(`✅ [${endTime.toISOString()}] Hourly financial summary update completed in ${duration}ms`);
    console.log(`📊 Results: ${successCount} successful, ${errorCount} errors`);
    
    res.status(200).json({ 
      success: true, 
      message: 'Hourly financial summary updated successfully',
      timestamp: endTime.toISOString(),
      duration: `${duration}ms`,
      type: 'hourly',
      results: {
        total: shops.length,
        successful: successCount,
        errors: errorCount,
        details: results
      }
    });
    
  } catch (error) {
    console.error('❌ Hourly financial summary cron job error:', error);
    
    res.status(500).json({ 
      success: false,
      error: 'Hourly cron job failed', 
      message: error.message,
      timestamp: new Date().toISOString(),
      type: 'hourly'
    });
  }
};
