// Vercel Cron Job: Comprehensive Daily Tasks (Hobby Plan Optimized)
const {
    updateHourlyFinancialSummary,
    updateDailyFinancialSummary,
    updateWeeklyFinancialSummary,
    cleanupTempFiles
} = require('../../routes/controllers/financialReports');
const { Shop } = require('../../models');

// Helper function for liability reminders (from financialReports.js)
const checkLiabilityReminders = async (shop_id) => {
    try {
        const moment = require('moment');
        const { Op } = require('sequelize');
        const { Liability } = require('../../models');
        
        const shop = await Shop.findByPk(shop_id);
        if (!shop) return;
        
        const upcomingDue = moment().add(7, 'days').toDate();
        const liabilities = await Liability.findAll({
            where: {
                shop_id,
                dueDate: { [Op.lte]: upcomingDue },
                status: { [Op.ne]: 2 } 
            }
        });

        for (const liability of liabilities) {
            // Note: sendLiabilityReminder function would need to be imported
            // await sendLiabilityReminder(shop, liability);
            console.log(`📧 Liability reminder needed for shop ${shop_id}, liability ${liability.id}`);
        }
    } catch (error) {
        console.error('Error checking liability reminders:', error);
    }
};

module.exports = async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Verify the request is from Vercel Cron or authorized source
  const authHeader = req.headers.authorization;
  const expectedAuth = `Bearer ${process.env.CRON_SECRET}`;
  
  if (!process.env.CRON_SECRET || authHeader !== expectedAuth) {
    console.log('Unauthorized cron request:', { 
      hasSecret: !!process.env.CRON_SECRET,
      authHeader: authHeader ? 'present' : 'missing'
    });
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const startTime = new Date();
    const today = new Date();
    const isFirstOfMonth = today.getDate() === 1;
    const isSunday = today.getDay() === 0;

    console.log(`🌙 [${startTime.toISOString()}] Starting comprehensive daily tasks...`);
    console.log(`📅 Date: ${today.toISOString().split('T')[0]}, First of month: ${isFirstOfMonth}, Sunday: ${isSunday}`);

    // Get all shops (limit for performance)
    const shops = await Shop.findAll({ limit: 10 });
    const results = [];

    // Process each shop with all necessary updates
    for (const shop of shops) {
      const shopResult = {
        shop_id: shop.shop_id,
        tasks: {},
        overall_status: 'success'
      };

      try {
        // 1. Always do hourly update (since we can't run hourly cron)
        try {
          await updateHourlyFinancialSummary(shop.shop_id);
          shopResult.tasks.hourly = 'success';
          console.log(`⏰ Updated hourly summary for shop ${shop.shop_id}`);
        } catch (error) {
          shopResult.tasks.hourly = 'error';
          console.error(`Error in hourly update for shop ${shop.shop_id}:`, error.message);
        }

        // 2. Always do daily update
        try {
          await updateDailyFinancialSummary(shop.shop_id);
          shopResult.tasks.daily = 'success';
          console.log(`📅 Updated daily summary for shop ${shop.shop_id}`);
        } catch (error) {
          shopResult.tasks.daily = 'error';
          console.error(`Error in daily update for shop ${shop.shop_id}:`, error.message);
        }

        // 3. Weekly update on Sundays
        if (isSunday) {
          try {
            await updateWeeklyFinancialSummary(shop.shop_id);
            shopResult.tasks.weekly = 'success';
            console.log(`📊 Updated weekly summary for shop ${shop.shop_id}`);
          } catch (error) {
            shopResult.tasks.weekly = 'error';
            console.error(`Error in weekly update for shop ${shop.shop_id}:`, error.message);
          }
        }

        // 4. Check liability reminders
        try {
          await checkLiabilityReminders(shop.shop_id);
          shopResult.tasks.liabilities = 'success';
          console.log(`📧 Checked liability reminders for shop ${shop.shop_id}`);
        } catch (error) {
          shopResult.tasks.liabilities = 'error';
          console.error(`Error checking liabilities for shop ${shop.shop_id}:`, error.message);
        }

      } catch (shopError) {
        shopResult.overall_status = 'error';
        shopResult.error = shopError.message;
        console.error(`Overall error for shop ${shop.shop_id}:`, shopError);
      }

      results.push(shopResult);
    }

    // 5. System cleanup (once per day)
    try {
      await cleanupTempFiles();
      console.log(`🧹 System cleanup completed`);
    } catch (error) {
      console.error(`Error in system cleanup:`, error);
    }
    
    const endTime = new Date();
    const duration = endTime - startTime;
    const successCount = results.filter(r => r.overall_status === 'success').length;
    const errorCount = results.filter(r => r.overall_status === 'error').length;

    // Count task-specific results
    const taskSummary = {
      hourly: results.filter(r => r.tasks.hourly === 'success').length,
      daily: results.filter(r => r.tasks.daily === 'success').length,
      weekly: results.filter(r => r.tasks.weekly === 'success').length,
      liabilities: results.filter(r => r.tasks.liabilities === 'success').length
    };

    console.log(`✅ [${endTime.toISOString()}] Comprehensive daily tasks completed in ${duration}ms`);
    console.log(`📊 Shop Results: ${successCount} successful, ${errorCount} errors`);
    console.log(`📋 Task Summary:`, taskSummary);

    res.status(200).json({
      success: true,
      message: 'Comprehensive daily tasks completed successfully',
      timestamp: endTime.toISOString(),
      duration: `${duration}ms`,
      type: 'comprehensive-daily',
      date_info: {
        date: today.toISOString().split('T')[0],
        is_first_of_month: isFirstOfMonth,
        is_sunday: isSunday
      },
      results: {
        total_shops: shops.length,
        successful_shops: successCount,
        error_shops: errorCount,
        task_summary: taskSummary,
        details: results
      }
    });
    
  } catch (error) {
    console.error('❌ Daily financial summary cron job error:', error);
    
    res.status(500).json({ 
      success: false,
      error: 'Daily cron job failed', 
      message: error.message,
      timestamp: new Date().toISOString(),
      type: 'daily'
    });
  }
};
