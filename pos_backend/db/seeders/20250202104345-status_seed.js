'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    return queryInterface.bulkInsert('status', [
      {
        status_name: 'In Stock',
        status_description: 'Item is available in inventory',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Out of Stock',
        status_description: 'Item is currently unavailable in inventory',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Low Stock',
        status_description: 'Item quantity is low in inventory',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Backordered',
        status_description: 'Item is out of stock but can be ordered',
        created_at: new Date(),
        updated_at: new Date(),
      },

      // POS Statuses
      {
        status_name: 'Available for Sale',
        status_description: 'Item is available for purchase in POS',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Sold Out',
        status_description: 'Item is completely sold out in POS',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Returned',
        status_description: 'Item was sold but returned by customer',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
       status_name: 'Damaged',
        status_description: 'Item is damaged and cannot be sold',
        created_at: new Date(),
        updated_at: new Date(),
      },

      // E-commerce Statuses
      {
        status_name: 'Active',
        status_description: 'Item is available for sale online',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        status_name: 'Inactive',
        status_description: 'Item is temporarily unavailable online',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        
        status_name: 'Pre-Order',
        status_description: 'Item can be pre-ordered before stock arrives',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        
        status_name: 'Discontinued',
        status_description: 'Item is no longer available online',
        created_at: new Date(),
        updated_at: new Date(),
      }
    ]);
  },

  async down (queryInterface, Sequelize) {
    return queryInterface.bulkDelete('status', null, {});
  }
};
