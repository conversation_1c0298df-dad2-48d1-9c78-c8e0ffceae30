'use strict';

/** @type {import('sequelize-cli').Migration} */

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION generate_item_id()
      RETURNS TEXT AS $$
      DECLARE
        new_id TEXT;
      BEGIN
        new_id := 'ITEM-' || to_char(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISS') || '-' || lpad(floor(random() * 10000)::text, 4, '0');
        RETURN new_id;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the items table
    await queryInterface.createTable('items', {
      item_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_item_id()'), // Use the function
      },
      item_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      item_selling: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      item_quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      item_availability: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      item_pic_url: {
        type: Sequelize.ARRAY(Sequelize.TEXT),
        allowNull: true,
      },
      item_cat_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'category', // Foreign key to category table
          key: 'category_id',
        },
      },
      item_disc_id: {
        type: Sequelize.TEXT,
        allowNull: true,
        references: {
          model: 'discount', // Foreign key to discount table
          key: 'discount_id',
        },
      },
      item_model_no: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      item_brand: {
        type: Sequelize.STRING(255),
        defaultValue: "Unbranded",
      },
      item_description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop', // Foreign key to shop table
          key: 'shop_id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      item_buying: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      item_ispublished: {
        type:Sequelize.BOOLEAN,
        allowNull:false
      }
    });

    // Add trigger for created_at and updated_at
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_items
      BEFORE INSERT OR UPDATE
      ON items
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the items table
    await queryInterface.dropTable('items');

    // Drop the trigger and function
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_items ON items;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);

    // Drop the generate_item_id() function
    await queryInterface.sequelize.query(`
      DROP FUNCTION IF EXISTS generate_item_id;
    `);
  },
};