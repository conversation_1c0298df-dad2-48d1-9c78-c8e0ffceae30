'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Orders', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      order_id: {
        type: Sequelize.TEXT
      },
      order_item: {
        type: Sequelize.TEXT
      },
      order_email: {
        type: Sequelize.STRING
      },
      order_phone: {
        type: Sequelize.ARRAY
      },
      order_address: {
        type: Sequelize.TEXT
      },
      order_receipt_id: {
        type: Sequelize.TEXT
      },
      order_delivery_time: {
        type: Sequelize.DATE
      },
      order_trans_id: {
        type: Sequelize.TEXT
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      created_at: {
        type: Sequelize.DATE
      },
      updated_at: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Orders');
  }
};