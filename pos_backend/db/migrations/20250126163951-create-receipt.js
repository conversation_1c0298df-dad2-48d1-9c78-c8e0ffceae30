'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Receipts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      receipt_id: {
        type: Sequelize.TEXT
      },
      receipt_item: {
        type: Sequelize.TEXT
      },
      receipt_item_id: {
        type: Sequelize.TEXT
      },
      receipt_quantity: {
        type: Sequelize.INTEGER
      },
      receipt_each: {
        type: Sequelize.DECIMAL
      },
      receipt_total: {
        type: Sequelize.DECIMAL
      },
      receipt_tax: {
        type: Sequelize.DECIMAL
      },
      receipt_net: {
        type: Sequelize.DECIMAL
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      trans_id: {
        type: Sequelize.TEXT
      },
      created_at: {
        type: Sequelize.DATE
      },
      updated_at: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Receipts');
  }
};