'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('CustomerFeedbacks', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      customer_feedback_id: {
        type: Sequelize.TEXT
      },
      feedback_order_id: {
        type: Sequelize.TEXT
      },
      feedback_rating: {
        type: Sequelize.INTEGER
      },
      feedback_comments: {
        type: Sequelize.TEXT
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      feedback_date: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('CustomerFeedbacks');
  }
};