'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('receipts', {
      receipt_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_receipt_id()'),
      },
      receipt_item: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'items',
          key: 'item_id',
        },
      },
      receipt_item_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'items',
          key: 'item_id',
        },
      },
      receipt_quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      receipt_each: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      receipt_total: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      receipt_tax: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      receipt_net: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_receipts
      BEFORE INSERT OR UPDATE
      ON receipts
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('receipts');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_receipts ON receipts;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};
