'use strict';

/** @type {import('sequelize-cli').Migration} */

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('orders', {
      order_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_order_id()'),
      },
      order_item: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'items',
          key: 'item_id',
        },
      },
      order_email: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      order_phone: {
        type: Sequelize.ARRAY(Sequelize.STRING(50)),
        allowNull: false,
      },
      order_address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      order_receipt_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'receipts',
          key: 'receipt_id',
        },
      },
      order_delivery_time: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      order_trans_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'transactions',
          key: 'trans_id',
        },
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_orders
      BEFORE INSERT OR UPDATE
      ON orders
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('orders');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_orders ON orders;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};