'use strict';

/** @type {import('sequelize-cli').Migration} */


module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('discount', {
      discount_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_discount_id()'),
      },
      discount_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      discount_type: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      discount_amount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_discount
      BEFORE INSERT OR UPDATE
      ON discount
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('discount');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_discount ON discount;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};
