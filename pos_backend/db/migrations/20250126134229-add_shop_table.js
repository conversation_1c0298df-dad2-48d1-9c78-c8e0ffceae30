'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('shop', {
      shop_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_shop_id()'),
      },
      shop_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      shop_owner_id: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      shop_owner_names: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      shop_email: {
        type: Sequelize.ARRAY(Sequelize.STRING(255)),
        allowNull: false,
      },
      shop_website: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      shop_phone: {
        type: Sequelize.ARRAY(Sequelize.STRING(255)),
        allowNull: false,
      },
      shop_location_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      shop_location_address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_shop
      BEFORE INSERT OR UPDATE
      ON shop
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('shop');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_shop ON shop;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};
