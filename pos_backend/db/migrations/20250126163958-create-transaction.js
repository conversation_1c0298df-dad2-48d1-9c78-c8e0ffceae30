'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Transactions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      trans_id: {
        type: Sequelize.TEXT
      },
      trans_status: {
        type: Sequelize.STRING
      },
      trans_total: {
        type: Sequelize.DECIMAL
      },

      trans_quantity: {
        type: Sequelize.INTEGER
      },
      trans_tax: {
        type: Sequelize.DECIMAL
      },
      trans_net: {
        type: Sequelize.DECIMAL
      },
      trans_discount: {
        type: Sequelize.DECIMAL
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      created_at: {
        type: Sequelize.DATE
      },
      updated_at: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Transactions');
  }
};