'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('PaymentTransactions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      payment_transaction_id: {
        type: Sequelize.TEXT
      },
      trans_id: {
        type: Sequelize.TEXT
      },
      payment_method_id: {
        type: Sequelize.TEXT
      },
      payment_amount: {
        type: Sequelize.DECIMAL
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      payment_date: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('PaymentTransactions');
  }
};