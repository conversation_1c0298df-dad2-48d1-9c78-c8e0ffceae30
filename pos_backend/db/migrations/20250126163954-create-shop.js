'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Shops', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      shop_name: {
        type: Sequelize.STRING
      },
      shop_owner_id: {
        type: Sequelize.STRING
      },
      shop_owner_names: {
        type: Sequelize.STRING
      },
      shop_email: {
        type: Sequelize.ARRAY
      },
      shop_website: {
        type: Sequelize.STRING
      },
      shop_phone: {
        type: Sequelize.ARRAY
      },
      shop_location_name: {
        type: Sequelize.STRING
      },
      shop_location_address: {
        type: Sequelize.TEXT
      },
      created_at: {
        type: Sequelize.DATE
      },
      updated_at: {
        type: Sequelize.DATE
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Shops');
  }
};