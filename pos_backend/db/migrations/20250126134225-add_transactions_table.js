'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('transactions', {
      trans_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_transaction_id()'),
      },
      trans_status: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      trans_total: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      trans_quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      trans_tax: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      trans_net: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      trans_discount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_transactions
      BEFORE INSERT OR UPDATE
      ON transactions
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('transactions');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_transactions ON transactions;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};

