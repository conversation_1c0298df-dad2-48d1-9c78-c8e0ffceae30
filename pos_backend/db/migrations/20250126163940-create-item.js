'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      item_id: {
        type: Sequelize.TEXT
      },
      item_name: {
        type: Sequelize.STRING
      },
      item_selling: {
        type: Sequelize.DECIMAL
      },
      item_quantity: {
        type: Sequelize.INTEGER
      },
      item_availability: {
        type: Sequelize.INTEGER
      },
      item_pic_url: {
        type: Sequelize.TEXT
      },
      item_cat_id: {
        type: Sequelize.TEXT
      },
      item_sub_cat: {
        type: Sequelize.TEXT
      },
      item_disc_id: {
        type: Sequelize.TEXT
      },
      item_model_no: {
        type: Sequelize.STRING
      },
      item_description: {
        type: Sequelize.TEXT
      },
      shop_id: {
        type: Sequelize.TEXT
      },
      created_at: {
        type: Sequelize.DATE
      },
      updated_at: {
        type: Sequelize.DATE
      },
      item_buying: {
        type: Sequelize.STRING
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Items');
  }
};