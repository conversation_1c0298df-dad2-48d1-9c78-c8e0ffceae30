'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('payment_methods', {
      payment_method_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_payment_method_id()'),
      },
      payment_method_name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      payment_method_description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      payment_methods_date: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_payment_methods
      BEFORE INSERT OR UPDATE
      ON payment_methods
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('payment_methods');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_payment_methods ON payment_methods;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },

};
