'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('payment_transactions', {
      payment_transaction_id: {
        type: Sequelize.TEXT,
        primaryKey: true,
        defaultValue: Sequelize.literal('generate_payment_transaction_id()'),
      },
      trans_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'orders',
          key: 'order_id',
        },
      },
      payment_method_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'payment_methods',
          key: 'payment_method_id',
        },
      },
      payment_amount: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      shop_id: {
        type: Sequelize.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id',
        },
      },
      payment_date: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_created_at_updated_at() RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        IF TG_OP = 'INSERT' THEN
          NEW.created_at = NOW();
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_set_created_at_updated_at_payment_transactions
      BEFORE INSERT OR UPDATE
      ON payment_transactions
      FOR EACH ROW
      EXECUTE FUNCTION set_created_at_updated_at();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('payment_transactions');
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_set_created_at_updated_at_payment_transactions ON payment_transactions;
      DROP FUNCTION IF EXISTS set_created_at_updated_at;
    `);
  },
};
