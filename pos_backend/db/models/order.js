'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Order extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  Order.init({
    order_id: DataTypes.TEXT,
    order_item: DataTypes.TEXT,
    order_email: DataTypes.STRING,
    order_phone: DataTypes.ARRAY,
    order_address: DataTypes.TEXT,
    order_receipt_id: DataTypes.TEXT,
    order_delivery_time: DataTypes.DATE,
    order_trans_id: DataTypes.TEXT,
    shop_id: DataTypes.TEXT,
    created_at: DataTypes.DATE,
    updated_at: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'Order',
  });
  return Order;
};