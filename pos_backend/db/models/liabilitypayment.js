'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class LiabilityPayment extends Model {
    static associate(models) {
      LiabilityPayment.belongsTo(models.Liability, { foreignKey: 'liabilityId' });
      LiabilityPayment.belongsTo(models.Shop, { foreignKey: 'shop_id' });
      LiabilityPayment.belongsTo(models.Status, { foreignKey: 'status', as: 'statusInfo' });
    }
  }
  LiabilityPayment.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    liabilityId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Liabilities',
        key: 'id'
      }
    },
    shop_id: {
      type: DataTypes.TEXT,
      allowNull: false,
      references: {
        model: 'shop',
        key: 'shop_id'
      }
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'status',
        key: 'status_id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'LiabilityPayment',
    tableName: 'LiabilityPayments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return LiabilityPayment;
};