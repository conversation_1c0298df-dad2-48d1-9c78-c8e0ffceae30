// db/models/user.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    static associate(models) {
      User.belongsToMany(models.Role, {
        through: models.Userrole,
        foreignKey: 'user_id',
        otherKey: 'role_id',    
        as: 'roles',
      });
      User.belongsToMany(models.Shop, {
        through: models.Usershop,
        foreignKey: 'user_id',
        otherKey: 'shop_id',    
        as: 'shops',
      });
      User.belongsTo(models.Status, {
          foreignKey: 'status',
          targetKey: 'status_id',
          as: 'user_status',
      });
    }
  }

  User.init(
    {
      user_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      username: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        defaultValue: sequelize.literal('generate_username_id()'),
      },
      first_names: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      last_names: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      identification: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.INTEGER, 
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      password: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_verified: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: 'User',
      tableName: 'users',
      timestamps: false,
    }
  );

  return User;
};