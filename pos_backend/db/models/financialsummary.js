'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class FinancialSummary extends Model {
    static associate(models) {
      FinancialSummary.belongsTo(models.Shop, { foreignKey: 'shop_id' });
    }
  }
  FinancialSummary.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shop_id: {
      type: DataTypes.TEXT,
      allowNull: false,
      references: {
        model: 'shop',
        key: 'shop_id'
      }
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false
    },
    totalSales: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    totalPurchases: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    totalExpenses: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    totalLiabilities: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    netProfit: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    cashIn: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    cashOut: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    netCashFlow: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'FinancialSummary',
    tableName: 'FinancialSummaries',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return FinancialSummary;
};