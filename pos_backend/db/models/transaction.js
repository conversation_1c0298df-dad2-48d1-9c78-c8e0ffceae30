'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Transaction extends Model {
    static associate(models) {
      Transaction.belongsTo(models.Shop, {foreignKey: 'shop_id'});
      Transaction.belongsTo(models.Status, {foreignKey: 'trans_status',targetKey: 'status_id',as: 'status'});
      Transaction.hasMany(models.Receipt, { foreignKey: 'trans_id', as: 'receipts' });
      Transaction.hasMany(models.Paymenttransaction, { foreignKey: 'trans_id',as: 'payments' });
      Transaction.belongsTo(models.User, { foreignKey: 'username',targetKey: 'username', as: 'user' });
      Transaction.belongsTo(models.User, { foreignKey: 'order_allocation', targetKey: 'user_id', as: 'allocatedUser' });
    }
  }
  Transaction.init({
    trans_id: {
      type: DataTypes.TEXT,
      primaryKey: true,
      defaultValue: sequelize.literal('generate_transaction_id()'),
    },
    trans_status: DataTypes.STRING,
    trans_type: DataTypes.STRING,
    income_type: DataTypes.TEXT,
    trans_total: DataTypes.DECIMAL,
    trans_quantity: DataTypes.INTEGER,
    trans_tax: DataTypes.DECIMAL,
    trans_net: DataTypes.DECIMAL,
    trans_discount: DataTypes.DECIMAL,
    reversed: DataTypes.BOOLEAN,
    reversed_count: DataTypes.INTEGER,
    shop_id: DataTypes.TEXT,
    payment_response: DataTypes.JSONB,
    username: DataTypes.TEXT,
    created_at: DataTypes.DATE,
    reversed:DataTypes.BOOLEAN,
    updated_at: DataTypes.DATE,
    extra_data: { type: DataTypes.JSONB, defaultValue: {} },
    order_allocation:DataTypes.UUID
  }, {
    sequelize,
    modelName: 'transactions',
    tableName: 'transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });
  return Transaction;
};