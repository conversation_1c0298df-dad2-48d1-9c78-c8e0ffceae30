// db/models/item.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Item extends Model {
    static associate(models) {
      Item.belongsTo(models.Category, {foreignKey: 'item_cat_id'});
      Item.belongsTo(models.Discount, {foreignKey: 'item_disc_id'});
      Item.belongsTo(models.Shop, {foreignKey: 'shop_id'});
      Item.belongsTo(models.Status, {foreignKey: 'item_availability',targetKey: 'status_id',as: 'status'});
    }
  }

  Item.init(
    {
      item_id: {
        type: DataTypes.TEXT,
        primaryKey: true,
        defaultValue: sequelize.literal('generate_item_id()'),
      },
      item_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      item_selling: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      item_quantity: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      item_availability: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      item_pic_url: {
        type: DataTypes.ARRAY(DataTypes.STRING), 
        allowNull: true,
        defaultValue: [],
      },
      item_cat_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      item_disc_id: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      item_sub_cat: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      item_model_no: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      item_brand: {
        type: DataTypes.STRING(255),
        defaultValue: "",
      },
      item_description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      shop_id: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      // shop_id: {
      //   type: DataTypes.TEXT,
      //   allowNull: false,
      // },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      item_buying: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      item_ispublished:{
        type: DataTypes.BOOLEAN,
        defaultValue:true
      },
      item_colors:{
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
      },
      item_sizes:{
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
      },
      item_tags:{
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: true,
        defaultValue: [],
      }
    },
    {
      sequelize,
      modelName: 'item',
      tableName: 'items', // Explicitly set the table name
      timestamps: false, // Disable Sequelize's default timestamps
    }
  );

  return Item;
};