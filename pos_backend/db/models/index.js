'use strict';
const process = require('process');
const env = process.env.NODE_ENV || 'development';
const config = require(__dirname + '/../config/config.js')[env];
const { Sequelize } = require('sequelize');
const pg = require('pg');
const Item = require('./item');
const Category = require('./category');
const Discount = require('./discount');
const Shop = require('./shop');
const Status = require('./status');
const Receipt = require('./receipt');
const Transaction  = require('./transaction');
const Paymentmethod  = require('./paymentmethod');
const Paymenttransaction  = require('./paymenttransaction');

const User  = require('./user');
const Userrole  = require('./userrole');
const Usershop  = require('./usershop');
const Role  = require('./role');
const MpesaConfirmation = require('./mpesaconfirmation');
const Supplier = require('./supplier');
const SupplyItem = require('./supplyitem');
const SupplySale = require('./supplysale');
const Liability = require('./liability');
const Expense = require('./expense');
const FinancialSummary = require('./financialsummary');
const LiabilityPayment = require('./liabilitypayment');

let sequelize;
if (config.use_env_variable) {
  sequelize = new Sequelize(process.env[config.use_env_variable], {
    ...config,
    dialectModule: pg,
    logging: false,
  });
} else {

  sequelize = new Sequelize(config.database, config.username, config.password, {
    ...config,
    dialectModule: pg,
    logging: false,
  });
}


const models = {
  Item: Item(sequelize, Sequelize.DataTypes),
  Category: Category(sequelize, Sequelize.DataTypes),
  Discount: Discount(sequelize, Sequelize.DataTypes),
  Shop: Shop(sequelize, Sequelize.DataTypes),
  Status: Status(sequelize, Sequelize.DataTypes),
  Receipt:Receipt(sequelize, Sequelize.DataTypes),
  Transaction:Transaction(sequelize, Sequelize.DataTypes),
  Paymentmethod:Paymentmethod(sequelize, Sequelize.DataTypes),
  Paymenttransaction:Paymenttransaction(sequelize, Sequelize.DataTypes),
  User:User(sequelize, Sequelize.DataTypes),
  Userrole:Userrole(sequelize, Sequelize.DataTypes),
  Usershop:Usershop(sequelize, Sequelize.DataTypes),
  Role:Role(sequelize, Sequelize.DataTypes),
  MpesaConfirmation:MpesaConfirmation(sequelize, Sequelize.DataTypes),
  Supplier:Supplier(sequelize, Sequelize.DataTypes),
  SupplyItem:SupplyItem(sequelize, Sequelize.DataTypes),
  SupplySale:SupplySale(sequelize, Sequelize.DataTypes),
  Liability:Liability(sequelize, Sequelize.DataTypes),
  Expense:Expense(sequelize, Sequelize.DataTypes),
  FinancialSummary:FinancialSummary(sequelize, Sequelize.DataTypes),
  LiabilityPayment:LiabilityPayment(sequelize, Sequelize.DataTypes)
};

Object.values(models).forEach((model) => {
  if (model.associate) {
    model.associate(models);
  }
});

module.exports = {
  sequelize,
  ...models,
};
