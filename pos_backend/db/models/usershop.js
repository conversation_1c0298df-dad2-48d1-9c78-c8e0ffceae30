'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class UserShop extends Model {
    static associate(models) {
      UserShop.belongsTo(models.User, { foreignKey: 'user_id' });
      UserShop.belongsTo(models.Shop, { foreignKey: 'shop_id' });
    }
  }

  UserShop.init(
    {
      user_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'user_id',
        },
        onDelete: 'CASCADE',
      },
      shop_id: {
        type: DataTypes.TEXT,
        allowNull: false,
        references: {
          model: 'shops',
          key: 'shop_id',
        },
        onDelete: 'CASCADE',
      },
      created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
      updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
      },
    },
    {
      sequelize,
      modelName: 'UserShop',
      tableName: 'user_shops',
      timestamps: false,
    }
  );

  return UserShop;
};
