'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class PaymentMethod extends Model {
    static associate(models) {
      PaymentMethod.hasMany(models.Paymenttransaction, {foreignKey: 'payment_method_id', as: 'paymentMethod'});
    }
  }
  PaymentMethod.init({
    payment_method_id:{
      type: DataTypes.TEXT,
      primaryKey: true,
      defaultValue: sequelize.literal('generate_payment_method_id()'),
    },
    payment_method_name: DataTypes.STRING,
    payment_method_description: DataTypes.TEXT,
    shop_id: DataTypes.TEXT,
    status: DataTypes.BOOLEAN,
    payment_number: DataTypes.TEXT
  }, {
    sequelize,
    modelName: 'payment_methods',
    tableName: 'payment_methods',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });
  return PaymentMethod;
};