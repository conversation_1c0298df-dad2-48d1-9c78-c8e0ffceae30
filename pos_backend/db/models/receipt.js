'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Receipt extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      Receipt.belongsTo(models.Item, { foreignKey: 'receipt_item_id' });
    }
  }
  Receipt.init({
    receipt_id: {
      type: DataTypes.TEXT,
      primaryKey: true,
      defaultValue: sequelize.literal('generate_receipt_id()'),
    },
    receipt_item: DataTypes.TEXT,
    receipt_item_id: DataTypes.TEXT,
    receipt_quantity: DataTypes.DECIMAL,
    receipt_each: DataTypes.DECIMAL,
    receipt_total: DataTypes.DECIMAL,
    receipt_tax: DataTypes.DECIMAL,
    receipt_net: DataTypes.DECIMAL,
    trans_id: DataTypes.TEXT,
    shop_id: DataTypes.TEXT,
    original_quantity: DataTypes.DECIMAL,
    sold_quantity: DataTypes.DECIMAL,
    expense_category: DataTypes.STRING,
    expense_description: DataTypes.TEXT,
    created_at: DataTypes.DATE,
    updated_at: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'receipts',
    tableName: 'receipts',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });
  return Receipt;
};