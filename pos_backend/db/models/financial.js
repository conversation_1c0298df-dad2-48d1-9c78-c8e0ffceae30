const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const FinancialRecord = sequelize.define('FinancialRecord', {
  record_id: {
    type: DataTypes.STRING,
    primaryKey: true,
    defaultValue: () => `FIN${Math.random().toString(36).substr(2, 9)}`
  },
  record_type: {
    type: DataTypes.ENUM('SALE', 'PURCHASE', 'EXPENSE', 'INCOME', 'WITHDRAWAL'),
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  category: {
    type: DataTypes.STRING,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('PENDING', 'COMPLETED', 'CANCELLED'),
    defaultValue: 'PENDING'
  },
  shop_id: {
    type: DataTypes.STRING,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

const ProfitLoss = sequelize.define('ProfitLoss', {
  pl_id: {
    type: DataTypes.STRING,
    primaryKey: true,
    defaultValue: () => `PL${Math.random().toString(36).substr(2, 9)}`
  },
  period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  total_sales: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  total_costs: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  net_profit: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  shop_id: {
    type: DataTypes.STRING,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

const CashFlow = sequelize.define('CashFlow', {
  cf_id: {
    type: DataTypes.STRING,
    primaryKey: true,
    defaultValue: () => `CF${Math.random().toString(36).substr(2, 9)}`
  },
  period_start: {
    type: DataTypes.DATE,
    allowNull: false
  },
  period_end: {
    type: DataTypes.DATE,
    allowNull: false
  },
  operating_income: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  operating_expenses: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  net_cash_flow: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0
  },
  shop_id: {
    type: DataTypes.STRING,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
});

module.exports = {
  FinancialRecord,
  ProfitLoss,
  CashFlow
};
