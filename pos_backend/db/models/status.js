'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Status extends Model {
      static associate(models) {
        Status.hasMany(models.Item, { foreignKey: 'item_availability' });
        Status.hasMany(models.User, { foreignKey: 'status' });
        Status.hasMany(models.Shop, { foreignKey: 'shop_status', sourceKey: 'status_id', as: 'shops' });
      }
  }
  Status.init(
    {
      status_id: {
        type: DataTypes.TEXT,
        allowNull: false,
        primaryKey: true,
        unique: true
      },
      status_name: {
        type: DataTypes.STRING,
        allowNull: false
      },
      status_description: {
        type: DataTypes.TEXT,
        allowNull: true
      }
    },
    {
      sequelize,
      modelName: 'status',
      tableName: 'status',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  return Status;
};
