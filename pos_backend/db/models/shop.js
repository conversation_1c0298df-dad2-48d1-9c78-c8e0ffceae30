// db/models/shop.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Shop extends Model {
    static associate(models) {
      Shop.hasMany(models.Item, { foreignKey: 'shop_id' });
      Shop.belongsToMany(models.User, {through: 'UserShop',foreignKey: 'shop_id',as: 'users',});
      Shop.belongsTo(models.Status, { foreignKey: 'shop_status', targetKey: 'status_id', as: 'status' });
      Shop.hasMany(models.Category, { 
        foreignKey: 'shop_id',
        onDelete: 'CASCADE'
      });
    }
  }

  Shop.init(
    {
      shop_id: {
        type: DataTypes.TEXT,
        primaryKey: true,
        defaultValue: sequelize.literal('generate_shop_id()'),
      },
      shop_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      shop_owner_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      shop_owner_names: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      shop_email: {
        type: DataTypes.ARRAY(DataTypes.STRING(255)),
        allowNull: true,
      },
      shop_website: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      shop_phone: {
        type: DataTypes.ARRAY(DataTypes.STRING(255)),
        allowNull: true,
      },
      shop_status: {
        type: DataTypes.TEXT, 
        allowNull: true,
      },
      shop_location_name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      shop_location_address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      shop_logo_url: {
        type: DataTypes.ARRAY(DataTypes.STRING), 
        allowNull: true,
        defaultValue: [],
      },
    },
    {
      sequelize,
      modelName: 'Shop',
      tableName: 'shop',
      timestamps: false,
    }
  );

  return Shop;
};