'use strict';
const { Model } = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class Liability extends Model {
    static associate(models) {
      // define association here
      Liability.belongsTo(models.Shop, { foreignKey: 'shop_id' });
      Liability.belongsTo(models.Status, { foreignKey: 'status', as: 'statusInfo' });
      Liability.hasMany(models.LiabilityPayment, { foreignKey: 'liabilityId', as: 'payments' });
    }
  }
  Liability.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    shop_id: {
      type: DataTypes.TEXT,
      allowNull: false,
      references: {
        model: 'shop',
        key: 'shop_id'
      }
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'status',
        key: 'status_id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    dueDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Liability',
    tableName: 'Liabilities',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return Liability;
};