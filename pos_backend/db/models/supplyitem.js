'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SupplyItem extends Model {
    static associate(models) {
      SupplyItem.belongsTo(models.Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
      SupplyItem.belongsTo(models.Supplier, { foreignKey: 'destination_id', as: 'destination' });
      SupplyItem.hasMany(models.SupplySale, { foreignKey: 'supply_id', as: 'supply_sales' });
      SupplyItem.belongsTo(models.Transaction, { foreignKey: 'trans_id', as: 'transaction' });
      SupplyItem.belongsTo(models.Status, { foreignKey: 'status', targetKey: 'status_id', as: 'item_status' });
    }
  }
  SupplyItem.init({
    supply_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    supplier_id: { type: DataTypes.UUID, allowNull: false },
    destination_id: { type: DataTypes.UUID, allowNull: true },
    item_name: { type: DataTypes.STRING, allowNull: false },
    quantity: { type: DataTypes.DECIMAL, allowNull: true },
    amount_expected: { type: DataTypes.FLOAT, allowNull: true },
    amount_paid: { type: DataTypes.FLOAT, allowNull: true },
    supply_date: { type: DataTypes.DATE, allowNull: true },
    attachments: { type: DataTypes.ARRAY(DataTypes.STRING), defaultValue: [] },
    status: { type: DataTypes.INTEGER, defaultValue: 13 },
    quantity_sold: { type: DataTypes.DECIMAL, defaultValue: 0 },
    selling_price: { type: DataTypes.FLOAT, defaultValue: 0 },
    shop_id: { type: DataTypes.STRING, allowNull: false },
    trans_id: { type: DataTypes.TEXT, allowNull: true },
  }, {
    sequelize,
    modelName: 'SupplyItem',
    tableName: 'supply_items',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return SupplyItem;
};