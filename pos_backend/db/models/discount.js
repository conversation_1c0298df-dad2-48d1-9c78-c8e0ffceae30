// db/models/discount.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Discount extends Model {
    static associate(models) {
      Discount.hasMany(models.Item, { foreignKey: 'item_disc_id' });
    }
  }

  Discount.init(
    {
      discount_id: {
        type: DataTypes.TEXT,
        primaryKey: true,
        defaultValue: sequelize.literal('generate_discount_id()'),
      },
      discount_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      discount_type: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      discount_amount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      shop_id: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      sequelize,
      modelName: 'Discount',
      tableName: 'discount',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  return Discount;
};