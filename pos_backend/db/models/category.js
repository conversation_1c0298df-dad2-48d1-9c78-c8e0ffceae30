// db/models/category.js
'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Category extends Model {
    static associate(models) {
      Category.hasMany(models.Item, { foreignKey: 'item_cat_id' });
      Category.belongsTo(models.Shop, {
        foreignKey: 'shop_id',
        onDelete: 'CASCADE'
      });
    }
  }

  Category.init(
    {
      category_id: {
        type: DataTypes.TEXT,
        primaryKey: true,
        defaultValue: sequelize.literal('generate_category_id()'),
      },
      category_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      shop_id: {
        type: DataTypes.TEXT,
        allowNull: false,
        references: {
          model: 'shop',
          key: 'shop_id'
        },
        onDelete: 'CASCADE'
      },
      sub_category: {
        type: DataTypes.ARRAY(DataTypes.STRING), // Define sub_category as an array of strings
        allowNull: true, // Allow null if sub_category is optional
        defaultValue: [], // Default value as an empty array
      },
    },
    {
      sequelize,
      modelName: 'Category',
      tableName: 'category',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  return Category;
};