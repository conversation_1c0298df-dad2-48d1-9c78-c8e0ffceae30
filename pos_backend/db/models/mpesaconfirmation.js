'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class MpesaConfirmation extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  MpesaConfirmation.init({
    confirmation_id: {
      type: DataTypes.STRING, 
      primaryKey: true
    },
    trans_id: DataTypes.STRING,
    business_shortcode: DataTypes.STRING,
    trans_amount: DataTypes.DECIMAL,
    bill_ref_number: DataTypes.STRING,
    invoice_number: DataTypes.STRING,
    msisdn: DataTypes.STRING,
    transaction_time: DataTypes.DATE,
    payment_response: DataTypes.JSON,
    created_at: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'MpesaConfirmation',
    tableName: 'mpesa_confirmations',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });
  return MpesaConfirmation;
};