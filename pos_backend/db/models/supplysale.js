'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class SupplySale extends Model {
    static associate(models) {
      SupplySale.belongsTo(models.SupplyItem, { foreignKey: 'supply_id', as: 'supply_item' });
    }
  }
  SupplySale.init({
    sale_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    supply_id: { type: DataTypes.UUID, allowNull: false },
    supplier_id: { type: DataTypes.UUID },
    buyer_supplier_id: { type: DataTypes.UUID,allowNull: true },
    is_supplier: { type: DataTypes.BOOLEAN, defaultValue: false },
    item_name: { type: DataTypes.STRING },
    quantity_sold: { type: DataTypes.DECIMAL, allowNull: false },
    selling_price: { type: DataTypes.FLOAT, allowNull: false },
    total_amount: { type: DataTypes.FLOAT, allowNull: false },
    sale_date: { type: DataTypes.DATE, allowNull: false },
    customer_name: { type: DataTypes.STRING },
    customer_contact: { type: DataTypes.STRING },
    payment_method: { type: DataTypes.STRING },
    payment_status: { type: DataTypes.STRING},
    shop_id: { type: DataTypes.STRING, allowNull: false },
    attachments: { type: DataTypes.JSONB, defaultValue: [] },
    vat_rate: { type: DataTypes.FLOAT, allowNull: false },
    vat_amount: { type: DataTypes.FLOAT, allowNull: false },
    is_transaction: { type: DataTypes.BOOLEAN, defaultValue: false },
    extra_data: { type: DataTypes.JSONB, defaultValue: {} }
  }, {
    sequelize,
    modelName: 'SupplySale',
    tableName: 'supply_sales',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return SupplySale;
};