'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Supplier extends Model {
    static associate(models) {
      Supplier.hasMany(models.SupplyItem, { foreignKey: 'supplier_id', as: 'supply_items' });
    }
  }
  Supplier.init({
    supplier_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    supplier_name: { type: DataTypes.STRING, allowNull: false },
    supplier_contact: { type: DataTypes.STRING, allowNull: false },
    supplier_email: { type: DataTypes.STRING, allowNull: false },
    supplier_address: { type: DataTypes.STRING },
    supplier_status: { type: DataTypes.STRING, defaultValue: 'active' },
    shop_id: { type: DataTypes.STRING, allowNull: false }
  }, {
    sequelize,
    modelName: 'Supplier',
    tableName: 'suppliers',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });
  return Supplier;
};