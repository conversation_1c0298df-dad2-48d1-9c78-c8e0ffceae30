'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class CustomerFeedback extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
    }
  }
  CustomerFeedback.init({
    customer_feedback_id: DataTypes.TEXT,
    feedback_order_id: DataTypes.TEXT,
    feedback_rating: DataTypes.INTEGER,
    feedback_comments: DataTypes.TEXT,
    shop_id: DataTypes.TEXT,
    feedback_date: DataTypes.DATE
  }, {
    sequelize,
    modelName: 'CustomerFeedback',
  });
  return CustomerFeedback;
};