'use strict';
const {
  Model
} = require('sequelize');
module.exports = (sequelize, DataTypes) => {
  class PaymentTransaction extends Model {
    static associate(models) {
      PaymentTransaction.belongsTo(models.Transaction, {foreignKey: 'trans_id'});
      PaymentTransaction.belongsTo(models.Shop, {foreignKey: 'shop_id'});
      PaymentTransaction.belongsTo(models.Paymentmethod, {foreignKey: 'payment_method_id',as: 'paymentMethod'});
    }
  }
  PaymentTransaction.init({
    payment_transaction_id: {
      type: DataTypes.TEXT,
      primaryKey: true,
      defaultValue: sequelize.literal('generate_payment_transaction_id()'),
    },
    trans_id: DataTypes.TEXT,
    payment_method_id: DataTypes.TEXT,
    payment_amount: DataTypes.DECIMAL,
    shop_id: DataTypes.TEXT,
    payment_id:DataTypes.TEXT,
    payment_phone:DataTypes.TEXT,
    payment_response:DataTypes.JSON,

  }, {
    sequelize,
    modelName: 'payment_transactions',
    tableName: 'payment_transactions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
  });
  return PaymentTransaction;
};
