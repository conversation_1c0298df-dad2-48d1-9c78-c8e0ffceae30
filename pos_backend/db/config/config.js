// config.js
require('dotenv').config();
module.exports = {
  // development: {
  //   username: process.env.POSTGRES_USERx || 'postgres',
  //   password: process.env.POSTGRES_PASSWORDx || 'M',
  //   database: process.env.POSTGRES_DATABASEx || 'smartpos_db',
  //   host: process.env.POSTGRES_HOSTx,
  //   dialect: process.env.POSTGRES_DIALECTx || 'postgres',
  // },
  development: {
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DATABASE,
    host: process.env.POSTGRES_HOST,
    dialect: process.env.POSTGRES_DIALECT,
  },
  production: {
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DATABASE_PRODUCTION,
    host: process.env.POSTGRES_HOST,
    dialect: process.env.POSTGRES_DIALECT,
  },
};


