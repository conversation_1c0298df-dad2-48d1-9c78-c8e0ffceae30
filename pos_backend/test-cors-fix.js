#!/usr/bin/env node

/**
 * Test CORS Configuration for alphav-pos.vercel.app
 * 
 * 
 */

const https = require('https');

const BACKEND_URL = 'https://pos-backend-five.vercel.app';
const FRONTEND_ORIGIN = 'https://alphav-pos.vercel.app';

// Test endpoints that were having CORS issues
const TEST_ENDPOINTS = [
    {
        name: 'Dashboard Calculations',
        path: '/api/cal_dash?query[shop_id]=SH0002&shop_id=SH0002',
        method: 'GET'
    },
    {
        name: 'Graph Sales Data',
        path: '/api/graph_sale?query[shop_id]=SH0002&shop_id=SH0002',
        method: 'GET'
    },
    {
        name: 'Health Check',
        path: '/api/health',
        method: 'GET'
    }
];

// Make HTTP request with CORS headers
function makeRequest(url, options) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: data
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.end();
    });
}

// Test CORS preflight request
async function testPreflight(endpoint) {
    const url = `${BACKEND_URL}${endpoint.path}`;
    const options = {
        method: 'OPTIONS',
        headers: {
            'Origin': FRONTEND_ORIGIN,
            'Access-Control-Request-Method': endpoint.method,
            'Access-Control-Request-Headers': 'content-type,x-user-id,x-shop-id'
        }
    };

    console.log(`\n🔍 Testing CORS Preflight for: ${endpoint.name}`);
    console.log(`📍 URL: ${url}`);
    console.log(`🌐 Origin: ${FRONTEND_ORIGIN}`);
    
    try {
        const response = await makeRequest(url, options);
        
        console.log(`📊 Status: ${response.statusCode}`);
        console.log(`🔧 CORS Headers:`);
        console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin'] || 'NOT SET'}`);
        console.log(`   Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods'] || 'NOT SET'}`);
        console.log(`   Access-Control-Allow-Headers: ${response.headers['access-control-allow-headers'] || 'NOT SET'}`);
        console.log(`   Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials'] || 'NOT SET'}`);
        
        if (response.statusCode === 200 && response.headers['access-control-allow-origin']) {
            console.log(`✅ Preflight PASSED`);
            return true;
        } else {
            console.log(`❌ Preflight FAILED`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Preflight ERROR: ${error.message}`);
        return false;
    }
}

// Test actual request
async function testActualRequest(endpoint) {
    const url = `${BACKEND_URL}${endpoint.path}`;
    const options = {
        method: endpoint.method,
        headers: {
            'Origin': FRONTEND_ORIGIN,
            'Content-Type': 'application/json',
            'x-user-id': 'test-user-id',
            'x-shop-id': 'SH0002'
        }
    };

    console.log(`\n🧪 Testing Actual Request for: ${endpoint.name}`);
    
    try {
        const response = await makeRequest(url, options);
        
        console.log(`📊 Status: ${response.statusCode}`);
        console.log(`🔧 CORS Header: ${response.headers['access-control-allow-origin'] || 'NOT SET'}`);
        
        if (response.statusCode === 200 && response.headers['access-control-allow-origin']) {
            console.log(`✅ Request PASSED`);
            
            // Try to parse response
            try {
                const jsonData = JSON.parse(response.data);
                console.log(`📄 Response Type: ${typeof jsonData}`);
                if (jsonData.success !== undefined) {
                    console.log(`📈 API Success: ${jsonData.success}`);
                }
            } catch (e) {
                console.log(`📄 Response: ${response.data.substring(0, 100)}...`);
            }
            
            return true;
        } else {
            console.log(`❌ Request FAILED`);
            console.log(`📄 Response: ${response.data.substring(0, 200)}`);
            return false;
        }
        
    } catch (error) {
        console.log(`❌ Request ERROR: ${error.message}`);
        return false;
    }
}

// Main test function
async function runCORSTests() {
    console.log('🚀 Testing CORS Configuration');
    console.log(`🌐 Frontend: ${FRONTEND_ORIGIN}`);
    console.log(`🔗 Backend: ${BACKEND_URL}`);
    console.log('=' * 60);
    
    const results = [];
    
    for (const endpoint of TEST_ENDPOINTS) {
        console.log(`\n${'='.repeat(50)}`);
        console.log(`🎯 Testing: ${endpoint.name}`);
        console.log(`${'='.repeat(50)}`);
        
        // Test preflight first
        const preflightPassed = await testPreflight(endpoint);
        
        // Test actual request
        const requestPassed = await testActualRequest(endpoint);
        
        results.push({
            endpoint: endpoint.name,
            preflight: preflightPassed,
            request: requestPassed,
            overall: preflightPassed && requestPassed
        });
        
        // Wait between tests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Summary
    console.log(`\n${'='.repeat(60)}`);
    console.log('📊 CORS TEST SUMMARY');
    console.log(`${'='.repeat(60)}`);
    
    const passed = results.filter(r => r.overall).length;
    const failed = results.filter(r => !r.overall).length;
    
    console.log(`✅ Passed: ${passed}/${results.length}`);
    console.log(`❌ Failed: ${failed}/${results.length}`);
    
    results.forEach(result => {
        const status = result.overall ? '✅' : '❌';
        console.log(`${status} ${result.endpoint}: Preflight(${result.preflight ? '✅' : '❌'}) Request(${result.request ? '✅' : '❌'})`);
    });
    
    if (passed === results.length) {
        console.log('\n🎉 All CORS tests passed! Your frontend should work correctly.');
        console.log('\n📱 You can now make API calls from https://alphav-pos.vercel.app');
    } else {
        console.log('\n⚠️  Some CORS tests failed. Check the configuration.');
    }
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Test from your actual Next.js frontend');
    console.log('2. Check browser console for any remaining CORS errors');
    console.log('3. Verify authentication headers are being sent correctly');
}

// Run the tests
runCORSTests().catch(error => {
    console.error('💥 Test error:', error);
    process.exit(1);
});
