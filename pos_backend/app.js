const cors = require("cors");
var express = require("express")
const flash = require("express-flash");
const bodyParser = require("body-parser");
const Routes = require("./routes");
var { createSession, getSession } = require("./utils/sessions");

// Import the simplified middleware system
const { protectRoute } = require("./middleware/simpleAuth");

// Simple request logger
const requestLogger = (req, res, next) => next();

// Simple error handler
const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);
    res.status(err.status || 500).json({
        error: err.message || 'Internal server error',
        message: 'An unexpected error occurred'
    });
};

require("dotenv").config();
const app =express();
// app.use(cors({ origin: 'http://localhost:3000',credentials: true,}));
// app.use(cors());
// app.use(cors({ origin: 'https://pos-backend-five.vercel.app',credentials: true,}));
// CORS Configuration - Fixed for production
const allowedOrigins = [
    'http://localhost:3000',                    // Local development
    'http://localhost:3001',                    // Alternative local port
    'https://alphav-pos.vercel.app',           // ✅ Your Next.js Frontend (MAIN)
    'https://www.alphav-pos.vercel.app',       // Frontend with www subdomain
    'https://pos-backend-five.vercel.app',     // Backend (for testing)
    process.env.FRONTEND_URL,                   // Environment variable override
    process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : null
].filter(Boolean); 
console.log('🌐 Allowed CORS origins:', allowedOrigins);

// Simple and working CORS configuration
app.use(cors({
    origin: function (origin, callback) {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);

        // Check if origin is in allowed list
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }

        // Block other origins
        callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'x-user-id',
        'x-shop-id',
        'x-auth-token',
        'Accept',
        'Origin',
        'X-Requested-With'
    ],
    exposedHeaders: ['x-total-count', 'x-page-count']
}));
app.set('trust proxy', 1);

// Security headers only (CORS handled above)
app.use((req, res, next) => {
    // Security headers
    res.header('X-Content-Type-Options', 'nosniff');
    res.header('X-Frame-Options', 'DENY');
    res.header('X-XSS-Protection', '1; mode=block');
    next();
});

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Add request logging
app.use(requestLogger);

// Add health check endpoints (unprotected)
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Test CORS endpoint
app.get('/api/cors-test', (req, res) => {
    res.json({
        message: 'CORS test successful',
        origin: req.headers.origin,
        timestamp: new Date().toISOString(),
        headers: req.headers
    });
});

// CORS-aware route protection middleware
app.use('/api/', (req, res, next) => {
    // Skip authentication for OPTIONS requests (already handled above)
    if (req.method === 'OPTIONS') {
        return next();
    }

    // Skip authentication for public endpoints
    const publicRoutes = [
        '/health',
        '/api/health',
        '/api/cors-test',
        '/api/auth_reset',      // Password reset should be public
        '/api/auth_login'       // Login should be public
    ];

    if (publicRoutes.includes(req.path)) {
        console.log(`✅ Public route accessed: ${req.path}`);
        return next();
    }

    // Apply authentication for all other routes
    protectRoute(req, res, next);
});

// Your existing routes (now with CORS-aware protection)
app.use("/api/", Routes);
// Enhanced error handling
app.use(errorHandler);


app.use(flash());
app.listen(process.env.PORT, () => {console.log(`Server running on port ${process.env.PORT}`);});
