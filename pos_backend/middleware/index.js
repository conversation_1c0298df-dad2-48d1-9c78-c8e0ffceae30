const { protectRoute, protect, PROTECTION_LEVELS } = require('./routeProtection');
const { authenticate, extractUserInfo } = require('./authMiddleware');

// Request logging middleware
const requestLogger = (req, res, next) => {
    const userInfo = extractUserInfo(req);
    
    next();
};

// CORS middleware with authentication headers
const corsMiddleware = (req, res, next) => {
    res.header('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, x-user-id, x-shop-id, x-auth-token');
    res.header('Access-Control-Allow-Credentials', 'true');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
};

// Error handling middleware
const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);
    
    // Authentication errors
    if (err.name === 'UnauthorizedError' || err.status === 401) {
        return res.status(401).json({
            error: 'Authentication failed',
            message: 'Invalid or expired token'
        });
    }
    
    // Validation errors
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            error: 'Validation failed',
            message: err.message,
            details: err.errors
        });
    }
    
    // Database errors
    if (err.name === 'SequelizeError') {
        return res.status(500).json({
            error: 'Database error',
            message: 'Please try again later'
        });
    }
    
    // Default error
    res.status(err.status || 500).json({
        error: err.message || 'Internal server error',
        message: 'An unexpected error occurred'
    });
};

// Rate limiting middleware (basic implementation)
const rateLimitStore = new Map();
const rateLimit = (options = {}) => {
    const { windowMs = 15 * 60 * 1000, max = 100 } = options; // 15 minutes, 100 requests
    
    return (req, res, next) => {
        const key = req.ip + (req.user?.user_id || 'anonymous');
        const now = Date.now();
        
        if (!rateLimitStore.has(key)) {
            rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
            return next();
        }
        
        const limit = rateLimitStore.get(key);
        
        if (now > limit.resetTime) {
            limit.count = 1;
            limit.resetTime = now + windowMs;
            return next();
        }
        
        if (limit.count >= max) {
            return res.status(429).json({
                error: 'Too many requests',
                message: 'Rate limit exceeded. Please try again later.',
                retryAfter: Math.ceil((limit.resetTime - now) / 1000)
            });
        }
        
        limit.count++;
        next();
    };
};

// Health check middleware
const healthCheck = (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0'
    });
};

// Setup all middleware for Express app
const setupMiddleware = (app) => {
    // Basic middleware
    app.use(corsMiddleware);
    app.use(requestLogger);
    
    // Rate limiting
    app.use('/api/', rateLimit({ windowMs: 15 * 60 * 1000, max: 1000 })); // API routes
    app.use('/api/auth/', rateLimit({ windowMs: 15 * 60 * 1000, max: 10 })); // Auth routes (stricter)
    
    // Health check
    app.get('/health', healthCheck);
    app.get('/api/health', healthCheck);
    
    // Route protection (apply to all routes)
    app.use(protectRoute);
    
    // Error handling (should be last)
    app.use(errorHandler);
    
};

// Manual protection for specific routes
const applyProtection = (router, routes) => {
    routes.forEach(({ method, path, protection, handler }) => {
        const middleware = protect[protection] || protect.shop;
        router[method.toLowerCase()](path, middleware, handler);
    });
};

// Middleware for checking specific permissions
const checkPermission = (permission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        
        const hasPermission = req.user.permissions.includes(permission) || req.user.permissions.includes('*');
        
        if (!hasPermission) {
            return res.status(403).json({
                error: 'Insufficient permissions',
                required: permission,
                userPermissions: req.user.permissions
            });
        }
        
        next();
    };
};

// Middleware for checking shop ownership/admin
const requireShopAdmin = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({ error: 'Authentication required' });
    }
    
    const isAdmin = req.user.permissions.includes('admin') || req.user.permissions.includes('*');
    const isShopManager = req.user.roles.some(role => 
        role.role_name === 'manager' && role.shop_id === req.user.shop_id
    );
    
    if (!isAdmin && !isShopManager) {
        return res.status(403).json({
            error: 'Shop admin access required',
            message: 'You must be an admin or shop manager to perform this action'
        });
    }
    
    next();
};

// Middleware to ensure user can only access their own data
const requireOwnership = (userIdField = 'user_id') => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }
        
        const targetUserId = req.params[userIdField] || req.body[userIdField] || req.query[userIdField];
        const isAdmin = req.user.permissions.includes('admin') || req.user.permissions.includes('*');
        
        if (!isAdmin && targetUserId !== req.user.user_id) {
            return res.status(403).json({
                error: 'Access denied',
                message: 'You can only access your own data'
            });
        }
        
        next();
    };
};

module.exports = {
    setupMiddleware,
    protectRoute,
    protect,
    authenticate,
    applyProtection,
    checkPermission,
    requireShopAdmin,
    requireOwnership,
    requestLogger,
    corsMiddleware,
    errorHandler,
    rateLimit,
    healthCheck,
    PROTECTION_LEVELS
};
