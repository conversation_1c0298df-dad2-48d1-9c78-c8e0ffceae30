const { User, Usershop, <PERSON><PERSON>le, Role, Shop, Status } = require('../db/models');
const jwt = require('jsonwebtoken');

// Extract user info from headers and query parameters
const extractUserInfo = (req) => {
    const userInfo = {
        user_id: req.headers['x-user-id'] || req.query.user_id,
        shop_id: req.headers['x-shop-id'] || req.query.shop_id || req.query['query[shop_id]'],
        token: req.headers.authorization?.replace('Bearer ', '') || req.headers['x-auth-token']
    };

    // Handle nested query parameters
    if (req.query.query && typeof req.query.query === 'object') {
        userInfo.shop_id = userInfo.shop_id || req.query.query.shop_id;
        userInfo.user_id = userInfo.user_id || req.query.query.user_id;
    }

    return userInfo;
};

// Verify JWT token
const verifyToken = async (token) => {
    try {
        if (!token) return null;
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        return decoded;
    } catch (error) {
        console.error('Token verification failed:', error.message);
        return null;
    }
};

// Check if user exists and is active
const validateUser = async (user_id) => {
    try {
        if (!user_id) return null;

        const user = await User.findOne({
            where: { user_id },
            attributes: ['user_id', 'username', 'email', 'status']
        });

        if (!user) {
            return null;
        }

        if (user.status !== 16) { 
            return null;
        }

        return user;
    } catch (error) {
        console.error('User validation error:', error);
        return null;
    }
};

// Check if user has access to the shop
const validateShopAccess = async (user_id, shop_id) => {
    try {
        if (!user_id || !shop_id) return false;

        const userShop = await Usershop.findOne({
            where: { user_id, shop_id },
            include: [{
                model: Shop,
                attributes: ['shop_id', 'shop_name', 'shop_status'],
                include: [{
                    model: Status,
                    as: 'status',
                    attributes: ['status_id', 'status_name']
                }]
            }]
        });

        if (!userShop) {
            return false;
        }

        // Check shop status via the Status relationship
        if (userShop.Shop && userShop.Shop.status) {
            const shopStatusName = userShop.Shop.status.status_name;
            if (shopStatusName !== 'active') {
                return false;
            }
        }

        return userShop;
    } catch (error) {
        console.error('Shop access validation error:', error);
        return false;
    }
};

// Get user roles (global, not shop-specific in your current schema)
const getUserRoles = async (user_id, shop_id) => {
    try {
        if (!user_id) return [];

        const userRoles = await Userrole.findAll({
            where: { user_id },
            include: [{
                model: Role,
                attributes: ['role_id', 'role_name', 'role_status']
            }]
        });

        return userRoles.map(ur => ({
            role_id: ur.Role.role_id,
            role_name: ur.Role.role_name,
            role_status: ur.Role.role_status,
            shop_id: shop_id, // Pass through the shop_id for context
            // Map role names to permissions for compatibility
            permissions: getRolePermissions(ur.Role.role_name)
        }));
    } catch (error) {
        console.error('Role validation error:', error);
        return [];
    }
};

// Map role names to permissions (since your schema doesn't have a permissions field)
const getRolePermissions = (roleName) => {
    const rolePermissionMap = {
        'admin': ['*'], // Admin has all permissions
        'manager': ['items_create', 'items_update', 'reports_view', 'financial_view', 'suppliers_manage'],
        'cashier': ['items_view', 'transactions_create'],
        'employee': ['items_view'],
        'owner': ['*'], // Owner has all permissions
        'supervisor': ['items_view', 'reports_view', 'transactions_view']
    };

    return rolePermissionMap[roleName?.toLowerCase()] || ['items_view']; // Default minimal permissions
};

// Check if user has required permission
const hasPermission = (userRoles, requiredPermission) => {
    if (!requiredPermission) return true;

    return userRoles.some(role => {
        if (!role.permissions) return false;

        const permissions = Array.isArray(role.permissions) ? role.permissions : [];
        return permissions.includes(requiredPermission) || permissions.includes('*');
    });
};

// Main authentication middleware
const authenticate = (options = {}) => {
    return async (req, res, next) => {
        try {
            const {
                requireAuth = true,
                requireShop = true,
                requiredPermission = null,
                allowPublic = false
            } = options;

            // Extract user information
            const userInfo = extractUserInfo(req);
            
        

            // Allow public access if configured
            if (allowPublic && !userInfo.user_id && !userInfo.token) {
                return next();
            }

            // Verify token if provided
            if (userInfo.token) {
                const tokenData = await verifyToken(userInfo.token);
                if (tokenData) {
                    userInfo.user_id = userInfo.user_id || tokenData.user_id;
                }
            }

            // Check authentication requirement
            if (requireAuth && !userInfo.user_id) {
                return res.status(401).json({
                    error: 'Authentication required',
                    message: 'User ID or valid token must be provided',
                    required: {
                        headers: ['x-user-id', 'x-shop-id'],
                        query: ['user_id', 'shop_id', 'query[shop_id]'],
                        token: 'Authorization header or x-auth-token'
                    }
                });
            }

            // Validate user if user_id is provided
            let user = null;
            if (userInfo.user_id) {
                user = await validateUser(userInfo.user_id);
                if (!user) {
                    return res.status(401).json({
                        error: 'Invalid user',
                        message: 'User not found or inactive'
                    });
                }
            }

            // Check shop requirement
            if (requireShop && !userInfo.shop_id) {
                return res.status(400).json({
                    error: 'Shop ID required',
                    message: 'Shop ID must be provided in headers or query parameters'
                });
            }

            // Validate shop access if both user and shop are provided
            let shopAccess = null;
            if (userInfo.user_id && userInfo.shop_id) {
                shopAccess = await validateShopAccess(userInfo.user_id, userInfo.shop_id);
                if (!shopAccess) {
                    return res.status(403).json({
                        error: 'Shop access denied',
                        message: 'User does not have access to this shop'
                    });
                }
            }

            // Get user roles
            const userRoles = userInfo.user_id ? await getUserRoles(userInfo.user_id, userInfo.shop_id) : [];

            // Check permissions
            if (requiredPermission && !hasPermission(userRoles, requiredPermission)) {
                return res.status(403).json({
                    error: 'Insufficient permissions',
                    message: `Required permission: ${requiredPermission}`,
                    userRoles: userRoles.map(r => r.role_name)
                });
            }

            // Attach user info to request
            req.user = {
                user_id: userInfo.user_id,
                shop_id: userInfo.shop_id,
                userData: user,
                shopAccess: shopAccess,
                roles: userRoles,
                permissions: userRoles.flatMap(role => {
                    return Array.isArray(role.permissions) ? role.permissions : [];
                })
            };

            next();
        } catch (error) {
            console.error('Authentication middleware error:', error);
            res.status(500).json({
                error: 'Authentication system error',
                message: 'Please try again later'
            });
        }
    };
};

// Convenience middleware functions
const requireAuth = authenticate({ requireAuth: true, requireShop: false });
const requireShop = authenticate({ requireAuth: true, requireShop: true });
const requirePermission = (permission) => authenticate({ requireAuth: true, requireShop: true, requiredPermission: permission });
const allowPublic = authenticate({ requireAuth: false, requireShop: false, allowPublic: true });

module.exports = {
    authenticate,
    requireAuth,
    requireShop,
    requirePermission,
    allowPublic,
    extractUserInfo,
    validateUser,
    validateShopAccess,
    getUserRoles,
    hasPermission
};
