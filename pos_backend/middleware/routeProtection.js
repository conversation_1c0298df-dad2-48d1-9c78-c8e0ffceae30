const { requireAuth, requireShop, requirePermission, allowPublic } = require('./authMiddleware');

// Define route protection levels
const PROTECTION_LEVELS = {
    PUBLIC: 'public',           // No authentication required
    AUTH: 'auth',              // Authentication required, no shop
    SHOP: 'shop',              // Authentication + shop access required
    ADMIN: 'admin',            // Admin permissions required
    MANAGER: 'manager',        // Manager permissions required
    CASHIER: 'cashier',        // Cashier permissions required
    REPORTS: 'reports',        // Reports access required
    FINANCIAL: 'financial'     // Financial access required
};

// Permission mappings
const PERMISSIONS = {
    [PROTECTION_LEVELS.ADMIN]: 'admin',
    [PROTECTION_LEVELS.MANAGER]: 'manager',
    [PROTECTION_LEVELS.CASHIER]: 'cashier',
    [PROTECTION_LEVELS.REPORTS]: 'reports_view',
    [PROTECTION_LEVELS.FINANCIAL]: 'financial_view'
};

// Route protection configuration
const ROUTE_PROTECTION = {
    // Public routes (no authentication)
    'GET /api/health': PROTECTION_LEVELS.PUBLIC,
    'GET /api/status': PROTECTION_LEVELS.PUBLIC,
    
    // Authentication only (no shop required)
    'GET /api/user/profile': PROTECTION_LEVELS.AUTH,
    'PUT /api/user/profile': PROTECTION_LEVELS.AUTH,
    'GET /api/user/shops': PROTECTION_LEVELS.AUTH,
    
    // Shop access required
    'GET /api/items': PROTECTION_LEVELS.SHOP,
    'POST /api/items': PROTECTION_LEVELS.MANAGER,
    'PUT /api/items/:id': PROTECTION_LEVELS.MANAGER,
    'DELETE /api/items/:id': PROTECTION_LEVELS.ADMIN,
    
    'GET /api/transactions': PROTECTION_LEVELS.SHOP,
    'POST /api/transactions': PROTECTION_LEVELS.CASHIER,
    'PUT /api/transactions/:id': PROTECTION_LEVELS.MANAGER,
    'DELETE /api/transactions/:id': PROTECTION_LEVELS.ADMIN,
    
    // Reports (require reports permission)
    'GET /api/reports': PROTECTION_LEVELS.REPORTS,
    'GET /api/reports/supplier_sales': PROTECTION_LEVELS.REPORTS,
    'GET /api/cal_dash': PROTECTION_LEVELS.REPORTS,
    
    // Financial (require financial permission)
    'GET /api/financial-reports': PROTECTION_LEVELS.FINANCIAL,
    'GET /api/money-summary': PROTECTION_LEVELS.FINANCIAL,
    'GET /api/financial-summary': PROTECTION_LEVELS.FINANCIAL,
    'POST /api/expenses': PROTECTION_LEVELS.FINANCIAL,
    'PUT /api/expenses/:id': PROTECTION_LEVELS.FINANCIAL,
    'DELETE /api/expenses/:id': PROTECTION_LEVELS.ADMIN,
    
    'GET /api/liabilities': PROTECTION_LEVELS.FINANCIAL,
    'POST /api/liabilities': PROTECTION_LEVELS.FINANCIAL,
    'PUT /api/liabilities/:id': PROTECTION_LEVELS.FINANCIAL,
    'DELETE /api/liabilities/:id': PROTECTION_LEVELS.ADMIN,
    
    // Supply management
    'GET /api/suppliers': PROTECTION_LEVELS.SHOP,
    'POST /api/suppliers': PROTECTION_LEVELS.MANAGER,
    'PUT /api/suppliers/:id': PROTECTION_LEVELS.MANAGER,
    'DELETE /api/suppliers/:id': PROTECTION_LEVELS.ADMIN,
    
    'GET /api/supply-items': PROTECTION_LEVELS.SHOP,
    'POST /api/supply-items': PROTECTION_LEVELS.MANAGER,
    'PUT /api/supply-items/:id': PROTECTION_LEVELS.MANAGER,
    
    // Admin only
    'GET /api/users': PROTECTION_LEVELS.ADMIN,
    'POST /api/users': PROTECTION_LEVELS.ADMIN,
    'PUT /api/users/:id': PROTECTION_LEVELS.ADMIN,
    'DELETE /api/users/:id': PROTECTION_LEVELS.ADMIN,
    
    'GET /api/shops': PROTECTION_LEVELS.ADMIN,
    'POST /api/shops': PROTECTION_LEVELS.ADMIN,
    'PUT /api/shops/:id': PROTECTION_LEVELS.ADMIN,
    'DELETE /api/shops/:id': PROTECTION_LEVELS.ADMIN
};

// Get protection level for a route
const getRouteProtection = (method, path) => {
    // Ensure path starts with /api/ for API routes
    const fullPath = path.startsWith('/api/') ? path : `/api${path}`;
    const routeKey = `${method.toUpperCase()} ${fullPath}`;

    // Exact match
    if (ROUTE_PROTECTION[routeKey]) {
        return ROUTE_PROTECTION[routeKey];
    }

    // Pattern matching for parameterized routes
    for (const [pattern, protection] of Object.entries(ROUTE_PROTECTION)) {
        const regex = new RegExp('^' + pattern.replace(/:\w+/g, '[^/]+') + '$');
        if (regex.test(routeKey)) {
            return protection;
        }
    }

    // Default to shop protection for API routes
    if (fullPath.startsWith('/api/')) {
        return PROTECTION_LEVELS.SHOP;
    }

    return PROTECTION_LEVELS.PUBLIC;
};

// Get middleware for protection level
const getProtectionMiddleware = (protectionLevel) => {
    switch (protectionLevel) {
        case PROTECTION_LEVELS.PUBLIC:
            return allowPublic;
        case PROTECTION_LEVELS.AUTH:
            return requireAuth;
        case PROTECTION_LEVELS.SHOP:
            return requireShop;
        case PROTECTION_LEVELS.ADMIN:
        case PROTECTION_LEVELS.MANAGER:
        case PROTECTION_LEVELS.CASHIER:
        case PROTECTION_LEVELS.REPORTS:
        case PROTECTION_LEVELS.FINANCIAL:
            return requirePermission(PERMISSIONS[protectionLevel]);
        default:
            return requireShop; // Default fallback
    }
};

// Main route protection middleware
const protectRoute = (req, res, next) => {
    const protectionLevel = getRouteProtection(req.method, req.path);
    const middleware = getProtectionMiddleware(protectionLevel);
    
    
    return middleware(req, res, next);
};

// Manual protection functions for specific use cases
const protect = {
    public: allowPublic,
    auth: requireAuth,
    shop: requireShop,
    admin: requirePermission(PERMISSIONS[PROTECTION_LEVELS.ADMIN]),
    manager: requirePermission(PERMISSIONS[PROTECTION_LEVELS.MANAGER]),
    cashier: requirePermission(PERMISSIONS[PROTECTION_LEVELS.CASHIER]),
    reports: requirePermission(PERMISSIONS[PROTECTION_LEVELS.REPORTS]),
    financial: requirePermission(PERMISSIONS[PROTECTION_LEVELS.FINANCIAL])
};

// Utility function to check if user has access to route
const checkRouteAccess = (userRoles, method, path) => {
    const protectionLevel = getRouteProtection(method, path);
    
    if (protectionLevel === PROTECTION_LEVELS.PUBLIC) {
        return true;
    }
    
    if (protectionLevel === PROTECTION_LEVELS.AUTH || protectionLevel === PROTECTION_LEVELS.SHOP) {
        return userRoles.length > 0; // Just need to be authenticated
    }
    
    const requiredPermission = PERMISSIONS[protectionLevel];
    if (!requiredPermission) return true;
    
    return userRoles.some(role => {
        try {
            const permissions = typeof role.permissions === 'string' 
                ? JSON.parse(role.permissions) 
                : role.permissions || [];
            return permissions.includes(requiredPermission) || permissions.includes('*');
        } catch {
            return false;
        }
    });
};

// Add route to protection config
const addRouteProtection = (method, path, protectionLevel) => {
    const routeKey = `${method.toUpperCase()} ${path}`;
    ROUTE_PROTECTION[routeKey] = protectionLevel;
};

// Remove route from protection config
const removeRouteProtection = (method, path) => {
    const routeKey = `${method.toUpperCase()} ${path}`;
    delete ROUTE_PROTECTION[routeKey];
};

module.exports = {
    protectRoute,
    protect,
    PROTECTION_LEVELS,
    PERMISSIONS,
    ROUTE_PROTECTION,
    getRouteProtection,
    getProtectionMiddleware,
    checkRouteAccess,
    addRouteProtection,
    removeRouteProtection
};
