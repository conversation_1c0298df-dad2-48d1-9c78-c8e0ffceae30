// Integration script to add middleware to your existing Express app
// Add this to your main app.js or server.js file

const express = require('express');
const { setupMiddleware, protectRoute } = require('./middleware');

// Example of how to integrate into your existing app
function integrateMiddleware(app) {
    console.log('🔧 Integrating authentication and authorization middleware...');
    
    // 1. Add CORS, logging, and rate limiting
    const { corsMiddleware, requestLogger, rateLimit, errorHandler } = require('./middleware');
    
    // Apply CORS first
    app.use(corsMiddleware);
    
    // Add request logging
    app.use(requestLogger);
    
    // Add rate limiting for API routes
    app.use('/api/', rateLimit({ windowMs: 15 * 60 * 1000, max: 1000 }));
    app.use('/api/auth_login', rateLimit({ windowMs: 15 * 60 * 1000, max: 5 })); // Stricter for login
    
    // 2. Add route protection BEFORE your existing routes
    // This will automatically protect routes based on the configuration
    app.use('/api/', protectRoute);
    
    // 3. Your existing routes (from routes/index.js) will now be protected
    const routes = require('../routes');
    app.use('/api', routes);
    
    // 4. Add error handling LAST
    app.use(errorHandler);
    
    console.log('✅ Middleware integration complete');
    console.log('📋 Route protection is now active for all /api routes');
}

// Alternative: Manual route-by-route protection
function manualIntegration(app) {
    const { protect } = require('./middleware');
    
    // Example of manually protecting specific routes
    const router = express.Router();
    
    // Public routes (no changes needed)
    router.get('/health', (req, res) => res.json({ status: 'ok' }));
    
    // Protected routes - add middleware before your existing handlers
    const ItemController = require('../routes/controllers/Item');
    const reportsController = require('../routes/controllers/reports');
    const financialReportsController = require('../routes/controllers/financialReports');
    const TotalController = require('../routes/controllers/calculations');
    
    // Items (require shop access)
    router.get('/items', protect.shop, ItemController.getItems);
    router.post('/items', protect.manager, ItemController.createItem);
    router.put('/items/:id', protect.manager, ItemController.updateItem);
    router.delete('/items/:id', protect.admin, ItemController.deleteItem);
    
    // Reports (require reports permission)
    router.get('/reports', protect.reports, reportsController.generateReports);
    router.get('/reports/supplier_sales', protect.reports, reportsController.getSupplierSalesReport);
    router.get('/cal_dash', protect.reports, TotalController.getSalesAndProductMetrics);
    
    // Financial (require financial permission)
    router.get('/financial-reports', protect.financial, financialReportsController.getFinancialReports);
    router.get('/money-summary', protect.financial, financialReportsController.getMoneySummary);
    router.post('/expenses', protect.financial, financialReportsController.createExpense);
    router.put('/expenses/:id', protect.financial, financialReportsController.updateExpense);
    router.delete('/expenses/:id', protect.admin, financialReportsController.deleteExpense);
    
    app.use('/api', router);
}

module.exports = {
    integrateMiddleware,
    manualIntegration
};
