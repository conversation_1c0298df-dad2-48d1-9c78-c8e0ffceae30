
const { User, Usershop } = require('../db/models');

// Extract user info from headers and query parameters
const extractUserInfo = (req) => {
    const userInfo = {
        user_id: req.headers['x-user-id'] || req.query.user_id,
        shop_id: req.headers['x-shop-id'] || req.query.shop_id || req.query['query[shop_id]'],
        token: req.headers.authorization?.replace('Bearer ', '') || req.headers['x-auth-token']
    };

    // Handle nested query parameters
    if (req.query.query && typeof req.query.query === 'object') {
        userInfo.shop_id = userInfo.shop_id || req.query.query.shop_id;
        userInfo.user_id = userInfo.user_id || req.query.query.user_id;
    }

    return userInfo;
};

// Check if user exists and is active
const validateUser = async (user_id) => {
    try {
        if (!user_id) return null;

        const user = await User.findOne({
            where: { user_id },
            attributes: ['user_id', 'username', 'email', 'status']
        });

        if (!user) {
            return null;
        }

        if (user.status !== 16) {
            return null;
        }

        return user;
    } catch (error) {
        return null;
    }
};

// Check if user has access to the shop
const validateShopAccess = async (user_id, shop_id) => {
    try {
        if (!user_id || !shop_id) return false;

        const userShop = await Usershop.findOne({
            where: { user_id, shop_id }
        });

        if (!userShop) {
            return false;
        }

        return userShop;
    } catch (error) {
        return false;
    }
};

// Simple authentication middleware
const simpleAuth = (options = {}) => {
    return async (req, res, next) => {
        try {
            const {
                requireAuth = true,
                requireShop = true,
                allowPublic = false
            } = options;

            // Extract user information
            const userInfo = extractUserInfo(req);

            // Allow public access if configured
            if (allowPublic && !userInfo.user_id) {
                return next();
            }

            // Check authentication requirement
            if (requireAuth && !userInfo.user_id) {
                // Ensure CORS headers are set before sending error response
                const origin = req.headers.origin;
                const allowedOrigins = [
                    'http://localhost:3000',
                    'http://localhost:3001',
                    'https://alphav-pos.vercel.app',
                    'https://www.alphav-pos.vercel.app',
                    'https://pos-backend-five.vercel.app'
                ];

                if (origin && allowedOrigins.includes(origin)) {
                    res.header('Access-Control-Allow-Origin', origin);
                    res.header('Access-Control-Allow-Credentials', 'true');
                }

                return res.status(401).json({
                    error: 'Authentication required',
                    message: 'Please provide valid user credentials'
                });
            }

            // Helper function to set CORS headers on error responses
            const setCORSHeaders = (res, req) => {
                const origin = req.headers.origin;
                const allowedOrigins = [
                    'http://localhost:3000',
                    'http://localhost:3001',
                    'https://alphav-pos.vercel.app',
                    'https://www.alphav-pos.vercel.app',
                    'https://pos-backend-five.vercel.app'
                ];

                if (origin && allowedOrigins.includes(origin)) {
                    res.header('Access-Control-Allow-Origin', origin);
                    res.header('Access-Control-Allow-Credentials', 'true');
                }
            };

            // Validate user if user_id is provided
            let user = null;
            if (userInfo.user_id) {
                user = await validateUser(userInfo.user_id);
                if (!user) {
                    setCORSHeaders(res, req);
                    return res.status(401).json({
                        error: 'Authentication required',
                        message: 'Invalid user credentials'
                    });
                }
            }

            // Check shop requirement
            if (requireShop && !userInfo.shop_id) {
                setCORSHeaders(res, req);
                return res.status(400).json({
                    error: 'Shop ID required',
                    message: 'Please provide a valid shop ID'
                });
            }

            // Validate shop access if both user and shop are provided
            let shopAccess = null;
            if (userInfo.user_id && userInfo.shop_id) {
                shopAccess = await validateShopAccess(userInfo.user_id, userInfo.shop_id);
                if (!shopAccess) {
                    setCORSHeaders(res, req);
                    return res.status(403).json({
                        error: 'Access denied',
                        message: 'You do not have access to this shop'
                    });
                }
            }

            // Attach user info to request (simplified)
            req.user = {
                user_id: userInfo.user_id,
                shop_id: userInfo.shop_id,
                userData: user,
                shopAccess: shopAccess,
                // Simplified permissions - everyone gets basic access for now
                permissions: ['basic_access']
            };

            next();
        } catch (error) {
            // Set CORS headers for error responses
            const origin = req.headers.origin;
            const allowedOrigins = [
                'http://localhost:3000',
                'http://localhost:3001',
                'https://alphav-pos.vercel.app',
                'https://www.alphav-pos.vercel.app',
                'https://pos-backend-five.vercel.app'
            ];

            if (origin && allowedOrigins.includes(origin)) {
                res.header('Access-Control-Allow-Origin', origin);
                res.header('Access-Control-Allow-Credentials', 'true');
            }

            console.error('Authentication middleware error:', error);
            res.status(500).json({
                error: 'Authentication system error',
                message: 'Please try again later'
            });
        }
    };
};

// Route protection levels
const PROTECTION_LEVELS = {
    PUBLIC: 'public',
    AUTH: 'auth',
    SHOP: 'shop'
};

// Simple route protection
const protectRoute = (req, res, next) => {
    // Public routes (no authentication required)
    const publicRoutes = [
        '/health',
        '/status',
        // Authentication routes - all public
        '/auth_login',
        '/login',
        '/change_password',
        '/change_password_admin',
        '/get_users',
        '/create_user',
        '/update_user',
        '/delete_user',
        '/get_user',
        '/delete_user_role'
    ];

    // Check if current path matches any public route
    const isPublicRoute = publicRoutes.some(route =>
        req.path === route ||
        req.path.startsWith(route + '/') ||
        req.path.includes('auth') ||
        req.path.includes('login') ||
        req.path.includes('user')
    );

    if (isPublicRoute)  return simpleAuth({ requireAuth: false, requireShop: false, allowPublic: true })(req, res, next);
    

    return simpleAuth({ requireAuth: true, requireShop: true })(req, res, next);
};

// Convenience middleware functions
const requireAuth = simpleAuth({ requireAuth: true, requireShop: false });
const requireShop = simpleAuth({ requireAuth: true, requireShop: true });
const allowPublic = simpleAuth({ requireAuth: false, requireShop: false, allowPublic: true });

module.exports = {
    simpleAuth,
    protectRoute,
    requireAuth,
    requireShop,
    allowPublic,
    PROTECTION_LEVELS,
    extractUserInfo,
    validateUser,
    validateShopAccess
};
