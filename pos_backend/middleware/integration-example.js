// Example of how to integrate the middleware system into your Express app

const express = require('express');
const { setupMiddleware, protect, checkPermission, requireShopAdmin } = require('./index');

// Create Express app
const app = express();

// Setup all middleware (CORS, logging, rate limiting, route protection, error handling)
setupMiddleware(app);

// Your existing routes will now be automatically protected based on the configuration
// in middleware/routeProtection.js

// Example: Manual route protection (if you need custom protection)
const router = express.Router();

// Public route (no authentication required)
router.get('/public-info', protect.public, (req, res) => {
    res.json({ message: 'This is public information' });
});

// Authentication required (no shop)
router.get('/user/profile', protect.auth, (req, res) => {
    res.json({ 
        user: req.user.userData,
        message: 'User profile data' 
    });
});

// Shop access required
router.get('/dashboard', protect.shop, (req, res) => {
    res.json({ 
        shop_id: req.user.shop_id,
        user_id: req.user.user_id,
        message: 'Dashboard data for shop' 
    });
});

// Specific permission required
router.get('/admin/users', protect.admin, (req, res) => {
    res.json({ message: 'Admin users list' });
});

router.get('/reports/financial', protect.financial, (req, res) => {
    res.json({ message: 'Financial reports' });
});

// Custom permission check
router.get('/custom-permission', protect.shop, checkPermission('custom_action'), (req, res) => {
    res.json({ message: 'Custom permission granted' });
});

// Shop admin required
router.post('/shop/settings', protect.shop, requireShopAdmin, (req, res) => {
    res.json({ message: 'Shop settings updated' });
});

// Multiple middleware example
router.get('/complex-route', 
    protect.shop,                    // Require shop access
    checkPermission('reports_view'), // Require reports permission
    (req, res) => {
        res.json({ 
            message: 'Complex route accessed',
            user: req.user.user_id,
            shop: req.user.shop_id,
            permissions: req.user.permissions
        });
    }
);

app.use('/api', router);

// Start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log('Middleware protection active for all routes');
});

module.exports = app;
