# CORS Fix Guide for Next.js + Vercel Backend

## 🚨 Problem Analysis

Your CORS errors are caused by:

1. **Incorrect CORS logic** in `app.js`
2. **Missing frontend domain** in allowed origins
3. **Improper string comparison** in origin validation

## ✅ Solution Implemented

### 1. Fixed CORS Configuration

**Before (Broken):**
```javascript
// ❌ This always evaluates to true
if (!origin || origin == "http://localhost:3000" || "https://alphav-pos.vercel.app/"||"https://pos-backend-five.vercel.app'") {
```

**After (Fixed):**
```javascript
// ✅ Proper array-based origin checking
const allowedOrigins = [
    'http://localhost:3000',
    'https://alphav-pos.vercel.app',
    'https://pos-backend-five.vercel.app'
];

if (!origin || allowedOrigins.includes(origin)) {
    callback(null, true);
}
```

### 2. Environment Variables Setup

Add these to your Vercel project settings:

```bash
# Frontend URL (your Next.js app domain)
FRONTEND_URL=https://your-nextjs-app.vercel.app

# Backend URL (your current backend)
BACKEND_URL=https://pos-backend-five.vercel.app
```

### 3. Security Headers Added

- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- Proper OPTIONS preflight handling

## 🔧 Next.js Frontend Configuration

### Update your Next.js API calls:

**Before:**
```javascript
// ❌ Might cause CORS issues
fetch('https://pos-backend-five.vercel.app/api/cal_dash?query[shop_id]=SH0002&shop_id=SH0002')
```

**After:**
```javascript
// ✅ Proper configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://pos-backend-five.vercel.app';

const response = await fetch(`${API_BASE_URL}/api/cal_dash`, {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
        'x-user-id': userId,
        'x-shop-id': shopId,
        // Add other required headers
    },
    credentials: 'include', // Important for CORS with credentials
});
```

### Add to your Next.js `.env.local`:

```bash
NEXT_PUBLIC_API_URL=https://pos-backend-five.vercel.app
```

## 🚀 Deployment Steps

### 1. Update Vercel Environment Variables

In your Vercel dashboard → Settings → Environment Variables:

```bash
FRONTEND_URL=https://your-nextjs-app.vercel.app
BACKEND_URL=https://pos-backend-five.vercel.app
NODE_ENV=production
```

### 2. Deploy Backend Changes

```bash
git add .
git commit -m "Fix CORS configuration for production"
git push origin main
```

### 3. Test CORS

```bash
# Test from browser console on your frontend domain
fetch('https://pos-backend-five.vercel.app/api/health', {
    method: 'GET',
    credentials: 'include'
}).then(r => r.json()).then(console.log);
```

## 🔍 Debugging CORS Issues

### 1. Check Browser Network Tab

Look for:
- **Preflight OPTIONS requests** (should return 200)
- **CORS error messages** in console
- **Response headers** should include `Access-Control-Allow-Origin`

### 2. Backend Logs

Check Vercel function logs for:
```
❌ CORS blocked origin: https://your-domain.com
```

### 3. Test with curl

```bash
# Test preflight request
curl -X OPTIONS https://pos-backend-five.vercel.app/api/cal_dash \
  -H "Origin: https://your-frontend-domain.vercel.app" \
  -H "Access-Control-Request-Method: GET" \
  -v

# Test actual request
curl -X GET https://pos-backend-five.vercel.app/api/cal_dash \
  -H "Origin: https://your-frontend-domain.vercel.app" \
  -H "x-user-id: test-user" \
  -H "x-shop-id: SH0002" \
  -v
```

## 🛠️ Common Issues & Solutions

### Issue 1: "Access to fetch blocked by CORS policy"

**Solution:** Add your frontend domain to `allowedOrigins` array

### Issue 2: "Preflight request doesn't pass"

**Solution:** Ensure OPTIONS method is handled properly (already fixed in our code)

### Issue 3: "Credentials not allowed"

**Solution:** Make sure both frontend and backend have `credentials: true/include`

### Issue 4: "Origin null not allowed"

**Solution:** This happens with file:// protocol or missing Origin header (already handled)

## 📱 Mobile App Considerations

If you have a mobile app:

```javascript
const allowedOrigins = [
    // ... existing origins
    'capacitor://localhost',  // Capacitor apps
    'ionic://localhost',      // Ionic apps
    'http://localhost',       // React Native
    'https://localhost'       // React Native with HTTPS
];
```

## 🔒 Security Best Practices

1. **Never use `*` for origins** in production
2. **Always validate origins** against a whitelist
3. **Use HTTPS** in production
4. **Limit exposed headers** to what's necessary
5. **Set appropriate cache times** for preflight requests

## ✅ Verification Checklist

- [ ] Backend CORS configuration updated
- [ ] Frontend domain added to allowed origins
- [ ] Environment variables set in Vercel
- [ ] Preflight requests return 200
- [ ] Actual API requests work from frontend
- [ ] No CORS errors in browser console
- [ ] Authentication headers are properly sent

## 🆘 If Still Having Issues

1. **Check exact frontend domain:** Use browser dev tools to see the exact Origin header being sent
2. **Verify environment variables:** Make sure they're set correctly in Vercel
3. **Test locally first:** Ensure it works with localhost before testing production
4. **Check Vercel logs:** Look for CORS-related error messages
5. **Use browser dev tools:** Network tab shows detailed CORS information

## 📞 Quick Fix Commands

```bash
# 1. Add your frontend domain to the allowedOrigins array in app.js
# 2. Set environment variables in Vercel dashboard
# 3. Deploy changes
git add app.js
git commit -m "Fix CORS for production"
git push origin main

# 4. Test after deployment
curl -X GET https://pos-backend-five.vercel.app/api/health \
  -H "Origin: https://your-frontend.vercel.app" -v
```

Your CORS issues should now be resolved! 🎉
