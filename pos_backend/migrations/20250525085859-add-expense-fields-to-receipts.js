'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('receipts', 'expense_category', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null
    });
    
    await queryInterface.addColumn('receipts', 'expense_description', {
      type: Sequelize.TEXT,
      allowNull: true,
      defaultValue: null
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('receipts', 'expense_category');
    await queryInterface.removeColumn('receipts', 'expense_description');
  }
};
